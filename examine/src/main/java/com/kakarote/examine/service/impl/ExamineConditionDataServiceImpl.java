package com.kakarote.examine.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.examine.entity.PO.ExamineConditionData;
import com.kakarote.examine.mapper.ExamineConditionDataMapper;
import com.kakarote.examine.service.IExamineConditionDataService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审批条件扩展字段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Service
public class ExamineConditionDataServiceImpl extends BaseServiceImpl<ExamineConditionDataMapper, ExamineConditionData> implements IExamineConditionDataService {

}
