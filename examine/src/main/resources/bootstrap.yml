server:
  port: 0
spring:
  profiles:
    active: core,dev
  application:
    name: examine
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 113.45.141.212:8848
        file-extension: yaml
        prefix: examine
      discovery:
        enabled: true
        server-addr: 113.45.141.212:8848
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 113.45.141.212:8079
      datasource:
        flow:
          server-addr: 113.45.141.212:8848
          dataId: ${spring.application.name}-flow-rules
          groupId: SENTINEL_GROUP
          rule-type: flow
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}_tx_group
  config:
    type: nacos
    nacos:
      namespace:
      serverAddr: 113.45.141.212:8848
      group: SEATA_GROUP
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 113.45.141.212:8848
      namespace:
