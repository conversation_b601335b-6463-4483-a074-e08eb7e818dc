package com.kakarote.oa.entity.BO;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Getter
@Setter
public class QueryEventListBO {
    private Long startTime;
    private Long endTime;
    private List<Long> typeIds;
    private Long userId;

    @Override
    public String toString() {
        return "QueryEventListBO{" +
                "startTime=" + startTime +
                ", endTime=" + endTime +
                ", typeIds=" + typeIds +
                ", userId=" + userId +
                '}';
    }
}
