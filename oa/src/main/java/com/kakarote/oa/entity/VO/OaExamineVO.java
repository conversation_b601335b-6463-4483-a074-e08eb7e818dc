package com.kakarote.oa.entity.VO;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class OaExamineVO {

    @ApiModelProperty(value = "审批id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long examineId;

    @ApiModelProperty(value = "审批类型")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "请假类型")
    private String typeId;

    @ApiModelProperty(value = "差旅、报销总金额")
    private BigDecimal money;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "时长")
    private BigDecimal duration;

    @ApiModelProperty(value = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUserId;

    @ApiModelProperty(value = "附件批次id")
    private String batchId;

    @ApiModelProperty(value = "审核记录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long examineRecordId;

    @ApiModelProperty("审批状态")
    private Integer examineStatus;

    @Override
    public String toString() {
        return "OaExamineVO{" +
                "examineId=" + examineId +
                ", categoryId=" + categoryId +
                ", content='" + content + '\'' +
                ", remark='" + remark + '\'' +
                ", typeId='" + typeId + '\'' +
                ", money=" + money +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", duration=" + duration +
                ", createUserId=" + createUserId +
                ", batchId='" + batchId + '\'' +
                ", examineRecordId=" + examineRecordId +
                ", examineStatus=" + examineStatus +
                '}';
    }
}
