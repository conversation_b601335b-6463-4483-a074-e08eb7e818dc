package com.kakarote.oa.entity.PO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 日志关联业务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-01
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_oa_log_relation")
@ApiModel(value="OaLogRelation对象", description="日志关联业务表")
public class OaLogRelation implements Serializable {


    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "日志ID")
    private Long logId;

    @ApiModelProperty(value = "关联id")
    private Long relationId;

    @ApiModelProperty(value = "类型 1客户id 2联系人id 3商机id 4合同id")
    private Integer type;

    @ApiModelProperty(value = "状态1可用")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "客户IDs")
    @TableField(exist = false)
    private String customerIds;

    @ApiModelProperty(value = "联系人IDs")
    @TableField(exist = false)
    private String contactsIds;

    @ApiModelProperty(value = "商机IDs")
    @TableField(exist = false)
    private String businessIds;

    @ApiModelProperty(value = "合同IDs")
    @TableField(exist = false)
    private String contractIds;

}
