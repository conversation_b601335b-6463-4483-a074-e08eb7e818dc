package com.kakarote.oa.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.oa.entity.BO.LogBO;
import com.kakarote.oa.entity.PO.OaLog;
import com.kakarote.oa.entity.PO.OaLogBulletin;
import com.kakarote.oa.entity.VO.OaBusinessNumVO;
import com.kakarote.oa.entity.VO.OaLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-15
 */
public interface OaLogMapper extends BaseMapper<OaLog> {

    /**
     * 分页查询日志
     *
     * @param parse 分页
     * @param data  入参数据
     * @return com.kakarote.core.entity.BasePage<com.kakarote.oa.entity.VO.OaLogVO>
     * <AUTHOR> sir
     * @date 2021/7/31
     */
    BasePage<OaLogVO> queryLogList(BasePage<OaLogVO> parse, @Param("data") LogBO data);

    /**
     * 根据日志id集合查询数量
     *
     * @param logIds 日志id集合
     * @return java.util.Map<java.lang.Integer, java.lang.Integer>
     * <AUTHOR> sir
     * @date 2021/7/31
     */
    List<Map<Integer, Object>> queryLogCommentNum(@Param("logIds") List<Long> logIds);

    /**
     * 查询日志统计信息
     *
     * @param userId    用户
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return data
     */
    @SqlParser(filter = true)
    public JSONObject queryLogBulletin(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 查询查询日志完成情况统计
     *
     * @param object param
     * @return data
     */
    public Integer queryCompleteStats(JSONObject object);

    /**
     * 查询查询日志完成情况列表
     *
     * @param object param
     * @return data
     */
    public BasePage<JSONObject> queryCompleteOaLogList(BasePage<JSONObject> page, @Param("data") JSONObject object);

    @SqlParser(filter = true)
    public BasePage<JSONObject> queryLogBulletinByType(BasePage<JSONObject> page, @Param("data") JSONObject object);

    public BasePage<SimpleUser> queryIncompleteOaLogList(BasePage<SimpleUser> page, @Param("data") JSONObject object);

    public JSONObject queryBulletinByLog(@Param("userIds") List<Long> userIds);

    public List<OaLogBulletin> queryBulletinByLogInfo(@Param("userIds") List<Long> userIds);

    public List<JSONObject> queryLogRecordCount(@Param("typeIds") List<Long> typeIds,@Param("sortField") String sortField,@Param("order") Integer order);

    public JSONObject queryById(Long logId);

    /***
     * 查询导出数据
     *
     * @param object 入参
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR> sir
     * @date 2021/11/20
     */
    List<JSONObject> queryExportList(JSONObject object);

    public List<JSONObject> queryCommentList(@Param("typeId") Integer typeId);

    public OaBusinessNumVO queryOaBusinessNum(Map<String, Object> map);
}
