package com.kakarote.authorization.config.smscode;

import com.kakarote.authorization.common.AuthException;
import com.kakarote.authorization.common.AuthorizationCodeEnum;
import com.kakarote.authorization.entity.AuthorizationUser;
import com.kakarote.authorization.entity.AuthorizationUserInfo;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.redis.Redis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;
import java.util.Objects;

@Slf4j
public class SmsCodeAuthenticationProvider implements AuthenticationProvider {


    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private Redis redis;


    /**
     * 进行身份认证的逻辑
     *
     * @param authentication
     * @return
     * @throws AuthenticationException
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SmsCodeAuthenticationToken authenticationToken = (SmsCodeAuthenticationToken) authentication;
        String smscode = authenticationToken.getSmscode();
        boolean b = Objects.equals(smscode, redis.get(AdminCacheKey.SMS_CACHE_KEY + authenticationToken.getPrincipal()));
        if (!b){
            throw new AuthException(AuthorizationCodeEnum.AUTHORIZATION_USER_SMS_CODE_ERROR);
        }
        UserDetails userDetails = userDetailsService.loadUserByUsername((String) authenticationToken.getPrincipal());
        AuthorizationUser user;
        if (userDetails instanceof AuthorizationUser) {
            user = (AuthorizationUser) userDetails;
        } else {
            log.debug("Authentication failed: no credentials provided");
            throw new AuthException(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_ERR);
        }
        SmsCodeAuthenticationToken authenticationResult = new SmsCodeAuthenticationToken(user,smscode, user.getAuthorities());
        List<UserInfo> userInfoList = user.getUserInfoList();
        if (userInfoList.size() == 0) {
            throw new AuthException(AuthorizationCodeEnum.AUTHORIZATION_USER_DOES_NOT_EXIST);
        }
        AuthorizationUserInfo userDetailsInfo = new AuthorizationUserInfo();
        userInfoList.forEach(userInfo -> {
            AuthorizationUser authorizationUser = AuthorizationUser.toAuthorizationUser(userInfo);
            userDetailsInfo.addAuthorizationUser(authorizationUser);
        });
        authenticationResult.setDetails(userDetailsInfo);
        return authenticationResult;
    }

    /**
     * 根据该方法判断 AuthenticationManager选择哪个 Provider进行认证处理
     *
     * @param authentication
     * @return
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return SmsCodeAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
