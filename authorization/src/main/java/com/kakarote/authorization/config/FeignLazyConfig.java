package com.kakarote.authorization.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * FeignClient延迟加载配置
 * 解决循环依赖问题
 * 
 * <AUTHOR>
 */
@Configuration
@Lazy
@EnableFeignClients(basePackages = "com.kakarote.authorization.service")
public class FeignLazyConfig {
    
}
