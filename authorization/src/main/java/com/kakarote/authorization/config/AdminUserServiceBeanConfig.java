package com.kakarote.authorization.config;

import com.kakarote.authorization.service.AdminUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;

/**
 * AdminUserService Bean配置
 * 通过编程方式创建FeignClient，避免循环依赖
 * 
 * <AUTHOR>
 */
@Configuration
public class AdminUserServiceBeanConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    @Primary
    @Lazy
    public AdminUserService adminUserService() {
        return new FeignClientBuilder(applicationContext)
                .forType(AdminUserService.class, "admin")
                .build();
    }
}
