package com.kakarote.authorization.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * 循环依赖解决器
 * 用于处理FeignClient与Spring Security之间的循环依赖
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CircularDependencyResolver implements BeanPostProcessor, Ordered {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        // 在Bean初始化之前进行处理
        if (beanName != null && beanName.contains("AdminUserService")) {
            log.debug("Processing AdminUserService bean: {}", beanName);
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 在Bean初始化之后进行处理
        if (beanName != null && beanName.contains("AdminUserService")) {
            log.debug("AdminUserService bean initialized: {}", beanName);
        }
        return bean;
    }

    @Override
    public int getOrder() {
        // 设置较高的优先级，确保在其他BeanPostProcessor之前执行
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }
}
