package com.kakarote.authorization.config;

import com.kakarote.authorization.service.AdminUserService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * AdminUserService配置类
 * 用于解决循环依赖问题
 * 
 * <AUTHOR>
 */
@Configuration
public class AdminUserServiceConfig {

    /**
     * 创建一个延迟加载的AdminUserService代理
     * 只有在实际需要时才会创建FeignClient
     */
    @Bean
    @Lazy
    @ConditionalOnMissingBean(AdminUserService.class)
    public AdminUserService adminUserServiceProxy() {
        // 这里返回null，让Spring使用FeignClient创建的代理
        return null;
    }
}
