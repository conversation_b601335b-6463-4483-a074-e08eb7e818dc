package com.kakarote.authorization.service;

import com.kakarote.authorization.entity.AdminUserStatusBO;
import com.kakarote.core.common.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 向admin模块请求企业信息
 */
@FeignClient(name = "admin", contextId = "adminUser", primary = false)
public interface AdminUserService {

    /**
     * 通过用户名查询用户
     *
     * @param username 用户名
     * @return 结果信息
     */
    @PostMapping(value = "/adminUser/findByUsername")
    Result<List<Map<String, Object>>> findByUsername(@RequestParam("username") String username);

    /**
     * 通过用户ID查询所属角色
     *
     * @param userId 用户ID
     * @return data
     */
    @RequestMapping(value = "/adminUser/queryUserRoleIds")
    Result<List<Long>> queryUserRoleIds(@RequestParam("userId") Long userId);

    /**
     * 设置用户状态
     * @param adminUserStatusBO:用户状态修改BO
     * @return com.kakarote.core.common.Result
     */
    @PostMapping("/adminUser/setUserStatus")
    @ApiOperation("禁用启用")
    Result setUserStatus(@RequestBody AdminUserStatusBO adminUserStatusBO);


    /**
     * 通过用户ID查询没有权限的菜单
     *
     * @param userId
     * @return 结果信息
     * @date 2020/8/20 15:25
     **/
    @PostMapping(value = "/adminRole/queryNoAuthMenu")
    Result<List<String>> queryNoAuthMenu(@RequestParam("userId") Long userId);
}
