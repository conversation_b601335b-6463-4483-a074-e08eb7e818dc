package com.kakarote.authorization.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.kakarote.authorization.common.AuthException;
import com.kakarote.authorization.common.AuthorizationCodeEnum;
import com.kakarote.authorization.common.AuthorizationConst;
import com.kakarote.authorization.common.LoginType;
import com.kakarote.authorization.config.smscode.SmsCodeAuthenticationToken;
import com.kakarote.authorization.entity.AdminUserStatusBO;
import com.kakarote.authorization.entity.AuthorizationUser;
import com.kakarote.authorization.entity.AuthorizationUserInfo;
import com.kakarote.authorization.entity.VO.LoginVO;
import com.kakarote.authorization.mapper.FindUserMapper;
import com.kakarote.authorization.service.AdminUserProxyService;
import com.kakarote.authorization.service.AdminUserService;
import com.kakarote.authorization.service.LoginService;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService, LoginService {

    @Autowired
    private ObjectFactory<AdminUserService> adminUserServiceFactory;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private FindUserMapper findUserMapper;

    @Autowired
    private Redis redis;
    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    private AdminUserProxyService adminUserProxyService;


    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        List<Map<String, Object>> userInfoList = findUserMapper.finbyUserName( username);
        if (!userInfoList.isEmpty()) {
            return new AuthorizationUser().setUserInfoList(userInfoList);
        }
        throw new UsernameNotFoundException(null);
    }

    /**
     * 登录方法的处理
     *
     * @param user    用户对象
     * @param request
     * @return Result
     */
    @Override
    public Result login(AuthorizationUser user, HttpServletResponse response, HttpServletRequest request) {
        String token = IdUtil.simpleUUID();
        UserInfo userInfo = user.toUserInfo();
        if (userInfo.getStatus() == 0) {
            throw new CrmException(AuthorizationCodeEnum.AUTHORIZATION_USER_DISABLE_ERROR);
        }
        //人资系统
        int hrmIndex = 5;
        if (ObjectUtil.equal(hrmIndex, user.getType())) {
            AdminConfig hrm = adminService.queryFirstConfigByName("hrm").getData();
            if (ObjectUtil.isNull(hrm) || ObjectUtil.equal(0, hrm.getStatus())) {
                throw new CrmException(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_MODULE_NOT_EXIST, "人力资源管理系统");
            }
        }
        // 财务系统
        int financeIndex = 6;
        if (ObjectUtil.equal(financeIndex, user.getType())) {
            AdminConfig finance = adminService.queryFirstConfigByName("finance").getData();
            if (ObjectUtil.isNull(finance) || ObjectUtil.equal(0, finance.getStatus())) {
                throw new CrmException(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_MODULE_NOT_EXIST, "财务管理系统");
            }
        }
        userInfo.setRoles(adminUserServiceFactory.getObject().queryUserRoleIds(userInfo.getUserId()).getData());
        UserUtil.userToken(token, userInfo, user.getType());
        // 未激活状态码
        int userStatus = 2;
        if (userInfo.getStatus() == userStatus) {
            adminUserServiceFactory.getObject().setUserStatus(AdminUserStatusBO.builder().status(1).ids(Collections.singletonList(userInfo.getUserId())).build());
        }
        redis.del(AdminCacheKey.USER_AUTH_CACHE_KET + userInfo.getUserId());
        redis.del(CrmCacheKey.CRM_AUTH_USER_CACHE_KEY + userInfo.getUserId());
        redis.del(AuthorizationConst.NO_AUTH_MENU_CACHE_KEY + userInfo.getUserId());
        return Result.ok(new LoginVO().setAdminToken(token));
    }

    /**
     * 登录方法的处理
     *
     * @param user 用户对象
     * @return Result
     */
    @Override
    public Result doLogin(AuthorizationUser user, HttpServletResponse response, HttpServletRequest request) {
        LoginType loginType = LoginType.valueOf(user.getLoginType());
        if (loginType.equals(LoginType.PASSWORD) || loginType.equals(LoginType.SMS_CODE)) {
            String key = AdminCacheKey.PASSWORD_ERROR_CACHE_KEY + user.getUsername().trim();
            Integer errorNum = redis.get(key);
            // 密码输入错误次数上限
            if (errorNum != null && errorNum >= AuthorizationConst.PWD_ERROR_NUM) {
                int second = Optional.ofNullable(redis.ttl(key)).orElse(0L).intValue();
                if (second > 0) {
                    String errorTimeDesc = this.getErrorTimeDesc(second);
                    return Result.error(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_PASSWORD_TO_MANY_ERROR, "密码错误次数过多，请在" + errorTimeDesc + "后重试！");
                }
            }
        }
        try {
            AbstractAuthenticationToken authenticationToken;
            if (loginType == LoginType.SMS_CODE) {
                user.setSmscode(decodePassWord(user.getSmscode()));
                authenticationToken = new SmsCodeAuthenticationToken(user.getUsername().trim(), user.getSmscode().trim());
            } else {
                user.setPassword(decodePassWord(user.getPassword()));
                authenticationToken = new UsernamePasswordAuthenticationToken(user.getUsername().trim(), user.getPassword().trim());
            }
            Authentication authentication = authenticationManager.authenticate(authenticationToken);
            AuthorizationUserInfo userInfo = (AuthorizationUserInfo) authentication.getDetails();
            if (userInfo.getAuthorizationUserList().size() == 0) {
                return this.handleLoginPassWordToManyError(user.getUsername().trim());
            }
            return login(userInfo.getAuthorizationUserList().get(0).setType(user.getType()), response, request);
        } catch (AuthException e) {
            return Result.error(e.getResultCode());
        } catch (BadCredentialsException e) {
            return this.handleLoginPassWordToManyError(user.getUsername().trim());
        }

    }

    private String decodePassWord(String data) {
        data = data.trim();

        RSA rsa = SecureUtil.rsa(AuthorizationConst.USER_PRIVATE_KEY, null);
        try {
            String str = rsa.decryptStr(data, KeyType.PrivateKey);
            long currentTimeMillis = System.currentTimeMillis();
            //当前时间戳的长度
            int length = Long.toString(currentTimeMillis).length();
            if (str.length() > length) {
                String time = StrUtil.sub(str, str.length() - length, str.length());
                if (NumberUtil.isNumber(time) && Math.abs(NumberUtil.parseLong(time) - currentTimeMillis) < 3000000) {
                    return StrUtil.sub(str, 0, str.length() - length);
                }
            }
            //StrUtil.subWithLength()
        } catch (Exception ex) {
            log.error("解密数据失败", ex);
        }

        return data;
    }

    /**
     * 时间描述
     *
     * @param second
     * @return java.lang.String
     * @date 2020/11/9 16:57
     **/
    private String getErrorTimeDesc(Integer second) {
        String errorTimeDesc;
        // 1-5分钟的秒数
        int five = 300;
        int four = 240;
        int three = 180;
        int two = 120;
        int one = 60;
        if (Arrays.asList(five, four, three, two, one).contains(second)) {
            errorTimeDesc = second / 60 + "分";
        } else if (second < one) {
            errorTimeDesc = second + "秒";
        } else {
            errorTimeDesc = second / 60 + "分" + second % 60 + "秒";
        }
        return errorTimeDesc;
    }

    /**
     * 密码失败次数处理
     *
     * @param userName
     * @return com.kakarote.core.common.Result
     * @date 2020/11/9 15:42
     **/
    private Result handleLoginPassWordToManyError(String userName) {
        String key = AdminCacheKey.PASSWORD_ERROR_CACHE_KEY + userName;
        Integer errorNum = redis.get(key);
        if (errorNum == null) {
            errorNum = 0;
        }
        redis.setex(AdminCacheKey.PASSWORD_ERROR_CACHE_KEY + userName, AuthorizationConst.ENTRY_INHIBITED_TIME, errorNum + 1);
        return Result.error(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_NO_USER);
    }

    @Override
    public Result permission(String authentication, String url, String method) {
        UserInfo userInfo = redis.get(authentication);
        if (userInfo == null) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NOT_LOGIN);
        }
        Long userId = userInfo.getUserId();
        String key = AuthorizationConst.NO_AUTH_MENU_CACHE_KEY + userId;
        List<String> noAuthMenuUrls = redis.get(key);
        if (noAuthMenuUrls == null) {
            noAuthMenuUrls = adminUserServiceFactory.getObject().queryNoAuthMenu(userId).getData();
            redis.setex(key, 1800, noAuthMenuUrls);
        }
        boolean permission = isHasPermission(noAuthMenuUrls, url);
        return permission ? Result.ok() : Result.noAuth();
    }

    @Override
    public Result logout(String authentication) {
        Object data = redis.get(authentication);
        if (data instanceof UserInfo) {
            UserInfo userInfo = (UserInfo) data;
            redis.del(authentication);
            redis.del(AdminCacheKey.USER_AUTH_CACHE_KET + userInfo.getUserId());
            redis.del(CrmCacheKey.CRM_AUTH_USER_CACHE_KEY + userInfo.getUserId());
            redis.del(AuthorizationConst.NO_AUTH_MENU_CACHE_KEY + userInfo.getUserId());
        }
        return Result.ok();
    }


    /**
     * 判断有无权限访问
     *
     * @param noAuthMenuUrls
     * @param url
     * @return boolean
     * @date 2020/8/21 13:35
     **/
    private boolean isHasPermission(List<String> noAuthMenuUrls, String url) {
        //用户信息丢失 | 错误
        if (noAuthMenuUrls == null) {
            return false;
        }
        //管理员
        if (noAuthMenuUrls.size() == 0) {
            return true;
        }
        //没有任何权限
        String equesAuth = "/*/**";
        if (noAuthMenuUrls.size() == 1 && equesAuth.equals(noAuthMenuUrls.get(0))) {
            return false;
        }
        boolean permission = true;
        for (String noAuthMenuUrl : noAuthMenuUrls) {
            if (noAuthMenuUrl.contains("*")) {
                if (noAuthMenuUrl.contains(",")) {
                    boolean isNoAuth = false;
                    String splitIndex = ",";
                    for (String noAuthUrl : noAuthMenuUrl.split(splitIndex)) {
                        if (url.startsWith(noAuthUrl.replace("*", ""))) {
                            isNoAuth = true;
                            break;
                        }
                    }
                    if (isNoAuth) {
                        permission = false;
                        break;
                    }
                } else {
                    if (url.startsWith(noAuthMenuUrl.replace("*", ""))) {
                        permission = false;
                        break;
                    }
                }
            } else {
                if (noAuthMenuUrl.contains(",")) {
                    if (Arrays.asList(noAuthMenuUrl.split(",")).contains(url)) {
                        permission = false;
                        break;
                    }
                } else {
                    if (noAuthMenuUrl.equals(url)) {
                        permission = false;
                        break;
                    }
                }
            }
        }
        return permission;
    }

    @Override
    public String getLoginQrCode(HttpServletRequest request) {
        //由sentinel进行登录次数拦截，此处不做限制
        String token = IdUtil.simpleUUID();
        //设置有效期为分钟
        redis.setex(token, 180, "");
        return token;
    }

    @Override
    public LoginVO getLoginQrInfo(String token) {
        String data = redis.get(token);
        if (null == data) {
            throw new CrmException(AuthorizationCodeEnum.QRCODE_INFO_IS_NULL);
        } else if ("".equals(data)) {
            throw new CrmException(AuthorizationCodeEnum.QRCODE_DID_NOT_CONFIRM);
        }
        return new LoginVO().setAdminToken(data);
    }

    @Override
    public void setQrInfo(String token) {
        UserInfo user = UserUtil.getUser();
        String uuid = IdUtil.simpleUUID();
        UserUtil.userToken(uuid, user, 1);
        redis.setex(token, 60, uuid);
    }
}
