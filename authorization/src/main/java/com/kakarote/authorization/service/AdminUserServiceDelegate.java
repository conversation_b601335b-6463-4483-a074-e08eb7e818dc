package com.kakarote.authorization.service;

import com.kakarote.authorization.entity.AdminUserStatusBO;
import com.kakarote.core.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AdminUserService代理类，用于解决循环依赖问题
 * 通过ApplicationContext延迟获取AdminUserService Bean
 * 
 * <AUTHOR>
 */
@Service
public class AdminUserServiceDelegate {

    @Autowired
    private ApplicationContext applicationContext;

    private AdminUserService adminUserService;

    /**
     * 延迟获取AdminUserService实例
     */
    private AdminUserService getAdminUserService() {
        if (adminUserService == null) {
            adminUserService = applicationContext.getBean(AdminUserService.class);
        }
        return adminUserService;
    }

    /**
     * 通过用户ID查询所属角色
     */
    public Result<List<Long>> queryUserRoleIds(Long userId) {
        return getAdminUserService().queryUserRoleIds(userId);
    }

    /**
     * 设置用户状态
     */
    public Result setUserStatus(AdminUserStatusBO adminUserStatusBO) {
        return getAdminUserService().setUserStatus(adminUserStatusBO);
    }

    /**
     * 查询用户无权限菜单
     */
    public Result<List<String>> queryNoAuthMenu(Long userId) {
        return getAdminUserService().queryNoAuthMenu(userId);
    }
}
