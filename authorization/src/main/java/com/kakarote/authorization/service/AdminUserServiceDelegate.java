package com.kakarote.authorization.service;

import com.kakarote.authorization.entity.AdminUserStatusBO;
import com.kakarote.core.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * AdminUserService代理类，用于解决循环依赖问题
 * 通过ApplicationContext延迟获取AdminUserService Bean
 * 
 * <AUTHOR>
 */
@Service
public class AdminUserServiceDelegate {

    @Autowired
    private ApplicationContext applicationContext;

    private AdminUserService adminUserService;
    private volatile boolean initialized = false;

    /**
     * Spring容器初始化完成后再获取AdminUserService
     * 避免循环依赖
     */
    @PostConstruct
    public void init() {
        // 延迟到PostConstruct阶段再初始化，此时Spring容器已经基本完成初始化
        try {
            Thread.sleep(100); // 稍微延迟一下，确保所有Bean都已创建
            adminUserService = applicationContext.getBean(AdminUserService.class);
            initialized = true;
        } catch (Exception e) {
            // 如果获取失败，在实际调用时再尝试
            initialized = false;
        }
    }

    /**
     * 延迟获取AdminUserService实例
     */
    private AdminUserService getAdminUserService() {
        if (!initialized || adminUserService == null) {
            try {
                adminUserService = applicationContext.getBean(AdminUserService.class);
                initialized = true;
            } catch (Exception e) {
                throw new RuntimeException("无法获取AdminUserService实例", e);
            }
        }
        return adminUserService;
    }

    /**
     * 通过用户ID查询所属角色
     */
    public Result<List<Long>> queryUserRoleIds(Long userId) {
        return getAdminUserService().queryUserRoleIds(userId);
    }

    /**
     * 设置用户状态
     */
    public Result setUserStatus(AdminUserStatusBO adminUserStatusBO) {
        return getAdminUserService().setUserStatus(adminUserStatusBO);
    }

    /**
     * 查询用户无权限菜单
     */
    public Result<List<String>> queryNoAuthMenu(Long userId) {
        return getAdminUserService().queryNoAuthMenu(userId);
    }
}
