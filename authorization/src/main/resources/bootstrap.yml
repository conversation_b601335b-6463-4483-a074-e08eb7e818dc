server:
  port: 0
spring:
  application:
    name: authorization
  profiles:
    active: core,dev
  main:
    allow-circular-references: true
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 113.45.141.212:8848
        file-extension: yaml
        prefix: authorization
      discovery:
        enabled: true
        server-addr: 113.45.141.212:8848
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 113.45.141.212:8079
      datasource:
        ds1:
          nacos:
            server-addr: 113.45.141.212:8848
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow