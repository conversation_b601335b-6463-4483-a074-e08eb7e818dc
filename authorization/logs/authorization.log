2025-06-19 13:56:44 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 13:56:44 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 13:56:44 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 13:56:44 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 13:56:44 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 13:56:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 13:56:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 13:56:45 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 13:56:45 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 13:56:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 13:56:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 13:56:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10ms. Found 0 Redis repository interfaces. 
2025-06-19 13:56:46 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 13:56:46 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=9641ff9c-06a3-3a5a-b70d-91cdcf91d96a 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$523aa5aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d140eb22] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:47 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 13:56:48 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 13:56:48 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 13:56:48 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 13:56:49 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 13:56:49 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1dbeedff, org.springframework.security.web.context.SecurityContextPersistenceFilter@768f4b42, org.springframework.security.web.header.HeaderWriterFilter@1c8f71a7, com.kakarote.authorization.common.AuthenticationTokenFilter@62b786dc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@63b4b9c6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f3f57ff, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ba1cdbe, org.springframework.security.web.access.ExceptionTranslationFilter@e36bc01, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@636fccb0] 
2025-06-19 13:56:49 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 13:56:49 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 13:56:49 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 13:56:49 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 13:56:49 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 13:56:49 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 13:56:49 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 13:56:51 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 13:56:51 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 13:56:51 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 13:56:51 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 7.377 seconds (JVM running for 8.852) 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 13:56:51 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 13:56:51 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 13:56:51 authorization [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean - For 'admin' URL not provided. Will try picking an instance via load-balancing. 
2025-06-19 13:56:51 authorization [main] ERROR org.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@6bedbc4d] to prepare test instance [com.kakarote.authorization.AuthorizationAppliactionTest@543588e6] 
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.kakarote.authorization.AuthorizationAppliactionTest': Unsatisfied dependency expressed through field 'adminUserService'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:392)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:119)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener.prepareTestInstance(SpringBootDependencyInjectionTestExecutionListener.java:43)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.beforeSingletonCreation(DefaultSingletonBeanRegistry.java:355)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:227)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at com.alibaba.cloud.sentinel.feign.SentinelFeign$Builder$1.create(SentinelFeign.java:84)
	at feign.ReflectiveFeign.newInstance(ReflectiveFeign.java:64)
	at feign.Feign$Builder.target(Feign.java:269)
	at org.springframework.cloud.openfeign.HystrixTargeter.target(HystrixTargeter.java:38)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.loadBalance(FeignClientFactoryBean.java:352)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:388)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:361)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:246)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1230)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1172)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 31 common frames omitted
2025-06-19 13:56:51 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 13:56:51 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 13:56:51 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 14:18:20 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 14:18:21 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 14:18:21 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 14:18:21 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 14:18:21 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:18:21 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:18:21 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:18:21 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 14:18:21 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 14:18:22 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 14:18:22 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 14:18:22 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14ms. Found 0 Redis repository interfaces. 
2025-06-19 14:18:22 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 14:18:22 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=9641ff9c-06a3-3a5a-b70d-91cdcf91d96a 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e280cd34] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$618712ac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:23 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:18:24 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 14:18:24 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 14:18:24 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 14:18:24 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 14:18:24 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4c8d45cf, org.springframework.security.web.context.SecurityContextPersistenceFilter@1fbd5e0, org.springframework.security.web.header.HeaderWriterFilter@4eb1943b, com.kakarote.authorization.common.AuthenticationTokenFilter@fde487b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@135064ea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2bcb1414, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4cc4d454, org.springframework.security.web.access.ExceptionTranslationFilter@521a506c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3ba97962] 
2025-06-19 14:18:25 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 14:18:25 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 14:18:25 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 14:18:25 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 14:18:25 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 14:18:25 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 14:18:25 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 14:18:26 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 14:18:26 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 14:18:26 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 14:18:26 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 6.753 seconds (JVM running for 7.805) 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 14:18:26 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:18:26 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 14:18:26 authorization [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean - For 'admin' URL not provided. Will try picking an instance via load-balancing. 
2025-06-19 14:18:26 authorization [main] ERROR org.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@581ac8a8] to prepare test instance [com.kakarote.authorization.AuthorizationAppliactionTest@53ce1329] 
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.kakarote.authorization.AuthorizationAppliactionTest': Unsatisfied dependency expressed through field 'adminUserService'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:392)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:119)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener.prepareTestInstance(SpringBootDependencyInjectionTestExecutionListener.java:43)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.beforeSingletonCreation(DefaultSingletonBeanRegistry.java:355)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:227)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at com.alibaba.cloud.sentinel.feign.SentinelFeign$Builder$1.create(SentinelFeign.java:84)
	at feign.ReflectiveFeign.newInstance(ReflectiveFeign.java:64)
	at feign.Feign$Builder.target(Feign.java:269)
	at org.springframework.cloud.openfeign.HystrixTargeter.target(HystrixTargeter.java:38)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.loadBalance(FeignClientFactoryBean.java:352)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:388)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:361)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:246)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1230)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1172)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 31 common frames omitted
2025-06-19 14:18:26 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 14:18:26 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 14:18:26 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 14:21:30 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 14:21:31 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 14:21:31 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 14:21:31 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 14:21:31 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:21:31 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:21:31 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 14:21:31 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 14:21:31 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 14:21:32 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 14:21:32 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 14:21:32 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15ms. Found 0 Redis repository interfaces. 
2025-06-19 14:21:32 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 14:21:32 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=9641ff9c-06a3-3a5a-b70d-91cdcf91d96a 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d1e61c4e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$50ec61c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:33 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 14:21:34 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 14:21:34 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 14:21:35 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 14:21:35 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 14:21:35 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ffe3d42, org.springframework.security.web.context.SecurityContextPersistenceFilter@7aa15a80, org.springframework.security.web.header.HeaderWriterFilter@7a3c99f1, com.kakarote.authorization.common.AuthenticationTokenFilter@6cd7f381, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c4ca87c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c31e410, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7df3da0b, org.springframework.security.web.access.ExceptionTranslationFilter@2fa879ed, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6be50b35] 
2025-06-19 14:21:35 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 14:21:35 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 14:21:35 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 14:21:35 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 14:21:35 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 14:21:35 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 14:21:35 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 14:21:37 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 14:21:37 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 14:21:37 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 14:21:37 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 7.015 seconds (JVM running for 8.534) 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 14:21:37 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 14:21:37 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 14:21:37 authorization [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean - For 'admin' URL not provided. Will try picking an instance via load-balancing. 
2025-06-19 14:21:37 authorization [main] ERROR org.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@505fc5a4] to prepare test instance [com.kakarote.authorization.AuthorizationAppliactionTest@d9345cd] 
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.kakarote.authorization.AuthorizationAppliactionTest': Unsatisfied dependency expressed through field 'adminUserService'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:392)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:119)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener.prepareTestInstance(SpringBootDependencyInjectionTestExecutionListener.java:43)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.beforeSingletonCreation(DefaultSingletonBeanRegistry.java:355)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:227)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at com.alibaba.cloud.sentinel.feign.SentinelFeign$Builder$1.create(SentinelFeign.java:84)
	at feign.ReflectiveFeign.newInstance(ReflectiveFeign.java:64)
	at feign.Feign$Builder.target(Feign.java:269)
	at org.springframework.cloud.openfeign.HystrixTargeter.target(HystrixTargeter.java:38)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.loadBalance(FeignClientFactoryBean.java:352)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:388)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:361)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:246)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1230)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1172)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 40 common frames omitted
2025-06-19 14:21:37 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 14:21:37 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 14:21:37 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 15:40:40 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 15:40:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 15:40:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 15:40:41 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 15:40:41 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:40:41 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:40:41 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:40:41 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 15:40:41 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 15:40:42 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 15:40:42 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 15:40:43 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24ms. Found 0 Redis repository interfaces. 
2025-06-19 15:40:43 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 15:40:43 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=35ba904d-4741-34e4-9f40-6397348ccbeb 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$619d2cdf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e0a37257] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:44 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:40:45 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 15:40:45 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 15:40:45 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 15:40:46 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:40:46 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@35b3c1f6, org.springframework.security.web.context.SecurityContextPersistenceFilter@bcfe29c, org.springframework.security.web.header.HeaderWriterFilter@6c66ddab, com.kakarote.authorization.common.AuthenticationTokenFilter@495da9a7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5bd3ca3c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@351d93bd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2beee3e8, org.springframework.security.web.access.ExceptionTranslationFilter@768f4b42, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2493eec6] 
2025-06-19 15:40:46 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:40:46 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:40:46 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:40:46 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:40:46 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:40:46 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 15:40:46 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:40:48 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 15:40:48 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 15:40:48 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:40:48 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 8.405 seconds (JVM running for 9.549) 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 15:40:48 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:40:48 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:40:48 authorization [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean - For 'admin' URL not provided. Will try picking an instance via load-balancing. 
2025-06-19 15:40:48 authorization [main] ERROR org.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4d5b6aac] to prepare test instance [com.kakarote.authorization.AuthorizationAppliactionTest@6e0f5f7f] 
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.kakarote.authorization.AuthorizationAppliactionTest': Unsatisfied dependency expressed through field 'adminUserService'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:392)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:119)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener.prepareTestInstance(SpringBootDependencyInjectionTestExecutionListener.java:43)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
Caused by: org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.beforeSingletonCreation(DefaultSingletonBeanRegistry.java:355)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:227)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at com.alibaba.cloud.sentinel.feign.SentinelFeign$Builder$1.create(SentinelFeign.java:84)
	at feign.ReflectiveFeign.newInstance(ReflectiveFeign.java:64)
	at feign.Feign$Builder.target(Feign.java:269)
	at org.springframework.cloud.openfeign.HystrixTargeter.target(HystrixTargeter.java:38)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.loadBalance(FeignClientFactoryBean.java:352)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:388)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:361)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:246)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1230)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1172)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 31 common frames omitted
2025-06-19 15:40:48 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:40:48 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 15:40:48 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 15:50:05 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 15:50:06 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 15:50:06 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 15:50:06 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 15:50:06 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:50:06 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:50:06 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:50:06 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 15:50:06 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 15:50:08 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 15:50:08 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 15:50:08 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14ms. Found 0 Redis repository interfaces. 
2025-06-19 15:50:08 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 15:50:08 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=ca5d127b-1f4a-31a9-b75b-c698002c7149 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$84deb55a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3e4fad2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:09 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:50:10 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 15:50:11 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 15:50:11 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 15:50:11 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:50:11 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9825465, org.springframework.security.web.context.SecurityContextPersistenceFilter@2553dcc0, org.springframework.security.web.header.HeaderWriterFilter@603b9d4b, com.kakarote.authorization.common.AuthenticationTokenFilter@36cf6377, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@746e534, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6a5c2d2d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2befb16f, org.springframework.security.web.access.ExceptionTranslationFilter@593f7d2e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4205d5d0] 
2025-06-19 15:50:11 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:50:11 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:50:11 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:50:11 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:50:12 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:50:12 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 15:50:12 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:50:13 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 15:50:13 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 15:50:13 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 8.098 seconds (JVM running for 9.377) 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 15:50:13 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 开始测试 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - adminUserService class: class com.kakarote.authorization.service.AdminUserService$MockitoMock$2082164808 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 结束测试 
2025-06-19 15:50:13 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 开始测试 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - adminUserService class: class com.kakarote.authorization.service.AdminUserService$MockitoMock$2082164808 
2025-06-19 15:50:13 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 结束测试 
2025-06-19 15:50:13 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:50:13 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 15:50:13 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 15:58:45 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 15:58:45 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 15:58:45 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 15:58:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 15:58:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:58:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:58:45 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 15:58:45 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 15:58:45 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 15:58:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 15:58:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 15:58:46 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8ms. Found 0 Redis repository interfaces. 
2025-06-19 15:58:46 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 15:58:47 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=a12d9c07-9e57-3a47-bb72-21e070c0203a 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ffd6e5e1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7edd2b59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:48 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 15:58:49 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 15:58:50 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 15:58:50 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 15:58:50 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:58:50 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ffdd510, org.springframework.security.web.context.SecurityContextPersistenceFilter@3d50a3d9, org.springframework.security.web.header.HeaderWriterFilter@8dbf0f2, com.kakarote.authorization.common.AuthenticationTokenFilter@8c18bde, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5111f814, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e91cf69, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6719f206, org.springframework.security.web.access.ExceptionTranslationFilter@7b1559f1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@76105ac0] 
2025-06-19 15:58:50 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:58:50 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:58:50 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 15:58:50 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 15:58:50 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:58:50 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 15:58:50 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 15:58:52 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 15:58:52 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 15:58:52 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 7.779 seconds (JVM running for 8.905) 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 15:58:52 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 开始测试 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - adminUserService class: class com.kakarote.authorization.service.AdminUserService$MockitoMock$208705059 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 结束测试 
2025-06-19 15:58:52 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 开始测试 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - adminUserService class: class com.kakarote.authorization.service.AdminUserService$MockitoMock$208705059 
2025-06-19 15:58:52 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - 结束测试 
2025-06-19 15:58:52 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 15:58:52 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 15:58:52 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
2025-06-19 16:33:33 authorization [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.6.Final 
2025-06-19 16:33:34 authorization [main] INFO  com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config 
2025-06-19 16:33:34 authorization [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0 
2025-06-19 16:33:34 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization] & group[DEFAULT_GROUP] 
2025-06-19 16:33:34 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization.yaml] & group[DEFAULT_GROUP] 
2025-06-19 16:33:34 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-core.yaml] & group[DEFAULT_GROUP] 
2025-06-19 16:33:34 authorization [main] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[authorization-dev.yaml] & group[DEFAULT_GROUP] 
2025-06-19 16:33:34 authorization [main] INFO  org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-authorization-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization-core.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-authorization,DEFAULT_GROUP'}] 
2025-06-19 16:33:34 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - The following profiles are active: core,dev 
2025-06-19 16:33:35 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode! 
2025-06-19 16:33:35 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-19 16:33:35 authorization [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13ms. Found 0 Redis repository interfaces. 
2025-06-19 16:33:35 authorization [main] WARN  org.springframework.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format. 
2025-06-19 16:33:35 authorization [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=5b8fc5f7-14ab-3742-8939-2dd8bfc73241 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1f7604c2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9e7c4a3a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:36 authorization [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2025-06-19 16:33:38 authorization [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource 
2025-06-19 16:33:38 authorization [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited 
2025-06-19 16:33:38 authorization [main] INFO  com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis 
2025-06-19 16:33:38 authorization [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 16:33:39 authorization [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7157413e, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ccf6114, org.springframework.security.web.header.HeaderWriterFilter@b849fa6, com.kakarote.authorization.common.AuthenticationTokenFilter@3245efdb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@670c171c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46d69ca4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2f0e7fa8, org.springframework.security.web.access.ExceptionTranslationFilter@3909a854, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2cff03cf] 
2025-06-19 16:33:39 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 16:33:39 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 16:33:39 authorization [main] WARN  com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources. 
2025-06-19 16:33:39 authorization [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath. 
2025-06-19 16:33:39 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 16:33:39 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null 
2025-06-19 16:33:39 authorization [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null 
2025-06-19 16:33:40 authorization [main] WARN  org.springframework.cloud.security.oauth2.SpringCloudSecurityAutoConfiguration - All Spring Cloud Security modules and starters are deprecated. They will be moved to individual projects in the next major release. 
2025-06-19 16:33:40 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-flow-rules+SENTINEL_GROUP 
2025-06-19 16:33:40 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-flow-rules, group=SENTINEL_GROUP, cnt=1 
2025-06-19 16:33:41 authorization [main] WARN  com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter - converter can not convert rules because source is empty 
2025-06-19 16:33:41 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 16:33:41 authorization [main] INFO  com.kakarote.authorization.AuthorizationAppliactionTest - Started AuthorizationAppliactionTest in 7.937 seconds (JVM running for 9.16) 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization+DEFAULT_GROUP 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization, group=DEFAULT_GROUP, cnt=1 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-core.yaml+DEFAULT_GROUP 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-core.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization-dev.yaml+DEFAULT_GROUP 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization-dev.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-113.45.141.212_8848] [subscribe] authorization.yaml+DEFAULT_GROUP 
2025-06-19 16:33:41 authorization [main] INFO  com.alibaba.nacos.client.config.impl.CacheData - [fixed-113.45.141.212_8848] [add-listener] ok, tenant=, dataId=authorization.yaml, group=DEFAULT_GROUP, cnt=1 
2025-06-19 16:33:41 authorization [com.alibaba.nacos.client.Worker.longPolling.fixed-113.45.141.212_8848] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[] 
2025-06-19 16:33:41 authorization [main] INFO  org.springframework.cloud.openfeign.FeignClientFactoryBean - For 'admin' URL not provided. Will try picking an instance via load-balancing. 
2025-06-19 16:33:41 authorization [main] ERROR org.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@544d57e] to prepare test instance [com.kakarote.authorization.AuthorizationAppliactionTest@1fa1cab1] 
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.kakarote.authorization.AuthorizationAppliactionTest': Unsatisfied dependency expressed through field 'adminUserService'; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireBeanProperties(AbstractAutowireCapableBeanFactory.java:392)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:119)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener.prepareTestInstance(SpringBootDependencyInjectionTestExecutionListener.java:43)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at org.junit.vintage.engine.execution.RunnerExecutor.execute(RunnerExecutor.java:43)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.junit.vintage.engine.VintageTestEngine.executeAllChildren(VintageTestEngine.java:82)
	at org.junit.vintage.engine.VintageTestEngine.execute(VintageTestEngine.java:73)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:248)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$5(DefaultLauncher.java:211)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:226)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:199)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:132)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'com.kakarote.authorization.service.AdminUserService': Requested bean is currently in creation: Is there an unresolvable circular reference?
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.beforeSingletonCreation(DefaultSingletonBeanRegistry.java:355)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:227)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1109)
	at com.alibaba.cloud.sentinel.feign.SentinelFeign$Builder$1.create(SentinelFeign.java:84)
	at feign.ReflectiveFeign.newInstance(ReflectiveFeign.java:64)
	at feign.Feign$Builder.target(Feign.java:269)
	at org.springframework.cloud.openfeign.HystrixTargeter.target(HystrixTargeter.java:38)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.loadBalance(FeignClientFactoryBean.java:352)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getTarget(FeignClientFactoryBean.java:388)
	at org.springframework.cloud.openfeign.FeignClientFactoryBean.getObject(FeignClientFactoryBean.java:361)
	at org.springframework.cloud.openfeign.FeignClientsRegistrar.lambda$registerFeignClient$0(FeignClientsRegistrar.java:246)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.obtainFromSupplier(AbstractAutowireCapableBeanFactory.java:1230)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1172)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 52 common frames omitted
2025-06-19 16:33:41 authorization [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler' 
2025-06-19 16:33:41 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ... 
2025-06-19 16:33:41 authorization [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed 
