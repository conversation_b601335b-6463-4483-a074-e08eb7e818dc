package com.kakarote.work.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.work.entity.BO.WorkOwnerRoleBO;
import com.kakarote.work.entity.BO.WorkTaskQueryBO;
import com.kakarote.work.entity.BO.WorkTaskTemplateBO;
import com.kakarote.work.entity.PO.Work;
import com.kakarote.work.entity.PO.WorkTask;
import com.kakarote.work.entity.VO.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-15
 */
public interface WorkMapper extends BaseMapper<Work> {
    void deleteTaskRelationByWorkId(@Param("workId") Long workId);

    void leaveTaskOwnerUser(@Param("workId") Long workId, @Param("userId") Long userId);

    List<String> queryTaskOwnerUser(@Param("userId") Long userId);

    List<WorkInfoVo> queryWorkNameList(@Param("userId") Long userId, @Param("data") WorkTaskQueryBO workTaskQueryBO);

    List<WorkTaskTemplateClassVO> queryWorkTaskTemplateClass(@Param("taskId") Long taskId);

    @SqlParser(filter = true)
    List<TaskInfoVO> queryTaskByClassId(@Param("data") WorkTaskTemplateBO workTaskTemplateBO, @Param("classId") Long classId);

    @SqlParser(filter = true)
    List<TaskInfoVO> queryWorkTaskByCondition(@Param("data") WorkTaskQueryBO workTaskQueryBO, @Param("workIdList") List<Long> workIdList);

    @SqlParser(filter = true)
    WorkTaskStatsVO workStatistics(@Param("workId") Long workId, @Param("workIdList") List<Long> workIdList, @Param("userId") Long userId, @Param("mainUserId") Long mainUserId);

    List<WorkUserStatsVO> queryAllWorkMember();

    List<Long> queryWorkIdListByOwnerUser(@Param("userId") Long userId);

    WorkClassStatsVO queryWorkClassStats(@Param("workId") Long workId, @Param("classId") Long classId);

    List<WorkTask> queryWorkLabelByWorkId(@Param("workId") Long workId);

    List<TaskInfoVO> archList(@Param("workId") Long workId);

    public List<String> queryWorkAuthByUserId(@Param("roleId") Long roleId, @Param("parentId") Integer parentId);

    public List<String> queryMenuByRoleId(@Param("roleId") Long roleId);

    public List<WorkOwnerRoleBO> queryOwnerRoleList(@Param("workId") Long workId);

    public String queryRoleName(@Param("roleId") Long roleId);

    public List<Map<String, Object>> workTaskExport(@Param("workId") Long workId);
}
