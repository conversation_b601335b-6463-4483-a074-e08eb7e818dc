package com.kakarote.work.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.work.entity.PO.WorkTaskLabelOrder;
import com.kakarote.work.mapper.WorkTaskLabelOrderMapper;
import com.kakarote.work.service.IWorkTaskLabelOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目标签排序表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-15
 */
@Service
public class WorkTaskLabelOrderServiceImpl extends BaseServiceImpl<WorkTaskLabelOrderMapper, WorkTaskLabelOrder> implements IWorkTaskLabelOrderService {

}
