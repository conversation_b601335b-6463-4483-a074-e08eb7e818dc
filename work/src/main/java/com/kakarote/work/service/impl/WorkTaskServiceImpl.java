package com.kakarote.work.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.CrmRelationDTO;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminMessageBO;
import com.kakarote.core.feign.admin.entity.AdminMessageEnum;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminMessageService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.*;
import com.kakarote.work.common.WorkAuthUtil;
import com.kakarote.work.common.WorkCodeEnum;
import com.kakarote.work.entity.BO.*;
import com.kakarote.work.entity.PO.*;
import com.kakarote.work.entity.VO.MyTaskVO;
import com.kakarote.work.entity.VO.OaTaskListVO;
import com.kakarote.work.entity.VO.TaskDetailVO;
import com.kakarote.work.entity.VO.TaskInfoVO;
import com.kakarote.work.mapper.WorkTaskMapper;
import com.kakarote.work.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-18
 */
@Service
public class WorkTaskServiceImpl extends BaseServiceImpl<WorkTaskMapper, WorkTask> implements IWorkTaskService {
    @Autowired
    private IWorkTaskLogService workTaskLogService;

    @Autowired
    private IWorkTaskLabelService workTaskLabelService;

    @Autowired
    private IWorkTaskRelationService workTaskRelationService;

    @Autowired
    private IWorkTaskClassService workTaskClassService;

    @Autowired
    private IWorkService workService;

    @Autowired
    private WorkAuthUtil workAuthUtil;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    @Lazy
    private AdminService adminService;

    private static final int FIVE=5;

    private static final String TYPE ="type";

    @Override
    public List<MyTaskVO> myTask(WorkTaskNameBO workTaskNameBO, boolean isInternal) {
        List<MyTaskVO> result = new ArrayList<>();
        result.add(new MyTaskVO("收件箱", 0, 0, new ArrayList<>()));
        if (!isInternal) {
            result.add(new MyTaskVO("今天要做", 1, 0, new ArrayList<>()));
            result.add(new MyTaskVO("下一步要做", 2, 0, new ArrayList<>()));
            result.add(new MyTaskVO("以后要做", 3, 0, new ArrayList<>()));
        }
        result.forEach(myTaskVO -> {
            Long userId = UserUtil.getUserId();
            if (CollUtil.isNotEmpty(workTaskNameBO.getUserIdList())) {
                workTaskNameBO.getUserIdList().remove(userId);
            }
            List<TaskInfoVO> taskList = getBaseMapper().queryMyTaskList(myTaskVO.getIsTop(), UserUtil.getUserId(), workTaskNameBO);
            myTaskVO.setCount(taskList.size());
            if (taskList.size() > 0) {
                if (Objects.equals(workTaskNameBO.getSort(), 1)) {
                    taskList.sort(Comparator.comparingInt(TaskInfoVO::getTopOrderNum));
                }
                boolean completedTask = Optional.ofNullable(workTaskNameBO.getCompletedTask()).orElse(false);
                if (completedTask) {
                    List<TaskInfoVO> taskInfoVoS = taskList.stream().filter(t -> t.getStatus() == 5).collect(Collectors.toList());
                    taskList.removeIf(t -> t.getStatus() == 5);
                    taskList.addAll(taskInfoVoS);
                }
                taskList.forEach(taskInfo -> {
                    if (taskInfo.getWorkId() != null) {
                        Work work = workService.getById(taskInfo.getWorkId());
                        if (work != null) {
                            taskInfo.setWorkName(work.getName());
                        }
                    }
                    if (taskInfo.getBatchId() != null) {
                        List<FileEntity> fileEntities = adminFileService.queryFileList(taskInfo.getBatchId()).getData();
                        taskInfo.setFileNum(fileEntities.size());
                    }
                });
                taskListTransfer(taskList);
                myTaskVO.setList(taskList);
            }
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskListTransfer(List<TaskInfoVO> taskList) {
        List<Long> taskIdList = taskList.stream().map(TaskInfoVO::getTaskId).collect(Collectors.toList());
        if (taskIdList.size() == 0) {
            taskIdList.add(0L);
        }
        List<TaskLabelBO> allLabelList = getBaseMapper().queryTaskLabel(taskIdList);
        Map<Long, List<TaskLabelBO>> labelMap = allLabelList.stream().collect(Collectors.groupingBy(TaskLabelBO::getTaskId));
        // 查询关系列表
        Map<Long, Integer> taskRelationCountMap = getTaskRelationCount(taskIdList);
        // 遍历处理
        taskList.forEach(taskInfo -> {
            LocalDate stopTime = taskInfo.getStopTime();
            if (stopTime != null) {
                if (LocalDateTimeUtil.toEpochMilli(stopTime) < System.currentTimeMillis()) {
                    taskInfo.setIsEnd(1);
                } else {
                    taskInfo.setIsEnd(0);
                }
            } else {
                taskInfo.setIsEnd(0);
            }
            Long taskId = taskInfo.getTaskId();
            if (ObjectUtil.isNotEmpty(taskInfo.getMainUserId())) {
                SimpleUser simpleUser = UserCacheUtil.getSimpleUser(taskInfo.getMainUserId());
                taskInfo.setMainUser(simpleUser);
            } else {
                taskInfo.setMainUser(null);
            }
            ArrayList<TaskLabelBO> labelList = new ArrayList<>();
            if (labelMap.get(taskId) != null) {
                labelList.addAll(labelMap.get(taskId));
            }
            taskInfo.setLabelList(labelList);
            // 处理关系数量
            taskInfo.setRelationCount(taskRelationCountMap.getOrDefault(taskInfo.getTaskId(), 0));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTop(UpdateTaskTopBo updateTaskClassBo) {
        if (CollectionUtil.isNotEmpty(updateTaskClassBo.getFromList())) {
            List<Integer> fromList = updateTaskClassBo.getFromList();
            for (int i = 1; i <= fromList.size(); i++) {
                updateById(new WorkTask().setTaskId(Convert.toLong(fromList.get(i - 1))).setIsTop(updateTaskClassBo.getFromTopId()).setTopOrderNum(i));
            }
        }
        if (CollectionUtil.isNotEmpty(updateTaskClassBo.getToList())) {
            List<Integer> toList = updateTaskClassBo.getToList();
            for (int i = 1; i <= toList.size(); i++) {
                updateById(new WorkTask().setTaskId(Convert.toLong(toList.get(i - 1))).setIsTop(updateTaskClassBo.getToTopId()).setTopOrderNum(i));
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkTask(WorkTask task) {
        UserInfo user = UserUtil.getUser();
        if (StrUtil.isNotEmpty(task.getOwnerUserId())) {
            Set<Long> ownerUserId = SeparatorUtil.toLongSet(task.getOwnerUserId());
            ownerUserId.add(user.getUserId());
            task.setOwnerUserId(SeparatorUtil.fromLongSet(ownerUserId));
        } else {
            task.setOwnerUserId("," + user.getUserId() + ",");
        }
        task.setCreateUserId(user.getUserId());
        if (StrUtil.isEmpty(task.getBatchId())) {
            task.setBatchId(IdUtil.simpleUUID());
        }
        //标签
        String labelId = task.getLabelId();
        if (StrUtil.isNotEmpty(labelId)) {
            task.setLabelId(SeparatorUtil.fromString(labelId));
        }
        boolean isSave = save(task);
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(user.getUserId());
        workTaskLog.setTaskId(task.getTaskId());
        workTaskLog.setContent("添加了新任务 " + task.getName());
        workTaskLogService.saveWorkTaskLog(workTaskLog);

        // 添加关系
        WorkTaskRelation workTaskRelation = BeanUtil.copyProperties(task, WorkTaskRelation.class);
        workTaskRelation.setEndTime(task.getStopTime()==null?null:task.getStopTime());
        workTaskRelationService.saveWorkTaskRelation(workTaskRelation);

        if (isSave) {
            AdminMessageBO adminMessageBO = new AdminMessageBO();
            adminMessageBO.setMessageType(AdminMessageEnum.OA_TASK_ALLOCATION.getType());
            adminMessageBO.setTypeId(task.getTaskId());
            adminMessageBO.setUserId(user.getUserId());
            adminMessageBO.setTitle(task.getName());
            JSONObject jsonObject = setContent(task.getTaskId());
            jsonObject.fluentPut("startTime", task.getStartTime()).fluentPut("endTime", task.getStopTime());
            adminMessageBO.setContent(jsonObject.toJSONString());
            List<Long> ids = new ArrayList<>();
            if (task.getMainUserId() != null) {
                ids.add(task.getMainUserId());
            }
            adminMessageBO.setIds(ids);
            //分配给我的任务
            ApplicationContextHolder.getBean(AdminMessageService.class).sendMessage(adminMessageBO);
            adminMessageBO.setMessageType(AdminMessageEnum.OA_TASK_JOIN.getType());
            ids = new ArrayList<>(StrUtil.splitTrim(task.getOwnerUserId(), Const.SEPARATOR).stream().map(Long::valueOf).collect(Collectors.toList()));
            adminMessageBO.setIds(ids);
            //我参与的任务
            ApplicationContextHolder.getBean(AdminMessageService.class).sendMessage(adminMessageBO);
            //子任务
            List<TaskInfoVO> taskInfoList = task.getTaskInfoList();
            if (CollUtil.isNotEmpty(taskInfoList)) {
                List<WorkTask> childTaskList = new ArrayList<>();
                for (TaskInfoVO taskInfo : taskInfoList) {
                    WorkTask childTask = new WorkTask()
                            .setName(taskInfo.getName())
                            .setStatus(taskInfo.getStatus())
                            .setStopTime(taskInfo.getStopTime());
                    if (taskInfo.getMainUserId() == null) {
                        childTask.setMainUserId(user.getUserId());
                    } else {
                        childTask.setMainUserId(taskInfo.getMainUserId());
                    }
                    childTask.setOwnerUserId("," + user.getUserId() + ",");
                    childTask.setCreateUserId(user.getUserId());
                    childTask.setBatchId(task.getBatchId());
                    childTask.setPid(task.getTaskId());
                    childTaskList.add(childTask);
                }
                saveBatch(childTaskList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWorkTask(WorkTask task) {
        Long taskId = task.getTaskId();
        WorkTask oldWorkTask = getById(taskId);
        if (oldWorkTask == null) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_EXIST_ERROR);
        }

        //任务名称
        if (!Objects.equals(oldWorkTask.getName(), task.getName())) {
            this.saveWorkTaskLog(taskId, "将标题修改为:" + task.getName());
        }
        //描述
        if (!Objects.equals(oldWorkTask.getDescription(), task.getDescription())) {
            this.saveWorkTaskLog(taskId, "将描述信息修改为:" + task.getDescription());
        }
        //负责人
        Long mainUserId = task.getMainUserId();
        if (!Objects.equals(oldWorkTask.getMainUserId(), mainUserId)) {
            if (mainUserId == null) {
                this.saveWorkTaskLog(taskId, "将负责人修改为:无");
            } else {
                this.saveWorkTaskLog(taskId, "将负责人修改为:" + UserCacheUtil.getUserName(mainUserId));
            }
        }
        //任务开始时间
        if (!Objects.equals(oldWorkTask.getStartTime(), task.getStartTime())) {
            this.saveWorkTaskLog(taskId, "把任务开始时间修改为:" + LocalDateTimeUtil.format(task.getStartTime(), DatePattern.NORM_DATE_FORMATTER));
        }
        //任务结束时间
        if (!Objects.equals(oldWorkTask.getStopTime(), task.getStopTime())) {
            this.saveWorkTaskLog(taskId, "把任务结束时间修改为:" + LocalDateTimeUtil.format(task.getStopTime(), DatePattern.NORM_DATE_FORMATTER));
        }
        //参与人
        if (StrUtil.isEmpty(oldWorkTask.getOwnerUserId())) {
            //判断旧数据没有参与人
            String[] userIds = task.getOwnerUserId().split(",");
            for (String id : userIds) {
                if (StrUtil.isNotBlank(id)) {
                    String userName = UserCacheUtil.getUserName(Long.valueOf(id));
                    this.saveWorkTaskLog(taskId, "添加" + userName + "参与任务");
                }
            }
        } else {
            //判断旧数据有参与人
            if (StrUtil.isNotEmpty(task.getOwnerUserId())) {
                String[] userIds = task.getOwnerUserId().split(",");
                List<Long> ids = new ArrayList<>();
                for (String id : userIds) {
                    if (StrUtil.isNotBlank(id)) {
                        if (!oldWorkTask.getOwnerUserId().contains("," + id + ",")) {
                            ids.add(Long.valueOf(id));
                            String userName = UserCacheUtil.getUserName(Long.valueOf(id));
                            this.saveWorkTaskLog(taskId, "添加" + userName + "参与任务");
                        }
                    }
                }
                if (ids.size() > 0) {
                    AdminMessageBO adminMessageBO = new AdminMessageBO();
                    adminMessageBO.setMessageType(AdminMessageEnum.OA_TASK_JOIN.getType());
                    JSONObject jsonObject = setContent(task.getTaskId());
                    jsonObject.fluentPut("startTime", task.getStartTime()).fluentPut("endTime", task.getStopTime());
                    adminMessageBO.setContent(jsonObject.toJSONString());
                    adminMessageBO.setTypeId(taskId);
                    adminMessageBO.setUserId(UserUtil.getUserId());
                    adminMessageBO.setTitle(task.getName());
                    adminMessageBO.setIds(ids);
                    ApplicationContextHolder.getBean(AdminMessageService.class).sendMessage(adminMessageBO);
                }
            }
        }
        String ownerUserId = task.getOwnerUserId();
        if (StrUtil.isNotEmpty(ownerUserId)) {
            task.setOwnerUserId(SeparatorUtil.fromString(ownerUserId));
        }
        //标签
        if (StrUtil.isEmpty(oldWorkTask.getLabelId())) {
            //旧数据没有标签 直接添加
            String[] labelName = task.getLabelId().split(",");
            for (String id : labelName) {
                if (StrUtil.isNotBlank(id)) {
                    WorkTaskLabel workTaskLabel = workTaskLabelService.getById(id);
                    this.saveWorkTaskLog(taskId, "增加了标签:" + workTaskLabel.getName());
                }
            }
        } else {
            //旧数据有标签 自动添加或修改
            if (StrUtil.isNotEmpty(task.getLabelId())) {
                String[] labelName = task.getLabelId().split(",");
                for (String id : labelName) {
                    if (StrUtil.isNotBlank(id)) {
                        if (!oldWorkTask.getLabelId().contains("," + id + ",")) {
                            WorkTaskLabel workTaskLabel = workTaskLabelService.getById(id);
                            this.saveWorkTaskLog(taskId, "增加了标签:" + workTaskLabel.getName());
                        }
                    }
                }
            }
        }
        String labelId = task.getLabelId();
        if (StrUtil.isNotEmpty(labelId)) {
            task.setLabelId(SeparatorUtil.fromString(labelId));
        }
        //优先级
        if (!Objects.equals(oldWorkTask.getPriority(), task.getPriority())) {
            this.saveWorkTaskLog(taskId, "将优先级修改为:" + this.getPriorityDesc(task.getPriority()));
        }

        // 添加关联业务关系
        WorkTaskRelation workTaskRelation = BeanUtil.copyProperties(task, WorkTaskRelation.class);
        workTaskRelationService.saveWorkTaskRelation(workTaskRelation);

        //子任务
        List<TaskInfoVO> taskInfoList = task.getTaskInfoList();
        if (CollUtil.isNotEmpty(taskInfoList)) {
            LambdaQueryWrapper<WorkTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(WorkTask::getPid, taskId);
            lambdaQueryWrapper.eq(WorkTask::getBatchId, task.getBatchId());
            this.remove(lambdaQueryWrapper);
            UserInfo user = UserUtil.getUser();
            List<WorkTask> childTaskList = new ArrayList<>();
            for (TaskInfoVO taskInfo : taskInfoList) {
                WorkTask childTask = new WorkTask().setName(taskInfo.getName()).setStopTime(taskInfo.getStopTime());
                if (taskInfo.getMainUserId() == null) {
                    childTask.setMainUserId(user.getUserId());
                } else {
                    childTask.setMainUserId(taskInfo.getMainUserId());
                }
                childTask.setOwnerUserId("," + user.getUserId() + ",");
                childTask.setCreateUserId(user.getUserId());
                childTask.setBatchId(task.getBatchId());
                childTask.setPid(taskId);
                childTaskList.add(childTask);
            }
            this.saveBatch(childTaskList);
        }
        return this.updateById(task);
    }


    /**
     * 保存任务日志
     *
     * @param taskId
     * @param content
     * @return void
     * @date 2020/11/11 16:15
     **/
    private void saveWorkTaskLog(Long taskId, String content) {
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(taskId);
        workTaskLog.setContent(content);
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    /**
     * 获取优先级描述
     *
     * @param priority
     * @return java.lang.String
     * @date 2020/11/12 10:22
     **/
    @Override
    public String getPriorityDesc(Integer priority) {
        String priorityDesc = "";
        switch (priority) {
            case 0:
                priorityDesc = "无";
                break;
            case 1:
                priorityDesc = "低";
                break;
            case 2:
                priorityDesc = "中";
                break;
            case 3:
                priorityDesc = "高";
                break;
            default:
                break;
        }
        return priorityDesc;
    }

    @Override
    public void setWorkTaskStatus(WorkTaskStatusBO workTaskStatusBO) {
        WorkTask workTask = getById(workTaskStatusBO.getTaskId());
        if (workTask.getStatus() != FIVE && Objects.equals(FIVE, workTaskStatusBO.getStatus())) {
            AdminMessageBO adminMessageBO = new AdminMessageBO();
            adminMessageBO.setMessageType(AdminMessageEnum.OA_TASK_OVER.getType());
            adminMessageBO.setTypeId(workTask.getTaskId());
            adminMessageBO.setUserId(UserUtil.getUserId());
            JSONObject jsonObject = setContent(workTask.getTaskId());
            jsonObject.fluentPut("startTime", workTask.getStartTime()).fluentPut("endTime", workTask.getStopTime());
            adminMessageBO.setContent(jsonObject.toJSONString());
            adminMessageBO.setTitle(workTask.getName());
            List<Long> ids = new ArrayList<>();
            if (workTask.getMainUserId() != null) {
                ids.add(workTask.getMainUserId());
            }
            ids.addAll(StrUtil.splitTrim(workTask.getOwnerUserId(), Const.SEPARATOR).stream().map(Long::valueOf).collect(Collectors.toList()));
            adminMessageBO.setIds(ids);
            ApplicationContextHolder.getBean(AdminMessageService.class).sendMessage(adminMessageBO);
        }
        workTask.setStatus(workTaskStatusBO.getStatus());
        updateById(workTask);
        String statusName = "";
        if (workTaskStatusBO.getStatus().equals(1)) {
            statusName = "未完成";
        } else if (workTaskStatusBO.getStatus().equals(FIVE)) {
            statusName = "完成";
        }
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(workTaskStatusBO.getTaskId());
        workTaskLog.setContent("将状态修改为:" + statusName);
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    private JSONObject setContent(Long id){
        JSONObject jsonObject = new JSONObject();
        List<JSONObject> list = getBaseMapper().queryRelationData(id);
        if(!CollectionUtil.isEmpty(list)){
            List<JSONObject> contactsList = new ArrayList<>();
            List<JSONObject> businessList = new ArrayList<>();
            List<JSONObject> contractList = new ArrayList<>();
            List<JSONObject> customerList = new ArrayList<>();
            int THREE=3;
            int TWO=2;
            int SIX=6;
            list.forEach(json -> {
                if (json.getInteger(TYPE) == THREE) {
                    contactsList.add(new JSONObject().fluentPut("id", json.getString("id")).fluentPut("name", json.getString("name")));
                } else if (json.getInteger(TYPE) == FIVE) {
                    businessList.add(new JSONObject().fluentPut("id", json.getString("id")).fluentPut("name", json.getString("name")));
                } else if (json.getInteger(TYPE) == TWO) {
                    customerList.add(new JSONObject().fluentPut("id", json.getString("id")).fluentPut("name", json.getString("name")));
                } else if (json.getInteger(TYPE) == SIX) {
                    contractList.add(new JSONObject().fluentPut("id", json.getString("id")).fluentPut("name", json.getString("name")));
                }
            });

            if(!CollectionUtil.isEmpty(contactsList)){
                jsonObject.put("contactss",contactsList);
            }
            if(!CollectionUtil.isEmpty(businessList)){
                jsonObject.put("businesss",businessList);
            }
            if(!CollectionUtil.isEmpty(customerList)){
                jsonObject.put("customers",customerList);
            }
            if(!CollectionUtil.isEmpty(contractList)){
                jsonObject.put("contracts",contractList);
            }
        }
        return jsonObject;

    }

    @Override
    public void setWorkTaskTitle(WorkTaskNameBO workTaskNameBO) {
        updateById(new WorkTask().setName(workTaskNameBO.getName()).setTaskId(workTaskNameBO.getTaskId()));
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(workTaskNameBO.getTaskId());
        workTaskLog.setContent("将标题修改为:" + workTaskNameBO.getName());
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    @Override
    public void setWorkTaskDescription(WorkTaskDescriptionBO workTaskDescriptionBO) {
        updateById(new WorkTask().setDescription(workTaskDescriptionBO.getDescription()).setTaskId(workTaskDescriptionBO.getTaskId()));
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(workTaskDescriptionBO.getTaskId());
        workTaskLog.setContent("将描述信息修改为:" + workTaskDescriptionBO.getDescription());
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    @Override
    public void setWorkTaskMainUser(WorkTaskUserBO workTaskUserBO) {
        Long mainUserId = workTaskUserBO.getUserId();
        Long taskId = workTaskUserBO.getTaskId();
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(workTaskUserBO.getTaskId());
        if (mainUserId == null) {
            update(null, Wrappers.<WorkTask>lambdaUpdate().set(WorkTask::getMainUserId, null).eq(WorkTask::getTaskId, taskId));
            workTaskLog.setContent("将负责人修改为:无");
        } else {
            updateById(new WorkTask().setTaskId(taskId).setMainUserId(mainUserId));
            workTaskLog.setContent("将负责人修改为:" + UserCacheUtil.getUserName(mainUserId));
        }
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    @Override
    public void setWorkTaskOwnerUser(WorkTaskOwnerUserBO workTaskOwnerUserBO) {
        WorkTask auldTask = getById(workTaskOwnerUserBO.getTaskId());
        if (StrUtil.isEmpty(auldTask.getOwnerUserId())) {
            //判断旧数据没有参与人
            String[] userIds = workTaskOwnerUserBO.getOwnerUserId().split(",");
            for (String id : userIds) {
                if (StrUtil.isNotBlank(id)) {
                    String userName = UserCacheUtil.getUserName(Long.valueOf(id));
                    WorkTaskLog workTaskLog = new WorkTaskLog();
                    workTaskLog.setUserId(UserUtil.getUserId());
                    workTaskLog.setTaskId(workTaskOwnerUserBO.getTaskId());
                    workTaskLog.setContent("添加" + userName + "参与任务");
                    workTaskLogService.saveWorkTaskLog(workTaskLog);
                }
            }
        } else {
            //判断旧数据有参与人
            if (StrUtil.isNotEmpty(workTaskOwnerUserBO.getOwnerUserId())) {
                String[] userIds = workTaskOwnerUserBO.getOwnerUserId().split(",");
                List<Long> ids = new ArrayList<>();
                for (String id : userIds) {
                    if (StrUtil.isNotBlank(id)) {
                        if (!auldTask.getOwnerUserId().contains("," + id + ",")) {
                            ids.add(Long.valueOf(id));
                            String userName = UserCacheUtil.getUserName(Long.valueOf(id));
                            WorkTaskLog workTaskLog = new WorkTaskLog();
                            workTaskLog.setUserId(UserUtil.getUserId());
                            workTaskLog.setTaskId(workTaskOwnerUserBO.getTaskId());
                            workTaskLog.setContent("添加" + userName + "参与任务");
                            workTaskLogService.saveWorkTaskLog(workTaskLog);
                        }
                    }
                }
                if (ids.size() > 0) {
                    AdminMessageBO adminMessageBO = new AdminMessageBO();
                    adminMessageBO.setMessageType(AdminMessageEnum.OA_TASK_JOIN.getType());
                    JSONObject jsonObject = setContent(auldTask.getTaskId());
                    jsonObject.fluentPut("startTime", auldTask.getStartTime()).fluentPut("endTime", auldTask.getStopTime());
                    adminMessageBO.setContent(jsonObject.toJSONString());
                    adminMessageBO.setTypeId(auldTask.getTaskId());
                    adminMessageBO.setUserId(UserUtil.getUserId());
                    adminMessageBO.setTitle(auldTask.getName());
                    adminMessageBO.setIds(ids);
                    ApplicationContextHolder.getBean(AdminMessageService.class).sendMessage(adminMessageBO);
                }
            }
        }
        WorkTask workTask = new WorkTask();
        String ownerUserId = workTaskOwnerUserBO.getOwnerUserId();
        if (StrUtil.isNotEmpty(ownerUserId)) {
            workTask.setOwnerUserId(SeparatorUtil.fromString(ownerUserId));
        }
        updateById(workTask.setTaskId(workTaskOwnerUserBO.getTaskId()));
    }

    @Override
    public void setWorkTaskTime(WorkTask workTask) {
        if (workTask.getStartTime() == null && workTask.getStopTime() == null) {
            throw new CrmException(SystemCodeEnum.WORK_NO_TIME_VALID);
        }
        WorkTask task = getById(workTask.getTaskId());
        LambdaUpdateChainWrapper<WorkTask> lambdaUpdate = lambdaUpdate();
        if (!Objects.equals(task.getStartTime(), workTask.getStartTime())) {
            WorkTaskLog workTaskLog = new WorkTaskLog();
            workTaskLog.setUserId(UserUtil.getUserId());
            workTaskLog.setTaskId(workTask.getTaskId());
            workTaskLog.setContent("把任务开始时间修改为:" + (workTask.getStartTime() == null ? "空" : LocalDateTimeUtil.format(workTask.getStartTime(), DatePattern.NORM_DATE_FORMATTER)));
            workTaskLogService.saveWorkTaskLog(workTaskLog);
            lambdaUpdate.set(WorkTask::getStartTime, workTask.getStartTime());
        }
        if (!Objects.equals(task.getStopTime(), workTask.getStopTime())) {
            WorkTaskLog workTaskLog = new WorkTaskLog();
            workTaskLog.setUserId(UserUtil.getUserId());
            workTaskLog.setTaskId(workTask.getTaskId());
            workTaskLog.setContent("把任务结束时间修改为:" + (workTask.getStopTime() == null ? "空" : LocalDateTimeUtil.format(workTask.getStopTime(), DatePattern.NORM_DATE_FORMATTER)));
            workTaskLogService.saveWorkTaskLog(workTaskLog);
            lambdaUpdate.set(WorkTask::getStopTime, workTask.getStopTime());
            if (workTask.getStopTime() == null) {
                lambdaUpdate.set(WorkTask::getStatus, 1);
            } else if (LocalDateTimeUtil.toEpochMilli(workTask.getStopTime()) < System.currentTimeMillis()) {
                lambdaUpdate.set(WorkTask::getStatus, 2);
            } else if (LocalDateTimeUtil.toEpochMilli(workTask.getStopTime()) > System.currentTimeMillis()) {
                lambdaUpdate.set(WorkTask::getStatus, 1);
            }
        }
        lambdaUpdate.eq(WorkTask::getTaskId, workTask.getTaskId());
        lambdaUpdate.update();
    }

    @Override
    public void setWorkTaskLabel(WorkTaskLabelsBO workTaskLabelsBO) {
        WorkTask auldTask = getById(workTaskLabelsBO.getTaskId());
        if (StrUtil.isEmpty(auldTask.getLabelId())) {
            //旧数据没有标签 直接添加
            String[] labelName = workTaskLabelsBO.getLabelId().split(",");
            for (String id : labelName) {
                if (StrUtil.isNotBlank(id)) {
                    WorkTaskLabel workTaskLabel = workTaskLabelService.getById(id);
                    WorkTaskLog workTaskLog = new WorkTaskLog();
                    workTaskLog.setUserId(UserUtil.getUserId());
                    workTaskLog.setTaskId(workTaskLabelsBO.getTaskId());
                    workTaskLog.setContent("增加了标签:" + workTaskLabel.getName());
                    workTaskLogService.saveWorkTaskLog(workTaskLog);
                }
            }
        } else {
            //旧数据有标签 自动添加或修改
            if (StrUtil.isNotEmpty(workTaskLabelsBO.getLabelId())) {
                String[] labelName = workTaskLabelsBO.getLabelId().split(",");
                for (String id : labelName) {
                    if (StrUtil.isNotBlank(id)) {
                        if (!auldTask.getLabelId().contains("," + id + ",")) {
                            WorkTaskLabel workTaskLabel = workTaskLabelService.getById(id);
                            WorkTaskLog workTaskLog = new WorkTaskLog();
                            workTaskLog.setUserId(UserUtil.getUserId());
                            workTaskLog.setTaskId(workTaskLabelsBO.getTaskId());
                            workTaskLog.setContent("增加了标签:" + workTaskLabel.getName());
                            workTaskLogService.saveWorkTaskLog(workTaskLog);
                        }
                    }
                }
            }
        }
        WorkTask workTask = new WorkTask();
        String labelId = workTaskLabelsBO.getLabelId();
        if (StrUtil.isNotEmpty(labelId)) {
            workTask.setLabelId(SeparatorUtil.fromString(labelId));
        }
        updateById(workTask.setTaskId(workTaskLabelsBO.getTaskId()));
    }

    @Override
    public void setWorkTaskPriority(WorkTaskPriorityBO workTaskPriorityBO) {
        updateById(new WorkTask().setTaskId(workTaskPriorityBO.getTaskId()).setPriority(workTaskPriorityBO.getPriority()));
        String priority = this.getPriorityDesc(workTaskPriorityBO.getPriority());
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(workTaskPriorityBO.getTaskId());
        workTaskLog.setContent("将优先级修改为:" + priority);
        workTaskLogService.saveWorkTaskLog(workTaskLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkTask addWorkChildTask(WorkTask workTask) {
        WorkTask task = new WorkTask().setName(workTask.getName()).setStopTime(workTask.getStopTime());
        WorkTask parentTask = getById(workTask.getPid());
        task.setIshidden(parentTask.getIshidden());
        UserInfo user = UserUtil.getUser();
        if (workTask.getMainUserId() == null) {
            task.setMainUserId(user.getUserId());
        } else {
            task.setMainUserId(workTask.getMainUserId());
        }
        task.setOwnerUserId("," + user.getUserId() + ",");
        task.setCreateUserId(user.getUserId());
        task.setBatchId(IdUtil.simpleUUID());
        task.setPid(workTask.getPid());
        save(task);
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(user.getUserId());
        workTaskLog.setTaskId(task.getTaskId());
        workTaskLog.setContent("添加了任务 " + task.getName());
        workTaskLogService.saveWorkTaskLog(workTaskLog);
        return task;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkChildTask(WorkTask workTask) {
        WorkTask task = new WorkTask().setTaskId(workTask.getTaskId()).setStopTime(workTask.getStopTime()).setName(workTask.getName());
        if (workTask.getMainUserId() == null) {
            update(null, Wrappers.<WorkTask>lambdaUpdate().set(WorkTask::getMainUserId, null).eq(WorkTask::getTaskId, workTask.getTaskId()));
        } else {
            task.setMainUserId(workTask.getMainUserId());
        }
        updateById(task.setTaskId(workTask.getTaskId()));
    }

    @Override
    public void setWorkChildTaskStatus(WorkTaskStatusBO workTaskStatusBO) {
        updateById(new WorkTask().setStatus(workTaskStatusBO.getStatus()).setTaskId(workTaskStatusBO.getTaskId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkTaskOwnerUser(WorkTaskUserBO workTaskUserBO) {
        WorkTask task = getById(workTaskUserBO.getTaskId());
        Long userId = workTaskUserBO.getUserId();
        String userName = UserCacheUtil.getUserName(userId);
        Set<Long> ownerUserIds = SeparatorUtil.toLongSet(task.getOwnerUserId());
        if (StrUtil.isEmpty(userName)) {
            throw new CrmException(WorkCodeEnum.WORK_USER_EXIST_ERROR);
        }
        ownerUserIds.remove(userId);
        task.setOwnerUserId(SeparatorUtil.fromLongSet(ownerUserIds));
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(task.getTaskId());
        workTaskLog.setContent("将 " + userName + "从任务中移除");
        workTaskLogService.saveWorkTaskLog(workTaskLog);
        updateById(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkTaskLabel(WorkTaskLabelBO workTaskLabelBO) {
        WorkTask task = getById(workTaskLabelBO.getTaskId());
        Long labelId = workTaskLabelBO.getLabelId();
        WorkTaskLabel taskLabel = workTaskLabelService.getById(labelId);
        Set<Long> labelIds = SeparatorUtil.toLongSet(task.getLabelId());
        if (!labelIds.contains(labelId)) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_LABEL_EXIST_ERROR);
        }
        labelIds.remove(labelId);
        task.setLabelId(SeparatorUtil.fromLongSet(labelIds));
        WorkTaskLog workTaskLog = new WorkTaskLog();
        workTaskLog.setUserId(UserUtil.getUserId());
        workTaskLog.setTaskId(task.getTaskId());
        workTaskLog.setContent("删除了标签 " + taskLabel.getName());
        workTaskLogService.saveWorkTaskLog(workTaskLog);
        updateById(task);
    }

    @Override
    public TaskDetailVO queryTaskInfo(Long taskId) {
        int number = query().eq("ishidden", 0).eq("task_id", taskId).count();
        if (number == 0) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_DELETE_ERROR);
        }
        return queryTrashTaskInfo(taskId);
    }

    @Override
    public TaskDetailVO queryTrashTaskInfo(Long taskId) {
        if (!workAuthUtil.isTaskAuth(taskId)) {
            throw new CrmException(WorkCodeEnum.WORK_AUTH_ERROR);
        }
        if (!UserUtil.isAdmin() && !UserUtil.getUser().getRoles().contains(workAuthUtil.getWorkAdminRole())) {
            WorkTask task = getById(taskId);
            boolean auth = true;
            if (Objects.equals(0L, task.getWorkId())) {
                auth = workAuthUtil.isOaAuth(1, taskId);
            } else {
                Work work = workService.getById(task.getWorkId());
                if (TagUtil.toLongSet(work.getOwnerUserId()).contains(UserUtil.getUserId()) || work.getIsOpen() == 1) {
                    auth = false;
                }
            }
            if (auth) {
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
            }
        }
        TaskDetailVO taskDetailVO = transfer(taskId);
        if (StrUtil.isNotEmpty(taskDetailVO.getBatchId())) {
            List<FileEntity> data = adminFileService.queryFileList(taskDetailVO.getBatchId()).getData();
            taskDetailVO.setFile(data);
        } else {
            taskDetailVO.setFile(new ArrayList<>());
        }
        List<Long> childTaskIdList = listObjs(new QueryWrapper<WorkTask>().select("task_id").eq("pid", taskId), o -> Convert.toLong(o.toString()));
        List<TaskDetailVO> childTaskList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(childTaskIdList)) {
            childTaskIdList.forEach(childTaskId -> {
                TaskDetailVO childTask = transfer(childTaskId);
                if (StrUtil.isNotEmpty(taskDetailVO.getBatchId())) {
                    List<FileEntity> file = adminFileService.queryFileList(taskDetailVO.getBatchId()).getData();
                    childTask.setFile(file);
                }
                childTaskList.add(childTask);
            });
        }
        taskDetailVO.setChildTask(childTaskList);
        if (taskDetailVO.getWorkId() != null && taskDetailVO.getWorkId() != 0) {
            taskDetailVO.setAuthList(ApplicationContextHolder.getBean(IWorkService.class).auth(taskDetailVO.getWorkId()).getJSONObject("work"));
        }
        setRelation(taskId, taskDetailVO);
        return taskDetailVO;
    }

    /**
     * 设置关系数据
     *
     * @param taskId       taskId
     * @param taskDetailVO task详情VO
     * @return void
     * <AUTHOR> sir
     * @date 2021/11/23
     */
    private void setRelation(Long taskId, TaskDetailVO taskDetailVO) {
        Map<Integer, Set<Long>> relationIdsMap = workTaskRelationService.lambdaQuery()
                .select(WorkTaskRelation::getRelationId, WorkTaskRelation::getType)
                .eq(WorkTaskRelation::getTaskId, taskId)
                .list()
                .stream()
                .collect(Collectors.groupingBy(WorkTaskRelation::getType, Collectors.mapping(WorkTaskRelation::getRelationId, Collectors.toSet())));
        // 获取crmRelation
        CrmRelationDTO crmRelation = CrmRelationUtils.getCrmRelation(relationIdsMap, taskId);
        // copy data
        BeanUtil.copyProperties(crmRelation, taskDetailVO);
    }

    private TaskDetailVO transfer(Long taskId) {
        TaskDetailVO task = getBaseMapper().queryTaskInfo(taskId);
        task.setStopTime(DateUtil.formatDate((Date) task.getStopTime()));
        if (task.getMainUserId() != null) {
            task.setMainUser(UserCacheUtil.getSimpleUser(task.getMainUserId()));
        }
        task.setCreateUser(UserCacheUtil.getSimpleUser(task.getCreateUserId()));
        List<WorkTaskLabelBO> labelList = new ArrayList<>();
        List<SimpleUser> ownerUserList = new ArrayList<>();
        if (StrUtil.isNotBlank(task.getLabelId())) {
            List<String> labelIds = StrUtil.splitTrim(task.getLabelId(), Const.SEPARATOR);
            if (labelIds.size() > 0) {
                labelList = getBaseMapper().queryWorkTaskLabelList(labelIds.stream().map(Long::parseLong).collect(Collectors.toList()));
            }
        }
        List<Long> ids = StrUtil.splitTrim(task.getOwnerUserId(), Const.SEPARATOR).stream().map(Long::valueOf).collect(Collectors.toList());
        if (ids.size() > 0) {
            ownerUserList.addAll(UserCacheUtil.getSimpleUsers(ids));
        }
        setRelation(taskId, task);
        task.setOwnerUserList(ownerUserList);
        task.setLabelList(labelList);
        return task;
    }

    @Override
    public void deleteWorkTask(Long taskId) {
        WorkTask workTask = getOne(new QueryWrapper<WorkTask>().select("pid").eq("task_id", taskId));
        if (!Objects.equals(workTask.getPid(), 0)) {
            removeById(taskId);
        } else {
            UpdateWrapper<WorkTask> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(WorkTask::getIshidden, 1);
            updateWrapper.lambda().eq(WorkTask::getPid, taskId);
            this.update(updateWrapper);
            updateById(new WorkTask().setTaskId(taskId).setIshidden(1).setHiddenTime(LocalDateTimeUtil.now()));
        }
        if (StrUtil.isNotEmpty(workTask.getBatchId())) {
            adminFileService.delete(workTask.getBatchId());
        }
    }

    @Override
    public void archiveByTaskId(Long taskId) {
        updateById(new WorkTask().setTaskId(taskId).setIsArchive(1).setArchiveTime(LocalDateTimeUtil.now()));
    }

    @Override
    public List<TaskInfoVO> queryTrashList() {
        List<TaskInfoVO> taskInfoVOList;
        if (workAuthUtil.isWorkAdmin()) {
            taskInfoVOList = getBaseMapper().queryTrashList(null);
        } else {
            taskInfoVOList = getBaseMapper().queryTrashList(UserUtil.getUserId());
        }
        taskListTransfer(taskInfoVOList);
        return taskInfoVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(Long taskId) {
        WorkTask task = getById(taskId);
        if (task == null) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_EXIST_ERROR);
        }
        if (task.getIshidden() != 1) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_IN_TRASH_ERROR);
        }
        if (StrUtil.isNotEmpty(task.getBatchId())) {
            adminFileService.delete(task.getBatchId());
        }
//        Db.delete("delete from wk_crm_activity where type = 2 and activity_type = 11 and activity_type_id = ?",taskId);
        removeById(taskId);
        lambdaUpdate().eq(WorkTask::getPid, taskId).remove();

    }

    @Override
    public void restore(Long taskId) {
        WorkTask task = getById(taskId);
        if (task == null) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_EXIST_ERROR);
        }
        if (task.getIshidden() != 1) {
            throw new CrmException(WorkCodeEnum.WORK_TASK_IN_TRASH_ERROR);
        }
        int count = workTaskClassService.count(new QueryWrapper<WorkTaskClass>().eq("class_id", task.getClassId()));
        if (count > 0) {
            update(null, Wrappers.<WorkTask>lambdaUpdate().set(WorkTask::getIshidden, 0).set(WorkTask::getHiddenTime, null).eq(WorkTask::getTaskId, taskId));
        } else {
            update(null, Wrappers.<WorkTask>lambdaUpdate().set(WorkTask::getClassId, -1).set(WorkTask::getIshidden, 0).set(WorkTask::getHiddenTime, null).eq(WorkTask::getTaskId, taskId));
        }
    }

    @Override
    public OaTaskListVO queryTaskList(OaTaskListBO oaTaskListBO) {
        Long userId = oaTaskListBO.getUserId();
        Integer mold = oaTaskListBO.getMold();
        List<Long> userIds = new ArrayList<>();
        if (mold == null) {
            userIds.add(UserUtil.getUserId());
        } else if (mold == 1 && userId == null) {
            userIds = adminService.queryChildUserId(UserUtil.getUserId()).getData();
        } else {
            List<Long> list = adminService.queryChildUserId(UserUtil.getUserId()).getData();
            for (Long id : list) {
                if (id.equals(userId)) {
                    userIds.add(userId);
                }
            }
        }
        if (UserUtil.isAdmin() && oaTaskListBO.getType() == 0 && mold == null) {
            userIds = adminService.queryUserList(1).getData();
        }
        if (userIds.size() == 0) {
            return new OaTaskListVO(0, 0, new BasePage<>(), null);
        } else {
            OaTaskListVO oaTaskListVO = getBaseMapper().queryTaskCount(oaTaskListBO, userIds);
            if (oaTaskListBO.getIsExport() != null && oaTaskListBO.getIsExport()) {
                List<JSONObject> recordList = getBaseMapper().getTaskListExport(oaTaskListBO, userIds);
                // 查询关系idsMap
                Set<Long> taskId = recordList.stream().map(r -> r.getLong("taskId")).collect(Collectors.toSet());
                // 查询crm关系map
                Map<Long, CrmRelationDTO> crmRelationMap = getCrmRelationMap(taskId);
                List<Map<String, Object>> list = new ArrayList<>();
                recordList.forEach(r -> {
                    String ownerUserName = UserCacheUtil.getSimpleUsers(TagUtil.toLongSet(r.getString("ownerUserId")))
                            .stream().map(SimpleUser::getRealname).collect(Collectors.joining(","));
                    r.put("ownerUserName", ownerUserName);
                    String mainUserName = UserCacheUtil.getUserName(r.getLong("mainUserId"));
                    r.put("mainUserName", mainUserName);
                    String createUserName = UserCacheUtil.getUserName(r.getLong("createUserId"));
                    r.put("createUserName", createUserName);
                    // 拼接关联业务内容
                    CrmRelationDTO crmRelationDTO = crmRelationMap.get(r.getLong("taskId"));
                    String relateCrmWork = CrmRelationUtils.getRelateCrmWork(crmRelationDTO);
                    r.put("relateCrmWork", relateCrmWork);
                    list.add(r.getInnerMap());
                });
                oaTaskListVO.setExportList(list);
                return oaTaskListVO;
            }
            BasePage<TaskInfoVO> page = getBaseMapper().getTaskList(oaTaskListBO.parse(), oaTaskListBO, userIds);
            page.setList(queryUser(page.getList()));
            return oaTaskListVO.setPage(page);
        }
    }


    /**
     * 获取关系数量map
     *
     * @param taskIds ids
     * @return java.util.Map<java.lang.Long, java.lang.Integer>
     * <AUTHOR> sir
     * @date 2021/11/23
     */
    private Map<Long, Integer> getTaskRelationCount(Collection<Long> taskIds) {
        List<WorkTaskRelation> workTaskRelations = getWorkTaskRelations(taskIds);
        Map<Long, List<WorkTaskRelation>> relationMap = workTaskRelations
                .stream()
                .collect(Collectors.groupingBy(WorkTaskRelation::getTaskId));
        Map<Long, Integer> relationCountMap = new HashMap<>(relationMap.size());
        relationMap.forEach((k, v) -> relationCountMap.put(k, v.size()));
        return relationCountMap;
    }


    /**
     * 查询crm关系map
     *
     * @param taskIds ids
     * @return java.util.Map<java.lang.Long, com.kakarote.core.entity.CrmRelationDTO>
     * <AUTHOR> sir
     * @date 2021/11/23
     */
    @Override
    public Map<Long, CrmRelationDTO> getCrmRelationMap(Collection<Long> taskIds) {
        List<WorkTaskRelation> workTaskRelations = getWorkTaskRelations(taskIds);
        // 获取日志关系map
        Map<Integer, Map<Long, Set<Long>>> taskRelationIdsMap = workTaskRelations
                .stream()
                .collect(Collectors.groupingBy(WorkTaskRelation::getType,
                        Collectors.groupingBy(WorkTaskRelation::getTaskId, Collectors.mapping(WorkTaskRelation::getRelationId, Collectors.toSet()))
                ));
        // 获取map
        return CrmRelationUtils.getCrmRelationMap(taskRelationIdsMap, taskIds);
    }

    /***
     * 查询crm关系列表
     *
     * @param taskIds taskIds
     * @return java.util.List<com.kakarote.work.entity.PO.WorkTaskRelation>
     * <AUTHOR> sir
     * @date 2021/11/23
     */
    private List<WorkTaskRelation> getWorkTaskRelations(Collection<Long> taskIds) {
        if(CollUtil.isEmpty(taskIds)){
            return Collections.emptyList();
        }
        // 查询关系
        return workTaskRelationService
                .lambdaQuery()
                .select(WorkTaskRelation::getRelationId, WorkTaskRelation::getType, WorkTaskRelation::getTaskId)
                .in(WorkTaskRelation::getTaskId, taskIds)
                .list();
    }

    private List<TaskInfoVO> queryUser(List<TaskInfoVO> tasks) {
        // 查询taskIds
        List<Long> taskIds = tasks.stream().map(TaskInfoVO::getTaskId).collect(Collectors.toList());
        // 查询relation count map
        Map<Long, Integer> taskRelationCountMap = getTaskRelationCount(taskIds);

        ArrayList<TaskLabelBO> labelList;
        for (TaskInfoVO task : tasks) {
            Long mainUserId = task.getMainUserId();
            if (mainUserId != null) {
                SimpleUser mainUser = UserCacheUtil.getSimpleUser(mainUserId);
                task.setMainUser(mainUser);
            }
            labelList = new ArrayList<>();
            if (StrUtil.isNotBlank(task.getLabelId())) {
                List<String> list = StrUtil.splitTrim(task.getLabelId(), Const.SEPARATOR);
                List<WorkTaskLabel> taskLabelList = workTaskLabelService.query().select("label_id", "name", "color").in("label_id", list).list();
                List<TaskLabelBO> collect = taskLabelList.stream().map(label -> {
                    TaskLabelBO taskLabelBO = new TaskLabelBO();
                    taskLabelBO.setColor(label.getColor());
                    taskLabelBO.setLabelName(label.getName());
                    taskLabelBO.setLabelId(label.getLabelId());
                    return taskLabelBO;
                }).collect(Collectors.toList());
                labelList.addAll(collect);
            }
            if (StrUtil.isNotBlank(task.getOwnerUserId())) {
                List<Long> ids = StrUtil.splitTrim(task.getOwnerUserId(), Const.SEPARATOR).stream().map(Long::valueOf).collect(Collectors.toList());
                if (ids.size() > 0) {
                    task.setOwnerUserList(UserCacheUtil.getSimpleUsers(ids));
                }
            }
            // 设置关系数量
            task.setRelationCount(taskRelationCountMap.getOrDefault(task.getTaskId(), 0));
            if (task.getStopTime() != null) {
                //设置开始时间
                if (task.getStopTime().isBefore(LocalDate.now()) && task.getStatus() != 5 && task.getStatus() != 2) {
                    task.setIsEnd(1);
                } else {
                    task.setIsEnd(0);
                }
            } else {
                task.setIsEnd(0);
            }
            task.setLabelList(labelList);

        }
        return tasks;
    }

    private Integer queryCount(Integer start, String str) {
        // start 开始个数
        if (str != null) {
            String[] ownerUserIds = str.split(",");
            for (String ownerUserId : ownerUserIds) {
                if (StrUtil.isNotBlank(ownerUserId)) {
                    ++start;
                }
            }
        }
        return start;
    }

    @Override
    public List<Map<String, Object>> workBenchTaskExport() {
        Dict kv = Dict.create().set("userId", UserUtil.getUserId());
        List<JSONObject> recordList = getBaseMapper().myTaskExport(kv);
        List<Map<String, Object>> list = new ArrayList<>();
        // 获取taskIds
        List<Long> taskId = recordList.stream().map(r -> r.getLong("taskId")).collect(Collectors.toList());
        // 获取crmRelationMap
        Map<Long, CrmRelationDTO> crmRelationMap = getCrmRelationMap(taskId);
        // 遍历数据
        recordList.forEach(record -> {
            Long createUserId = record.getLong("createUserId");
            record.put("createUserName", UserCacheUtil.getUserName(createUserId));
            Long mainUserId = record.getLong("mainUserId");
            record.put("mainUserName", UserCacheUtil.getUserName(mainUserId));
            String ownerUserId = record.getString("ownerUserId");
            record.put("ownerUserName", UserCacheUtil.getSimpleUsers(TagUtil.toLongSet(ownerUserId)).stream().map(SimpleUser::getRealname).collect(Collectors.joining(",")));
            // 拼接关联业务内容
            CrmRelationDTO crmRelationDTO = crmRelationMap.get(record.getLong("taskId"));
            String relateCrmWork = CrmRelationUtils.getRelateCrmWork(crmRelationDTO);
            record.put("relateCrmWork", relateCrmWork);
            list.add(record.getInnerMap());
        });
        return list;
    }
}
