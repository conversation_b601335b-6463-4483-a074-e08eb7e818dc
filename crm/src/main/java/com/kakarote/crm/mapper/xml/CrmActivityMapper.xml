<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmActivityMapper">
    <select id="getCrmActivityPageList" resultType="com.kakarote.crm.entity.VO.CrmActivityVO">
        select a.id,a.category,a.type,a.activity_id,a.activity_type,a.activity_type_id,a.content,a.next_time,a.create_user_id,a.create_time,a.time_type,a.batch_id,a.is_relation
        from wk_crm_activity as a
        where  a.status = 1
        <choose>
            <when test="data.recordType!=null">
                and a.type = #{data.recordType}
            </when>
            <when test="data.queryType == 1">
                and a.type in (1,4)
            </when>
            <when test="data.queryType == 2">
                and a.type in (2,3)
            </when>
        </choose>
        <if test="data.activityTypeId">
            and a.activity_id = #{data.activityTypeId}
        </if>
        <if test="data.activityType != null">
            and a.activity_type = #{data.activityType}
        </if>
        <if test="data.isOa">
            and a.activity_id = a.activity_type_id
        </if>
        <if test="timeEntity.beginDate != null and timeEntity.endDate != null">
            and a.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        </if>
        <if test="data.content != null and data.content != ''">
            and a.content like concat('%', #{data.content}, '%')
        </if>
        <if test="timeEntity.userIds != null and timeEntity.userIds.size > 0 ">
            and a.create_user_id in
            <foreach collection="timeEntity.userIds" item="userId" index="index" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        order by a.create_time desc
    </select>
    <update id="updateNextTime">
        update wk_crm_${tableName} set
        update_time = #{updateTime}
        <choose>
            <when test="idDel and lastContent == null">
                ,last_time = create_time
            </when>
            <otherwise>
                ,last_time = #{lastTime}
            </otherwise>
        </choose>
        <if test="label == 1 or label == 2 or label == 5">
            ,followup = #{followup}
        </if>
        <if test="label == 1 or label == 2">
            ,last_content = #{lastContent}
        </if>
        <if test="label == 1 or label == 2 or label == 3 or label == 5">
            ,next_time = #{nextTime}
        </if>
        where ${primaryKey} = #{tableId}
    </update>

    <update id="updateRelationNextTime">
        update wk_crm_${tableName} set
        update_time = #{updateTime},next_time = #{nextTime}
        where ${primaryKey} in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryOutworkStats" resultType="com.alibaba.fastjson.JSONObject">
        select b.user_id,b.realname,b.img,count(1) as count
        from wk_crm_activity as a inner join
        wk_admin_user as b on a.create_user_id = b.user_id
        where a.type = 4
        and b.user_id in
        <foreach collection="userIds" item="userId" index="index" separator="," close=")" open="(">
            #{userId}
        </foreach>
        and a.create_time between #{startTime} and #{endTime} group by a.create_user_id
    </select>
    <select id="queryRelationData" resultType="com.alibaba.fastjson.JSONObject">
        select a.type_id id, a.`type`,
            (
            case `type`
            when 3 then (select `name` from `wk_crm_contacts` where contacts_id = a.type_id)
            when 5 then (select business_name from `wk_crm_business` where business_id = a.type_id)
            when 2 then (select `name` from `wk_crm_contacts` where contacts_id = a.type_id)
            when 4 then (select `name` from `wk_crm_product` where product_id = a.type_id)
            when 6 then (select `name` from `wk_crm_contract` where contract_id = a.type_id)
            end
            ) name
        from wk_crm_activity_relation a where activity_id = #{activityId}
    </select>
    <select id="queryBusinessProducts" resultType="com.alibaba.fastjson.JSONObject">
        select b.product_id id,b.name from wk_crm_business_product a left join wk_crm_product b on a.product_id = b.product_id
        where  a.business_id = #{id}
    </select>
    <select id="querySubtasks" resultType="com.alibaba.fastjson.JSONObject">
        select task_id id,`name` from wk_work_task where  pid = #{id}
    </select>
    <select id="queryOutworkList" resultType="com.kakarote.crm.entity.VO.CrmActivityVO">
        select b.user_id,b.realname,b.img as userImg,a.*
        from wk_crm_activity as a
        left join wk_admin_user as b on a.create_user_id = b.user_id
        where a.type = 4 and a.create_user_id = #{userId}
        <if test="startTime!=null and startTime!=''">
            and a.create_time between #{startTime} and #{endTime}
        </if>
        order by create_time desc
    </select>
    <select id="queryRecordList" resultType="com.kakarote.crm.entity.VO.CrmActivityVO">
        select a.id,a.category,a.type,a.activity_id,a.activity_type,a.activity_type_id,a.content,a.next_time,a.create_user_id,a.create_time,a.time_type,a.batch_id,a.is_relation
        from wk_crm_activity as a
        where a.create_user_id in
        <foreach collection="biTimeEntity.userIds" index="index" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
        and a.status = 1 and a.activity_id = a.activity_type_id
        <choose>
            <when test="data.recordType == 1">
                and a.type = 1
            </when>
            <when test="data.recordType == 4">
                and a.type = 4
            </when>
            <when test="data.recordType == 0">
                and a.type in (1,4)
            </when>
        </choose>
        <if test="data.crmType != null">
            and a.activity_type = #{data.crmType}
        </if>
        <if test="data.content != null">
            and a.content like concat('%',#{data.content},'%')
        </if>
        <if test="biTimeEntity.beginDate != null and biTimeEntity.endDate != null">
            and a.create_time between #{biTimeEntity.beginDate} and #{biTimeEntity.endDate}
        </if>
        order by a.create_time desc
    </select>
</mapper>
