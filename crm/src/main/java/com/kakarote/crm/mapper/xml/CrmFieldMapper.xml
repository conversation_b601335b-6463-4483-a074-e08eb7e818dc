<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmFieldMapper">
    <select id="verifyFixedField" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM wk_crm_${tableName} WHERE ${fieldName}=#{value}
        <if test="label==2||label==5||label==4">
            and status != 3
        </if>
        <if test="label==6||label==7">
            and check_status != 7
        </if>
        <if test="batchId!=null and batchId!=''">
            and batch_id!=#{batchId}
        </if>
    </select>

    <select id="verifyField" resultType="java.lang.Integer">
        SELECT COUNT(*)
        <if test="label==1">
            FROM wk_crm_leads_data
        </if>
        <if test="label==2">
            FROM wk_crm_customer_data
        </if>
        <if test="label==3">
            FROM wk_crm_contacts_data
        </if>
        <if test="label==4">
            FROM wk_crm_product_data
        </if>
        <if test="label==5">
            FROM wk_crm_business_data
        </if>
        <if test="label==6">
            FROM wk_crm_contract_data
        </if>
        <if test="label==7">
            FROM wk_crm_receivables_data
        </if>
        <if test="label==8">
            FROM wk_crm_receivables_plan_data
        </if>
        <if test="label==17">
            FROM wk_crm_return_visit_data
        </if>
        <if test="label==18">
            FROM wk_crm_invoice_data
        </if>
        WHERE field_id = #{fieldId} and value = #{value}
        <if test="label==2||label==5||label==4">
            and status != 3
        </if>
        <if test="label==6||label==7">
            and check_status != 7
        </if>
        <if test="batchId!=null and batchId!=''">
            and batch_id!=#{batchId}
        </if>
        <if test="batchId!=null and batchId!=''">
            and batch_id!=#{batchId}
        </if>
    </select>
    <select id="initData" resultType="java.util.HashMap">
        SELECT *
        <if test="label==1">
            FROM wk_crm_leads WHERE leads_id &gt; #{lastId}
        </if>
        <if test="label==2">
            FROM wk_crm_customer WHERE customer_id &gt; #{lastId}
        </if>
        <if test="label==3">
            FROM wk_crm_contacts WHERE contacts_id &gt; #{lastId}
        </if>
        <if test="label==4">
            FROM wk_crm_product WHERE product_id &gt; #{lastId}
        </if>
        <if test="label==5">
            FROM wk_crm_business WHERE business_id &gt; #{lastId}
        </if>
        <if test="label==6">
            FROM wk_crm_contract WHERE contract_id &gt; #{lastId}
        </if>
        <if test="label==7">
            FROM wk_crm_receivables WHERE receivables_id &gt; #{lastId}
        </if>
        <if test="label==8">
            FROM wk_crm_receivables_plan WHERE receivables_plan_id &gt; #{lastId}
        </if>
        <if test="label==17">
            FROM wk_crm_return_visit WHERE visit_id &gt; #{lastId}
        </if>
        <if test="label==18">
            FROM wk_crm_invoice WHERE invoice_id &gt; #{lastId}
        </if>
        <if test="label==2||label==5||label==4">
            and status != 3
        </if>
        <if test="label==6||label==7">
            and check_status != 7
        </if>
        <if test="label==1">
            order by leads_id asc
        </if>
        <if test="label==2">
            order by customer_id asc
        </if>
        <if test="label==3">
            order by contacts_id asc
        </if>
        <if test="label==4">
            order by product_id asc
        </if>
        <if test="label==5">
            order by business_id asc
        </if>
        <if test="label==6">
            order by contract_id asc
        </if>
        <if test="label==7">
            order by receivables_id asc
        </if>
        <if test="label==8">
            order by receivables_plan_id asc
        </if>
        <if test="label==17">
            order by visit_id asc
        </if>
        <if test="label==18">
            order by invoice_id asc
        </if>

        limit 0,2000
    </select>

    <select id="initFieldData" resultType="com.kakarote.crm.entity.BO.CrmFieldDataBO" resultSetType="FORWARD_ONLY" fetchSize="10000">
        SELECT
        a.id,
        a.batch_id,
        a.field_id,
        a.`name`,
        a.`value`,
        <if test="label==1">
            b.leads_id
        </if>
        <if test="label==2">
            b.customer_id
        </if>
        <if test="label==3">
            b.contacts_id
        </if>
        <if test="label==4">
            b.product_id
        </if>
        <if test="label==5">
            b.business_id
        </if>
        <if test="label==6">
            b.contract_id
        </if>
        <if test="label==7">
            b.receivables_id
        </if>
        <if test="label==8">
            b.receivables_plan_id
        </if>
        <if test="label==17">
            b.visit_id
        </if>
        <if test="label==18">
            b.invoice_id
        </if>
        as typeId,
        c.type
        <if test="label==1">
            FROM wk_crm_leads_data AS a
            JOIN wk_crm_leads AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==2">
            FROM wk_crm_customer_data AS a
            JOIN wk_crm_customer AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==3">
            FROM wk_crm_contacts_data AS a
            JOIN wk_crm_contacts AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==4">
            FROM wk_crm_product_data AS a
            JOIN wk_crm_product AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==5">
            FROM wk_crm_business_data AS a
            JOIN wk_crm_business AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==6">
            FROM wk_crm_contract_data AS a
            JOIN wk_crm_contract AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==7">
            FROM wk_crm_receivables_data AS a
            JOIN wk_crm_receivables AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==8">
            FROM wk_crm_receivables_plan_data AS a
            JOIN wk_crm_receivables_plan AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==17">
            FROM wk_crm_return_visit_data AS a
            JOIN  wk_crm_return_visit AS b ON a.batch_id = b.batch_id
        </if>
        <if test="label==18">
            FROM wk_crm_invoice_data AS a
            JOIN wk_crm_invoice AS b ON a.batch_id = b.batch_id
        </if>
        JOIN wk_crm_field as c on a.field_id = c.field_id
        WHERE
        a.id &gt; #{lastId}  and value is not null
        order by a.id asc
        LIMIT 0,10000
    </select>

    <select id="queryCustomerFieldDuplicateByFixed" resultType="java.lang.Integer">
        select count(1) from `wk_crm_customer` where ${name} = #{value} and status != 3
    </select>
</mapper>
