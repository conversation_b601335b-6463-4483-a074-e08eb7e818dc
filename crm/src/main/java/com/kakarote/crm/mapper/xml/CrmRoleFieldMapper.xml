<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmRoleFieldMapper">

    <select id="queryRoleFieldList" resultType="com.kakarote.crm.entity.PO.CrmRoleField">
        SELECT
            a.*,
            b.type
        FROM
            wk_crm_role_field as a
            LEFT JOIN wk_crm_field as b on a.field_id = b.field_id
        WHERE
        a.role_id = #{roleId} and a.label = #{label}
    </select>

    <select id="queryMaskFieldList" resultType="com.kakarote.crm.entity.PO.CrmRoleField">
        SELECT
            a.field_name,
            max( a.mask_type ) AS mask_type,
            b.type
        FROM
            wk_crm_role_field as a
            JOIN wk_crm_field as b on a.field_id = b.field_id
        WHERE
            a.mask_type >= #{maskType} and a.label = #{label}
            and a.role_id in
        <foreach collection="ids" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY field_name
    </select>
</mapper>
