package com.kakarote.crm.mapper;

import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmCustomerSuperior;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-07
 */
public interface CrmCustomerSuperiorMapper extends BaseMapper<CrmCustomerSuperior> {

    public List<Long> querySuperiorCustomerId(@Param("customerId") Long customerId);

    public List<Long> querySubordinateCustomerId(@Param("customerId") Long customerId);
}
