package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 阶段流程主信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_flow")
@ApiModel(value="CrmFlow对象", description="阶段流程主信息表")
public class CrmFlow implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "阶段流程ID")
    @TableId(value = "flow_id",type = IdType.ASSIGN_ID)
    private Long flowId;

    @ApiModelProperty(value = "阶段流程名称")
    private String flowName;

    @ApiModelProperty(value = "成功阶段的名称")
    private String successName;

    @ApiModelProperty(value = "失败阶段的名称")
    private String failedName;

    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    @ApiModelProperty(value = "适用员工")
    private String userIds;

    @ApiModelProperty(value = "关联业务对象 1 线索 2 客户 3 联系人 4 产品 5 商机 6 合同 7回款8.回款计划")
    private Integer label;

    @ApiModelProperty(value = "状态 1 正常 2 停用 3 废弃")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    @ApiModelProperty("部门列表")
    private List<String> deptNameList;

    @TableField(exist = false)
    @ApiModelProperty("用户列表")
    private List<String> userNameList;

    @TableField(exist = false)
    @ApiModelProperty("创建人")
    private String createUserName;

    @TableField(exist = false)
    @ApiModelProperty("修改人")
    private String updateUserName;

}
