package com.kakarote.crm.entity.VO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName: CrmReceivablesPlanVO
 * @Author: Blue
 * @Description: CrmReceivablesPlanVO
 * @Date: 2021/11/16 16:41
 */
@Data
@ToString
@Accessors(chain = true)
@ApiModel("crm需要的自定义字段对象")
public class CrmReceivablesPlanVO {

    private Long receivablesPlanId;

    @ApiModelProperty(value = "期数")
    private String num;

    @ApiModelProperty(value = "回款ID")
    private Long receivablesId;

    @ApiModelProperty(value = "1完成 0 未完成")
    private Integer status;

    @ApiModelProperty(value = "计划回款金额")
    private BigDecimal money;

    @ApiModelProperty(value = "计划回款日期")
    private LocalDateTime returnDate;

    @ApiModelProperty(value = "计划回款方式")
    private String returnType;

    @ApiModelProperty(value = "提前几天提醒")
    private Integer remind;

    @ApiModelProperty(value = "提醒日期")
    private LocalDateTime remindDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建人ID")
    private String createUserName;

    @ApiModelProperty(value = "负责人ID")
    private Long ownerUserId;

    @ApiModelProperty(value = "负责人ID")
    private String ownerUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "附件批次ID")
    private String batchId;

    @ApiModelProperty(value = "实际回款金额")
    private BigDecimal realReceivedMoney;

    @ApiModelProperty(value = "实际回款日期")
    private LocalDateTime realReturnDate;

    @ApiModelProperty(value = "未回款金额")
    private BigDecimal unreceivedMoney;

    @ApiModelProperty(value = "回款状态 0 待回款 1 回款完成 2 部分回款 3 作废 4 逾期 5 待生效")
    private String receivedStatus;

    @ApiModelProperty(value = "合同ID")
    private Long contractId;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    @TableField(exist = false)
    private String customerName;

    @ApiModelProperty(value = "合同期数")
    @TableField(exist = false)
    private String contractNum;
}
