package com.kakarote.crm.entity.BO;

import com.kakarote.core.feign.crm.entity.BiEntityParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 销售简报查询
 * @description:
 * @author: zyy
 * @date: 2022-01-05
 */
@Data
@ApiModel("销售简报查询")
public class CrmInstrumentQueryBO extends BiEntityParams {

    @ApiModelProperty("crm类型")
    private Integer label;

    @ApiModelProperty("大搜索框内容")
    private String search;

    @ApiModelProperty("审批筛选  有审批流程时使用")
    private Integer checkStatus;

    @ApiModelProperty("类型")
    private Long categoryId;

}
