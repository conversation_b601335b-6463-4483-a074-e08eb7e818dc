package com.kakarote.crm.entity.BO;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@ApiModel("产品类型")
public class CrmProductCategoryBO {

    @ApiModelProperty("产品类型ID")
    private Long categoryId;

    @ApiModelProperty("产品类型上级ID")
    private Long parentId;

    @ApiModelProperty("产品类型名称")
    private String name;

    @ApiModelProperty("label")
    private String label;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.label = name;
    }

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<CrmProductCategoryBO> children;
}
