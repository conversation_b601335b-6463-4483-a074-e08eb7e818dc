package com.kakarote.crm.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@ApiModel("查询字段列表")
public class CrmPrintFieldVO {

    @ApiModelProperty("商机字段列表")
    private List<CrmModelFiledVO> business = Collections.emptyList();

    @ApiModelProperty("合同字段列表")
    private List<CrmModelFiledVO> contract = Collections.emptyList();

    @ApiModelProperty("联系人字段列表")
    private List<CrmModelFiledVO> contacts = Collections.emptyList();

    @ApiModelProperty("回款字段列表")
    private List<CrmModelFiledVO> receivables = Collections.emptyList();

    @ApiModelProperty("客户字段列表")
    private List<CrmModelFiledVO> customer = Collections.emptyList();

    @ApiModelProperty("产品字段列表")
    private List<CrmModelFiledVO> product = Collections.emptyList();
}
