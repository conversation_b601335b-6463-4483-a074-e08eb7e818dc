package com.kakarote.crm.entity.BO;

import com.kakarote.core.entity.PageEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CrmQueryRecordPageListBO extends PageEntity {
    private String type;
    private Long userId;
    private Long deptId;
    private String startTime;
    private String endTime;
    private Integer isUser;


    private Integer dataType;
    private String crmType;
    private Integer queryType;
    private Integer subUser;
    private String search;

    @Override
    public String toString() {
        return "CrmQueryRecordPageListBO{" +
                "type='" + type + '\'' +
                ", userId=" + userId +
                ", deptId=" + deptId +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", isUser=" + isUser +
                ", dataType=" + dataType +
                ", crmType='" + crmType + '\'' +
                ", queryType=" + queryType +
                ", subUser=" + subUser +
                ", search='" + search + '\'' +
                '}';
    }
}
