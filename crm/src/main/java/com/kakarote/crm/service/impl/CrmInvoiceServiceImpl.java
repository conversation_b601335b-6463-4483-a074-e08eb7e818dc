package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.feign.examine.entity.ExamineRecordReturnVO;
import com.kakarote.core.feign.examine.entity.ExamineRecordSaveBO;
import com.kakarote.core.feign.examine.service.ExamineService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.AdminMessageUtil;
import com.kakarote.core.utils.BaseUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.common.ResourcesUtil;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmBackLogEnum;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmChangeOwnerUserBO;
import com.kakarote.crm.entity.BO.CrmContractSaveBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.BO.CrmUpdateInformationBO;
import com.kakarote.crm.entity.PO.CrmContract;
import com.kakarote.crm.entity.PO.CrmInvoice;
import com.kakarote.crm.entity.PO.CrmInvoiceData;
import com.kakarote.crm.entity.PO.CrmInvoiceInfo;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmInvoiceMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.kakarote.core.servlet.ApplicationContextHolder.getBean;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-06
 */
@Service
@Slf4j
public class CrmInvoiceServiceImpl extends BaseServiceImpl<CrmInvoiceMapper, CrmInvoice> implements ICrmInvoiceService, CrmPageService {

    @Autowired
    private ICrmInvoiceInfoService crmInvoiceInfoService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    @Lazy
    private ExamineService examineService;

    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmInvoiceDataService crmInvoiceDataService;

    @Autowired
    private FieldService fieldService;

    @Autowired
    private Redis redis;

    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        return queryList(search, false);
    }

    private static final String CUSTOMER_ID = "customerId";

    private static final String CONTRACT_ID = "contractId";

    /**
     * 补充审批字段信息
     *
     * @param examineRecordSaveBO
     * @return void
     * @date 2020/12/18 13:44
     **/
    private void supplementFieldInfo(Long typeId, Long recordId, ExamineRecordSaveBO examineRecordSaveBO) {
        examineRecordSaveBO.setLabel(3);
        examineRecordSaveBO.setTypeId(typeId);
        examineRecordSaveBO.setRecordId(recordId);
        if (examineRecordSaveBO.getDataMap() != null) {
            examineRecordSaveBO.getDataMap().put("createUserId", UserUtil.getUserId());
        } else {
            Map<String, Object> entityMap = new HashMap<>(1);
            entityMap.put("createUserId", UserUtil.getUserId());
            examineRecordSaveBO.setDataMap(entityMap);
        }
    }

    @Override
    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<CrmInvoice> list = lambdaQuery().select(CrmInvoice::getInvoiceId, CrmInvoice::getInvoiceApplyNumber).in(CrmInvoice::getInvoiceId, ids).list();
        return list.stream().map(crmInvoice -> {
            SimpleCrmEntity simpleCrmEntity = new SimpleCrmEntity();
            simpleCrmEntity.setId(crmInvoice.getInvoiceId());
            simpleCrmEntity.setName(crmInvoice.getInvoiceApplyNumber());
            return simpleCrmEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public CrmInvoice queryById(Long invoiceId) {
        CrmInvoice crmInvoice = getBaseMapper().queryById(invoiceId);
        if (!ObjectUtil.isEmpty(crmInvoice.getContactsAddress())) {
            crmInvoice.setContactsAddress(JSONObject.parseArray(crmInvoice.getContactsAddress().toString()));
        }
        return crmInvoice;
    }

    @Override
    @Message(label = CrmMsgLabelEnum.invoice, action = CrmMsgActionEnum.updateInvoiceStatus)
    public void updateInvoiceStatus(CrmInvoice crmInvoice) {
        CrmInvoice invoice = new CrmInvoice();
        invoice.setInvoiceId(crmInvoice.getInvoiceId());
        invoice.setInvoiceStatus(1);
        invoice.setInvoiceNumber(crmInvoice.getInvoiceNumber());
        invoice.setRealInvoiceDate(crmInvoice.getRealInvoiceDate());
        invoice.setLogisticsNumber(crmInvoice.getLogisticsNumber());
        updateById(invoice);
        Map<String, Object> objectMap = BeanUtil.beanToMap(invoice, false, true);
        objectMap.put("realInvoiceDate", LocalDateTimeUtil.formatNormal(crmInvoice.getRealInvoiceDate()));
        objectMap.put("updateTime", DateUtil.formatLocalDateTime(invoice.getUpdateTime()));
        updateField(objectMap, Collections.singletonList(invoice.getInvoiceId()));

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.updateInvoiceStatus.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m8", UserUtil.getUser().getRealname(), crmInvoice.getInvoiceNumber());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        for (Long id : ids) {

            CrmInvoice crmInvoice = getById(id);
            if (crmInvoice == null) {
                continue;
            }
            boolean bol = (crmInvoice.getCheckStatus() != 4 && crmInvoice.getCheckStatus() != 5) && (!UserUtil.getUserId().equals(UserUtil.getSuperUser()) && !UserUtil.getUser().getRoles().contains(UserUtil.getSuperRole()));
            if (bol) {
                throw new CrmException(CrmCodeEnum.CAN_ONLY_DELETE_WITHDRAWN_AND_SUBMITTED_EXAMINE);
            }
            ApplicationContextHolder.getBean(AdminFileService.class).delete(Collections.singletonList(crmInvoice.getBatchId()));
            if (ObjectUtil.isNotEmpty(crmInvoice.getExamineRecordId())) {
                examineService.deleteExamineRecord(crmInvoice.getExamineRecordId());
            }
            crmInvoiceDataService.deleteByBatchId(Collections.singletonList(crmInvoice.getBatchId()));
            removeById(crmInvoice.getInvoiceId());
        }
        deletePage(ids);
    }

    @Override
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO) {
        Long ownerUserId = changeOwnerUserBO.getOwnerUserId();
        LambdaUpdateWrapper<CrmInvoice> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(CrmInvoice::getInvoiceId, changeOwnerUserBO.getIds()).set(CrmInvoice::getOwnerUserId, ownerUserId);
        update(lambdaUpdateWrapper);
        for (Long id : changeOwnerUserBO.getIds()) {
            CrmInvoice invoice = getById(id);
            actionRecordUtil.addConversionRecord(id, CrmEnum.INVOICE, ownerUserId, invoice.getInvoiceApplyNumber());
        }
        //修改es
        Map<String, Object> map = new HashMap<>();
        map.put("ownerUserId", changeOwnerUserBO.getOwnerUserId());
        map.put("ownerUserName", UserCacheUtil.getUserName(ownerUserId));
        updateField(map, changeOwnerUserBO.getIds());
    }

    @Override
    public List<FileEntity> queryFileList(Long id) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmInvoice crmInvoice = getById(id);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmInvoice.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        return fileEntityList;
    }

    @Override
    public void saveInvoiceInfo(CrmInvoiceInfo crmInvoiceInfo) {
        boolean crmAuth = AuthUtil.isCrmAuth(CrmEnum.CUSTOMER, crmInvoiceInfo.getCustomerId(), CrmAuthEnum.LIST);
        if (crmAuth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        crmInvoiceInfo.setCreateUserId(UserUtil.getUserId()).setCreateTime(LocalDateTimeUtil.now());
        crmInvoiceInfoService.save(crmInvoiceInfo);
    }

    @Override
    public void updateInvoiceInfo(CrmInvoiceInfo crmInvoiceInfo) {
        boolean auth = AuthUtil.isRwAuth(crmInvoiceInfo.getCustomerId(), CrmEnum.CUSTOMER, CrmAuthEnum.EDIT);
        if (auth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        int two = 2;
        if (crmInvoiceInfo.getTitleType() == two) {
            crmInvoiceInfo.setTaxNumber("");
            crmInvoiceInfo.setDepositAccount("");
            crmInvoiceInfo.setDepositBank("");
            crmInvoiceInfo.setDepositAddress("");
        }
        crmInvoiceInfoService.updateById(crmInvoiceInfo);
    }

    @Override
    public void deleteInvoiceInfo(Long infoId) {
        CrmInvoiceInfo invoiceInfo = crmInvoiceInfoService.getById(infoId);
        boolean auth = AuthUtil.isRwAuth(invoiceInfo.getCustomerId(), CrmEnum.CUSTOMER, CrmAuthEnum.DELETE);
        if (auth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        crmInvoiceInfoService.removeById(infoId);

    }


    @Override
    public void setOtherField(Map<String, Object> map) {
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
        String customerName = ApplicationContextHolder.getBean(ICrmCustomerService.class).getCustomerName(Convert.toLong(map.get("customerId")));
        map.put("customerName", customerName);
        CrmContract contract = ApplicationContextHolder.getBean(ICrmContractService.class).getById((Serializable) map.get("contractId"));
        if (contract != null) {
            map.put("contractNum", contract.getNum());
            map.put("contractMoney", contract.getMoney());
            map.put("contractName", contract.getName());
        }
    }

    /**
     * 获取crm列表类型
     *
     * @return data
     */
    @Override
    public CrmEnum getLabel() {
        return CrmEnum.INVOICE;
    }

    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("ownerUserName", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("createUserName", FieldEnum.TEXT, 1));
        return filedList;
    }


    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryByIds(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            if (crmModel.get(CUSTOMER_ID) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            if (crmModel.get(CONTRACT_ID) != null) {
                crmModel.put("contractId", Collections.singletonList(new JSONObject().fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum")).fluentPut("contractMoney", crmModel.get("contractMoney"))));
            } else {
                crmModel.put("contractId", new ArrayList<>());
            }
        }
        List<CrmModelFiledVO> vos = crmFieldService.queryField(crmModel);
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            vos.addAll(modelFiledVOS);
        }
        return vos;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryByIds(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            if (crmModel.get(CUSTOMER_ID) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            if (crmModel.get(CONTRACT_ID) != null) {
                crmModel.put("contractId", Collections.singletonList(new JSONObject().fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum")).fluentPut("contractMoney", crmModel.get("contractMoney"))));
            } else {
                crmModel.put("contractId", new ArrayList<>());
            }
            //去除编辑掩码
            crmModel.put("update", true);
        }
        return crmFieldService.queryFormPositionFieldVO(crmModel);
    }

    @Override
    public List<CrmModelFiledVO> information(Long contractId) {
        return queryField(contractId, true);
    }


    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(CrmContractSaveBO crmModel, boolean isExcel) {
        CrmInvoice crmInvoice = BeanUtil.copyProperties(crmModel.getEntity(), CrmInvoice.class);
        boolean crmAuth = AuthUtil.isCrmAuth(CrmEnum.CUSTOMER, crmInvoice.getCustomerId(), CrmAuthEnum.LIST);
        if (crmAuth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        String batchId = StrUtil.isNotEmpty(crmInvoice.getBatchId()) ? crmInvoice.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_invoice_data"));
        crmInvoiceDataService.saveData(crmModel.getField(), batchId);
        // 是否新建
        Boolean isAdd = false;
        if (!ObjectUtil.isEmpty(crmInvoice.getContactsAddress())) {
            crmInvoice.setContactsAddress(JSONObject.toJSONString(crmInvoice.getContactsAddress()));
        }
        Long invoiceId = crmInvoice.getInvoiceId() == null ? BaseUtil.getNextId() : crmInvoice.getInvoiceId();

        //把数据放入缓存，供系统消息展示预览数据使用
        redis.setNx(CrmCacheKey.CRM_ADMIN_MESSAGE + invoiceId, 60L, BeanUtil.beanToMap(crmInvoice));
        if (crmInvoice.getInvoiceId() != null) {
            CrmInvoice oldInvoice = getById(crmInvoice.getInvoiceId());
            if (oldInvoice.getCheckStatus() == 1) {
                throw new CrmException(CrmCodeEnum.CRM_INVOICE_EXAMINE_PASS_ERROR);
            }
            int two = 2;
            int four = 4;
            int five = 5;
            int ten = 10;
            if (!Arrays.asList(two, four, five, ten).contains(oldInvoice.getCheckStatus())) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
            }
            if (crmInvoice.getTitleType() == two) {
                crmInvoice.setTaxNumber(null);
                crmInvoice.setDepositAccount(null);
                crmInvoice.setDepositAddress(null);
                crmInvoice.setDepositBank(null);
            }
            ExamineRecordSaveBO examineRecordSaveBO = crmModel.getExamineFlowData();
            this.supplementFieldInfo(oldInvoice.getInvoiceId(), oldInvoice.getExamineRecordId(), examineRecordSaveBO);
            examineRecordSaveBO.setTitle(crmInvoice.getInvoiceApplyNumber());
            ExamineRecordReturnVO crmExamineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
            crmInvoice.setExamineRecordId(crmExamineData.getRecordId());
            crmInvoice.setCheckStatus(crmExamineData.getExamineStatus());
            Map<String, Object> oldInvoiceMap = BeanUtil.beanToMap(oldInvoice);
            Map<String, Object> newInvoiceMap = BeanUtil.beanToMap(crmInvoice);
            String invoiceType = "invoiceType";
            if (oldInvoiceMap.containsKey(invoiceType)) {
                oldInvoiceMap.put("invoiceType", parseInvoiceType(TypeUtils.castToInt(oldInvoiceMap.get("invoiceType"))));
            }
            if (newInvoiceMap.containsKey(invoiceType)) {
                newInvoiceMap.put("invoiceType", parseInvoiceType(TypeUtils.castToInt(newInvoiceMap.get("invoiceType"))));
            }
            ApplicationContextHolder.getBean(ICrmBackLogDealService.class).deleteByTypes(null, CrmEnum.INVOICE, crmInvoice.getInvoiceId(), CrmBackLogEnum.CHECK_INVOICE);
            actionRecordUtil.updateRecord(oldInvoiceMap, newInvoiceMap, CrmEnum.INVOICE, crmInvoice.getInvoiceApplyNumber(), crmInvoice.getInvoiceId());
            crmInvoice.setUpdateTime(LocalDateTimeUtil.now());
            updateById(crmInvoice);
            crmInvoice = getById(crmInvoice.getInvoiceId());
        } else {
            isAdd = true;
            if (crmInvoice.getOwnerUserId() == null) {
                crmInvoice.setOwnerUserId(UserUtil.getUserId());
            }
            crmInvoice.setInvoiceStatus(0);
            crmInvoice.setOwnerUserId(UserUtil.getUserId());
            ExamineRecordSaveBO examineRecordSaveBO = crmModel.getExamineFlowData();
            crmInvoice.setCreateUserId(UserUtil.getUserId()).setCreateTime(LocalDateTimeUtil.now()).setUpdateTime(LocalDateTimeUtil.now()).setBatchId(batchId);
            final Optional<CrmModelFiledVO> modelFiledOptional = queryDefaultField().stream().filter(data -> FieldEnum.SERIAL_NUMBER.getType().equals(data.getType())).findFirst();
            if (modelFiledOptional.isPresent()) {
                Map<String, Object> map = BeanUtil.beanToMap(crmInvoice);
                crmModel.getField().forEach(field -> {
                    map.put(field.getFieldName(), field.getValue());
                });
                final String generateNumber = getBean(ICrmFieldNumberDataService.class).generateNumber(getLabel(), modelFiledOptional.get(), map);
                crmInvoice.setInvoiceApplyNumber(generateNumber);
            }
            crmInvoice.setInvoiceId(invoiceId);
            save(crmInvoice);
            this.supplementFieldInfo(crmInvoice.getInvoiceId(), null, examineRecordSaveBO);
            examineRecordSaveBO.setTitle(crmInvoice.getInvoiceApplyNumber());
            ExamineRecordReturnVO crmExamineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
            crmInvoice.setExamineRecordId(crmExamineData.getRecordId());
            crmInvoice.setCheckStatus(crmExamineData.getExamineStatus());
            updateById(crmInvoice);
            actionRecordUtil.addRecord(crmInvoice.getInvoiceId(), CrmEnum.INVOICE, crmInvoice.getInvoiceApplyNumber());
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmInvoice));
        crmModel.getEntity().put("realInvoiceDate", DateUtil.formatDate((Date) crmModel.getEntity().get("realInvoiceDate")));
        savePage(crmModel, crmInvoice.getInvoiceId(), isExcel);

        if (isAdd) {
            // 发送消息
            MsgBodyBO msgBody = new MsgBodyBO();
            msgBody.setMsgKey(IdUtil.simpleUUID());
            msgBody.setMsgTag(getMsgLabelEnum().name());
            msgBody.setAction(CrmMsgActionEnum.save.name());
            msgBody.setCurrentUser(UserUtil.getUser());
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmInvoice.getInvoiceId());
            operateObject.put("name", crmInvoice.getInvoiceApplyNumber());
            operateObject.put("ownerUserId", crmInvoice.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmInvoice.getOwnerUserId()));
            String title = ResourcesUtil.getMessage("m2", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), "@NAME");
            msgBody.setTitle(title);
            msgBody.setOperateObject(Arrays.asList(operateObject));
            AdminMessageUtil.setMsgBody(msgBody);
        }
    }


    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    public CrmModel queryByIds(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryByIds(id, UserUtil.getUserId());
            crmModel.setLabel(CrmEnum.INVOICE.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            List<String> nameList = StrUtil.splitTrim((String) crmModel.get("companyUserId"), Const.SEPARATOR);
            String name = nameList.stream().map(str -> UserCacheUtil.getUserName(Long.valueOf(str))).collect(Collectors.joining(Const.SEPARATOR));
            crmModel.put("companyUserName", name);
            crmModel.put("createUserName", UserCacheUtil.getUserName((Long) crmModel.get("createUserId")));
            crmInvoiceDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
        } else {
            crmModel = new CrmModel(CrmEnum.INVOICE.getType());
        }
        return crmModel;
    }


    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.invoice, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        String status = "checkStatus";
        exportExcel(search, headList, response, isXls, (record, headMap) -> {
            for (String fieldName : headMap.keySet()) {
                record.put(fieldName, ActionRecordUtil.parseExportValue(record.get(fieldName), headMap.get(fieldName), false));
            }
            if (ObjectUtil.isEmpty(record.get(status))) {
                return;
            }
            String checkStatus;
            //0待审核、1通过、2拒绝、3审核中 4:撤回 5 未提交 6 创建 7 已删除 8 作废
            switch (TypeUtils.castToInt(record.get("checkStatus"))) {
                case 1:
                    checkStatus = "通过";
                    break;
                case 2:
                    checkStatus = "拒绝";
                    break;
                case 3:
                    checkStatus = "审核中";
                    break;
                case 4:
                    checkStatus = "撤回";
                    break;
                case 5:
                    checkStatus = "未提交";
                    break;
                case 7:
                    checkStatus = "已删除";
                    break;
                case 8:
                    checkStatus = "作废";
                    break;
                case 10:
                    checkStatus = "正常";
                    break;
                default:
                    checkStatus = "待审核";
            }
            record.put("checkStatus", checkStatus);
            Integer invoiceType = TypeUtils.castToInt(record.get("invoiceType"));
            if (invoiceType != null) {
                record.put("invoiceType", parseInvoiceType(invoiceType));
            }
            Integer invoiceStatus = TypeUtils.castToInt(record.get("invoiceStatus"));
            if (invoiceStatus != null) {
                record.put("invoiceStatus", Objects.equals(1, invoiceStatus) ? "已开票" : "未开票");
            }
        });

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    private String parseInvoiceType(Integer invoiceType) {
        switch (invoiceType) {
            case 1:
                return "增值税专用发票";
            case 2:
                return "增值税普通发票";
            case 3:
                return "国税通用机打发票";
            case 4:
                return "地税通用机打发票";
            case 5:
                return "收据";
            default:
                return "";
        }
    }

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"invoiceNumber", "contractNum", "customerName"};
    }

    @Override
    public Dict getSearchTransferMap() {
        return Dict.create().set("customerId", "customerName").set("contractId", "contractNum").set("customer_id", "customerId");
    }

    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long invoiceId = updateInformationBO.getId();
        CrmInvoice invoice = getById(invoiceId);
        int eight = 8;
        if (invoice.getCheckStatus() == eight) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
        }
        if (invoice.getCheckStatus() == 1) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
        }
        int two = 2;
        int four = 4;
        int five = 5;
        int ten = 10;
        if (!Arrays.asList(two, four, five, ten).contains(invoice.getCheckStatus())) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
        }
        updateInformationBO.getList().forEach(record -> {
            CrmInvoice crmInvoice = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldInvoiceMap = BeanUtil.beanToMap(crmInvoice);
            String fieldType = "fieldType";
            if (record.getInteger(fieldType) == 1) {
                Map<String, Object> crmInvoiceMap = new HashMap<>(oldInvoiceMap);
                crmInvoiceMap.put(record.getString("fieldName"), record.get("value"));
                CrmInvoice crmInvoices = BeanUtil.toBeanIgnoreCase(crmInvoiceMap, CrmInvoice.class, true);
                String invoiceType = "invoiceType";
                if (oldInvoiceMap.containsKey(invoiceType)) {
                    oldInvoiceMap.put("invoiceType", parseInvoiceType(TypeUtils.castToInt(oldInvoiceMap.get("invoiceType"))));
                }
                if (crmInvoiceMap.containsKey(invoiceType)) {
                    crmInvoiceMap.put("invoiceType", parseInvoiceType(TypeUtils.castToInt(crmInvoiceMap.get("invoiceType"))));
                }
                actionRecordUtil.updateRecord(oldInvoiceMap, crmInvoiceMap, CrmEnum.INVOICE, crmInvoices.getInvoiceApplyNumber(), crmInvoices.getInvoiceId());
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("invoice_id", updateInformationBO.getId()).update();
            } else if (record.getInteger(fieldType) == 0 || record.getInteger(fieldType) == two) {
                CrmInvoiceData invoiceData = crmInvoiceDataService.lambdaQuery().select(CrmInvoiceData::getValue, CrmInvoiceData::getId).eq(CrmInvoiceData::getFieldId, record.getInteger("fieldId"))
                        .eq(CrmInvoiceData::getBatchId, batchId).one();
                String value = invoiceData != null ? invoiceData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.INVOICE, BehaviorEnum.UPDATE, invoiceId, crmInvoice.getInvoiceApplyNumber(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));

                CrmInvoiceData crmInvoiceData = new CrmInvoiceData();
                crmInvoiceData.setId(invoiceData != null ? invoiceData.getId() : null);
                crmInvoiceData.setFieldId(record.getLong("fieldId"));
                crmInvoiceData.setName(record.getString("fieldName"));
                crmInvoiceData.setValue(newValue);
                crmInvoiceData.setCreateTime(LocalDateTimeUtil.now());
                crmInvoiceData.setBatchId(batchId);
                crmInvoiceDataService.saveOrUpdate(crmInvoiceData);

            }
            updateField(record, invoiceId);
        });
        this.lambdaUpdate().set(CrmInvoice::getUpdateTime, new Date()).eq(CrmInvoice::getInvoiceId, invoiceId).update();
    }

}
