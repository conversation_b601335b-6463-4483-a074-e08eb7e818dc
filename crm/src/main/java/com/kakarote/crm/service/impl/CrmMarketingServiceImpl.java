package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.TagUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.constant.CrmMarketingRelationEnum;
import com.kakarote.crm.entity.BO.CrmBusinessSaveBO;
import com.kakarote.crm.entity.BO.CrmCensusBO;
import com.kakarote.crm.entity.BO.CrmMarketingPageBO;
import com.kakarote.crm.entity.BO.CrmSyncDataBO;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmFieldMapper;
import com.kakarote.crm.mapper.CrmMarketingMapper;
import com.kakarote.crm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 营销表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
@Service
public class CrmMarketingServiceImpl extends BaseServiceImpl<CrmMarketingMapper, CrmMarketing> implements ICrmMarketingService {


    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    private ICrmMarketingInfoService crmMarketingInfoService;

    @Autowired
    private ICrmMarketingFieldService crmMarketingFieldService;

    @Autowired
    private ICrmMarketingFormService crmMarketingFormService;

    @Autowired
    private ICrmMarketingRelationService marketingRelationService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(CrmMarketing crmMarketing) {
        Long userId = UserUtil.getUserId();
        if (crmMarketing.getMarketingId() == null) {
            crmMarketing.setCreateUserId(userId);
            crmMarketing.setCreateTime(LocalDateTimeUtil.now());
            crmMarketing.setUpdateTime(LocalDateTimeUtil.now());
            if (save(crmMarketing)) {
                // 添加关系
                addMarketingRelation(crmMarketing);
            }
        } else {
            crmMarketing.setUpdateTime(LocalDateTimeUtil.now());
            if (updateById(crmMarketing)) {
                // 删除原有关系
                marketingRelationService
                        .lambdaUpdate()
                        .eq(CrmMarketingRelation::getMarketingId, crmMarketing.getMarketingId())
                        .remove();
                // 添加关系
                addMarketingRelation(crmMarketing);
            }
        }
    }


    @Override
    public BasePage<CrmMarketing> queryPageList(CrmMarketingPageBO crmMarketingPageBO, Integer status) {
        List<Long> userIds = null, deptIds = null;
        boolean isAdmin = UserUtil.isAdmin();
        // 判断是否是管理员
        if (!isAdmin) {
            userIds = adminService.queryChildUserId(UserUtil.getUserId()).getData();
            userIds.add(UserUtil.getUserId());
            deptIds = adminService.queryChildDeptId(UserUtil.getUser().getDeptId()).getData();
            deptIds.add(UserUtil.getUser().getDeptId());
        }
        // 查询数据
        BasePage<CrmMarketing> page = getBaseMapper().queryPageList(crmMarketingPageBO.parse(), userIds, deptIds,
                crmMarketingPageBO.getCrmType(), crmMarketingPageBO.getSearch(), isAdmin, crmMarketingPageBO.getTimeType(),
                status);
        List<CrmMarketing> pageList = page.getList();
        // 定义创建人ids
        Set<Long> createUserIds = new HashSet<>(pageList.size());
        // 定义MainFileIds
        Map<Long, Set<Long>> mainFileIdsMap = new HashMap<>(pageList.size());
        // 定义crmType
        Set<Long> crmTypeList = new HashSet<>(pageList.size());
        // 遍历添加数据
        pageList.forEach(result -> {
            createUserIds.add(result.getCreateUserId());
            Long crmType = result.getCrmType();
            if (!crmType.equals(LEADS) && !crmType.equals(CUSTOMER)) {
                crmTypeList.add(crmType);
            }
            mainFileIdsMap.put(result.getMarketingId(), TagUtil.toLongSet(result.getMainFileIds()));
        });
        // 查询createUsernameMap
        Map<Long, String> simpleUserRealnameMap = UserCacheUtil.getSimpleUserRealNameMap(createUserIds);
        // 查询MainFile
        Set<Long> fieldIds = mainFileIdsMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        // 获取fileDataMap
        Map<Long, FileEntity> fileDataMap = adminFileService.queryByIds(fieldIds)
                .getData()
                .stream()
                .collect(Collectors.toMap(FileEntity::getFileId, file -> file));
        // 查询crmTypeMap
        Map<Long, CrmMarketingForm> crmMarketingFormMap = new HashMap<>(crmTypeList.size());
        if (!crmTypeList.isEmpty()) {
            crmMarketingFormMap = crmMarketingFormService
                    .lambdaQuery()
                    .in(CrmMarketingForm::getId, crmTypeList)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(CrmMarketingForm::getId, form -> form));
        }
        // 遍历处理数据
        for (CrmMarketing marketing : pageList) {
            // 处理创建人姓名
            marketing.setCreateUserName(simpleUserRealnameMap.get(marketing.getCreateUserId()));
            // 处理crmType
            Long crmType = marketing.getCrmType();
            if (crmType.equals(LEADS)) {
                marketing.setCrmTypeName(LEADS_STR);
            } else if (crmType.equals(CUSTOMER)) {
                marketing.setCrmTypeName(CUSTOMER_STR);
            } else {
                CrmMarketingForm marketingForm = crmMarketingFormMap.get(crmType);
                if (Objects.nonNull(marketingForm)) {
                    marketing.setCrmTypeName(marketingForm.getTitle());
                }
            }
            // 处理mainFile
            Set<Long> fileIds = mainFileIdsMap.get(marketing.getMarketingId());
            List<FileEntity> fileEntities = new ArrayList<>(fileIds.size());
            fileIds.forEach(fileId -> {
                FileEntity fileEntity = fileDataMap.get(fileId);
                if (Objects.nonNull(fileEntity)) {
                    fileEntities.add(fileEntity);
                }
            });
            marketing.setMainFileList(fileEntities);
            marketing.setMainFile(fileEntities.size() > 0 ? fileEntities.get(0) : null);
        }
        return page;
    }

    @Override
    public JSONObject queryById(Long marketingId, String device) {
        AES aes = SecureUtil.aes(BYTES);
        CrmMarketing marketing = getById(marketingId);
        if(marketing == null){
            throw new CrmException(CrmCodeEnum.CRM_DATE_REMOVE_ERROR);
        }
        Integer subCount = crmMarketingInfoService.lambdaQuery().eq(CrmMarketingInfo::getMarketingId, marketingId).count();
        JSONObject crmMarketing = BeanUtil.copyProperties(marketing, JSONObject.class);
        Long crmType = marketing.getCrmType();
        String crmTypeName = "";
        if (crmType.equals(LEADS)) {
            crmTypeName = LEADS_STR;
        } else if (crmType.equals(CUSTOMER)) {
            crmTypeName = CUSTOMER_STR;
        } else {
            CrmMarketingForm marketingForm = crmMarketingFormService.getById(crmType);
            if (marketingForm != null) {
                crmTypeName = marketingForm.getTitle();
            }
        }
        crmMarketing.put("crmTypeName", crmTypeName);
        crmMarketing.put("subCount", subCount);
        crmMarketing.put("enMarketingId", aes.encryptHex(marketing.getMarketingId().toString()));
        crmMarketing.put("currentUserId", aes.encryptHex(UserUtil.getUserId().toString()));
        // 查询关联用户和部门
        Map<Integer, List<CrmMarketingRelation>> map = marketingRelationService
                .lambdaQuery()
                .select(CrmMarketingRelation::getRelationId, CrmMarketingRelation::getType)
                .eq(CrmMarketingRelation::getMarketingId, marketingId)
                .list()
                .stream()
                .collect(Collectors.groupingBy(CrmMarketingRelation::getType));
        // 定义关联id的map
        Map<Integer, List<Long>> relationIdMap = new HashMap<>(map.size());
        map.forEach((k, v) -> relationIdMap.put(k, v.stream().map(CrmMarketingRelation::getRelationId).collect(Collectors.toList())));
        // 放入用户关联
        crmMarketing.put("relationUserInfo", relationIdMap.containsKey(CrmMarketingRelationEnum.USER.getType())?UserCacheUtil.getSimpleUsers(relationIdMap.get(CrmMarketingRelationEnum.USER.getType())):null);
        // 放入部门关联
        crmMarketing.put("relationDeptInfo", relationIdMap.containsKey(CrmMarketingRelationEnum.DEPT.getType())?adminService.queryDeptByIds(relationIdMap.get(CrmMarketingRelationEnum.DEPT.getType())).getData():null);
        crmMarketing.put("createUserInfo", UserCacheUtil.getSimpleUser(marketing.getCreateUserId()));
        if (StrUtil.isNotEmpty(marketing.getDetailFileIds())) {
            List<FileEntity> recordList = adminFileService.queryByIds(TagUtil.toLongSet(marketing.getDetailFileIds())).getData();
            crmMarketing.put("detailFileList", recordList);
        } else {
            crmMarketing.put("detailFileList", new ArrayList<>());
        }
        if (marketing.getMainFileIds() != null) {
            List<FileEntity> recordList = adminFileService.queryByIds(TagUtil.toLongSet(marketing.getMainFileIds())).getData();
            crmMarketing.put("mainFileList", recordList);
        } else {
            crmMarketing.put("mainFileList", new ArrayList<>());
        }
        Integer second = marketing.getSecond();
        Integer count = crmMarketingInfoService.lambdaQuery().eq(CrmMarketingInfo::getMarketingId, marketingId).eq(CrmMarketingInfo::getDevice, device).count();
        if (second == 1 && count > 0) {
            crmMarketing.put("isAdd", 0);
        } else {
            crmMarketing.put("isAdd", 1);
        }
        if (marketing.getStatus() == 0 || System.currentTimeMillis() > LocalDateTimeUtil.toEpochMilli(marketing.getEndTime())) {
            crmMarketing.put("isEnd", 1);
        } else {
            crmMarketing.put("isEnd", 0);
        }
        AdminConfig data = adminService.queryFirstConfigByName("companyName").getData();
        crmMarketing.put("companyName", data.getValue());
        return crmMarketing;
    }

    @Override
    public void deleteByIds(List<Long> marketingIds) {
        for (Long marketingId : marketingIds) {
            Integer count = crmMarketingInfoService.lambdaQuery().eq(CrmMarketingInfo::getMarketingId, marketingId).eq(CrmMarketingInfo::getStatus, 0).count();
            if (count > 0) {
                throw new CrmException(CrmCodeEnum.CRM_MARKETING_UNSYNCHRONIZED_DATA);
            }
            removeById(marketingId);
            crmMarketingInfoService.lambdaUpdate().eq(CrmMarketingInfo::getMarketingId, marketingId).remove();
        }
    }

    @Autowired
    private ICrmFieldService crmFieldService;

    @Override
    public List<CrmModelFiledVO> queryField(Long marketingId) {
        CrmMarketing marketing = getById(marketingId);
        if (FIXED_CRM_TYPE.contains(marketing.getCrmType())) {
            List<CrmField> list = crmFieldService.lambdaQuery().in(CrmField::getFieldId, TagUtil.toSet(marketing.getFieldDataId())).list();
            return list.stream().map(field -> {
                CrmModelFiledVO crmModelFiled = BeanUtil.copyProperties(field, CrmModelFiledVO.class);
                FieldEnum typeEnum = FieldEnum.parse(crmModelFiled.getType());
                crmFieldService.recordToFormType(crmModelFiled, typeEnum);
                return crmModelFiled;
            }).collect(Collectors.toList());
        } else {
            List<CrmMarketingField> crmMarketingFields = crmMarketingFieldService.lambdaQuery().in(CrmMarketingField::getFieldId, TagUtil.toLongSet(marketing.getFieldDataId())).list();
            return crmMarketingFields.stream().map(field -> {
                CrmModelFiledVO crmModelFiled = BeanUtil.copyProperties(field, CrmModelFiledVO.class);
                FieldEnum typeEnum = FieldEnum.parse(crmModelFiled.getType());
                crmFieldService.recordToFormType(crmModelFiled, typeEnum);
                return crmModelFiled;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public void updateStatus(String marketingIds, Integer status) {
        List<CrmMarketing> crmMarketingList = new ArrayList<>();
        TagUtil.toLongSet(marketingIds).forEach(id -> {
            CrmMarketing marketing = new CrmMarketing();
            marketing.setMarketingId(id);
            marketing.setStatus(status);
            crmMarketingList.add(marketing);
        });
        updateBatchById(crmMarketingList, 100);
    }

    @Override
    public void updateShareNum(Long marketingId, Integer num) {
        CrmMarketing crmMarketing = getById(marketingId);
        if (num == null) {
            lambdaUpdate().set(CrmMarketing::getShareNum, crmMarketing.getShareNum() + 1).eq(CrmMarketing::getMarketingId, marketingId).update();
        } else {
            lambdaUpdate().set(CrmMarketing::getShareNum, crmMarketing.getShareNum() + num).eq(CrmMarketing::getMarketingId, marketingId).update();
        }
    }

    @Override
    public BasePage<JSONObject> census(CrmCensusBO crmCensusBO) {
        Long marketingId = crmCensusBO.getMarketingId();
        Long userId = UserUtil.getUserId();
        Integer status = crmCensusBO.getStatus();
        List<Long> userIds = adminService.queryChildUserId(userId).getData();
        userIds.add(userId);
        BasePage<JSONObject> page = getBaseMapper().census(crmCensusBO.parse(), marketingId, userIds, status);
        List<JSONObject> recordList = new ArrayList<>();
        page.getList().forEach(record -> {
            String fieldInfo = record.getString("fieldInfo");
            record.remove("fieldInfo");
            JSONObject jsonObject = JSON.parseObject(fieldInfo);
            JSONObject entity = jsonObject.getJSONObject("entity");
            String type="type";
            jsonObject.getJSONArray("field").forEach(field -> {
                JSONObject adminFieldv = (JSONObject) field;
                if (Objects.equals(adminFieldv.get(type), FieldEnum.DATE_INTERVAL.getType())
                        || Objects.equals(adminFieldv.get("type"), FieldEnum.TAG.getType())) {
                    entity.put(adminFieldv.getString("fieldName"), JSON.parseArray(adminFieldv.getString("value")));
                } else {
                    entity.put(adminFieldv.getString("fieldName"), adminFieldv.getString("value"));
                }
            });
            entity.put("status", record.getInteger("status"));
            entity.put("rId", record.getLong("rId"));
            entity.put("ownerUserName", UserCacheUtil.getUserName(record.getLong("ownerUserId")));
            record.put("entity", entity);
            record.remove("status", "rId");
            recordList.add(new JSONObject().fluentPutAll(entity.getInnerMap()));
        });
        page.setList(recordList);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject queryAddField(String marketingId) {
        AES aes = SecureUtil.aes(BYTES);
        Long marketingIdInt = Long.valueOf(aes.decryptStr(marketingId));
        CrmMarketing crmMarketing = getById(marketingIdInt);
        if (crmMarketing.getStatus() == 0) {
            throw new CrmException(CrmCodeEnum.CRM_MARKETING_STOP);
        }
        if (System.currentTimeMillis() > LocalDateTimeUtil.toEpochMilli(crmMarketing.getEndTime())) {
            throw new CrmException(CrmCodeEnum.CRM_MARKETING_QR_CODE_EXPIRED);
        }
        lambdaUpdate().set(CrmMarketing::getBrowse, crmMarketing.getBrowse() + 1).eq(CrmMarketing::getMarketingId, marketingIdInt).update();
        List<CrmModelFiledVO> recordList;
        if (FIXED_CRM_TYPE.contains(crmMarketing.getCrmType())) {
            List<CrmField> list = crmFieldService.lambdaQuery().in(CrmField::getFieldId, TagUtil.toSet(crmMarketing.getFieldDataId())).list();
            recordList = list.stream().map(field -> {
                CrmModelFiledVO crmModelFiled = BeanUtil.copyProperties(field, CrmModelFiledVO.class);
                FieldEnum typeEnum = FieldEnum.parse(crmModelFiled.getType());
                crmFieldService.recordToFormType(crmModelFiled, typeEnum);
                return crmModelFiled;
            }).collect(Collectors.toList());
        } else {
            List<CrmMarketingField> list = crmMarketingFieldService.lambdaQuery().in(CrmMarketingField::getFieldId, TagUtil.toSet(crmMarketing.getFieldDataId())).list();
            recordList = list.stream().map(field -> {
                CrmModelFiledVO crmModelFiled = BeanUtil.copyProperties(field, CrmModelFiledVO.class);
                FieldEnum typeEnum = FieldEnum.parse(crmModelFiled.getType());
                crmFieldService.recordToFormType(crmModelFiled, typeEnum);
                return crmModelFiled;
            }).collect(Collectors.toList());
        }
        JSONObject kv = new JSONObject().fluentPut("marketingName", crmMarketing.getMarketingName()).fluentPut("list", recordList);
        if (StrUtil.isNotEmpty(crmMarketing.getMainFileIds())) {
            List<FileEntity> fileEntities = adminFileService.queryByIds(TagUtil.toLongSet(crmMarketing.getMainFileIds())).getData();
            kv.put("mainFileList", fileEntities);
        } else {
            kv.put("mainFileList", new ArrayList<>());
        }
        return kv;
    }

    @Override
    public void saveMarketingInfo(JSONObject data) {
        AES aes = SecureUtil.aes(BYTES);
        Long marketingId = Long.valueOf(aes.decryptStr(data.getString("marketingId")));
        Long currentUserId = Long.valueOf(aes.decryptStr(data.getString("currentUserId")));
        UserUtil.setUser(ApplicationContextHolder.getBean(AdminService.class).queryLoginUserInfo(currentUserId).getData());
        CrmMarketing crmMarketing = getById(marketingId);
        CrmMarketingInfo marketingInfo = new CrmMarketingInfo();
        marketingInfo.setFieldInfo(data.getJSONObject("fieldInfo").toJSONString());
        marketingInfo.setDevice(data.getString("device"));
        marketingInfo.setMarketingId(marketingId);
        marketingInfo.setOwnerUserId(currentUserId);
        Integer second = crmMarketing.getSecond();
        Integer count = crmMarketingInfoService.lambdaQuery().eq(CrmMarketingInfo::getMarketingId, marketingInfo.getMarketingId())
                .eq(CrmMarketingInfo::getDevice, marketingInfo.getDevice()).count();
        if (second == 1 && count > 0) {
            throw new CrmException(CrmCodeEnum.CRM_MARKETING_CAN_ONLY_BE_FILLED_ONCE);
        }
        lambdaUpdate().set(CrmMarketing::getSubmitNum, crmMarketing.getSubmitNum() + 1).eq(CrmMarketing::getMarketingId, marketingId).update();
        marketingInfo.setCreateTime(LocalDateTimeUtil.now());
        crmMarketingInfoService.save(marketingInfo);
    }

    @Autowired
    private ICrmLeadsService crmLeadsService;

    @Autowired
    private ICrmCustomerService customerService;

    @Autowired
    private CrmFieldMapper crmFieldMapper;

    @Override
    public void syncData(CrmSyncDataBO syncDataBO) {
        if(syncDataBO.getStatus() == null){
            syncDataBO.setStatus(0);
        }
        List<Long> ids;
        if (CollUtil.isEmpty(syncDataBO.getrIds())) {
            ids = crmMarketingInfoService.lambdaQuery().select(CrmMarketingInfo::getRId).eq(CrmMarketingInfo::getMarketingId, syncDataBO.getMarketingId())
                    .eq(CrmMarketingInfo::getOwnerUserId, UserUtil.getUserId())
                    .eq(CrmMarketingInfo::getStatus,syncDataBO.getStatus())
                    .list()
                    .stream().map(CrmMarketingInfo::getRId).collect(Collectors.toList());
        } else {
            ids = syncDataBO.getrIds();
        }
        for (Long id : ids) {
            CrmMarketingInfo marketingInfo = crmMarketingInfoService.getById(id);
            if (!marketingInfo.getOwnerUserId().equals(UserUtil.getUserId())) {
                throw new CrmException(CrmCodeEnum.CRM_ONLY_SYNC_DATA_FOR_WHICH_YOU_ARE_RESPONSIBLE);
            }
            CrmMarketing crmMarketing = getById(marketingInfo.getMarketingId());
            Long crmType = crmMarketing.getCrmType();
            Integer status = marketingInfo.getStatus();
            if (status == 1) {
                throw new CrmException(CrmCodeEnum.CRM_MARKETING_DATA_SYNCED);
            }
            CrmBusinessSaveBO crmModelSaveBO = JSON.parseObject(marketingInfo.getFieldInfo(), CrmBusinessSaveBO.class);
            try {
                if (crmType.equals(LEADS)) {
                    List<CrmModelFiledVO> filedList = crmLeadsService.queryField(null);
                    List<CrmModelFiledVO> uniqueList = filedList.stream().filter(field -> field.getIsUnique() != null && field.getIsUnique().equals(1)).collect(Collectors.toList());
                    CrmEnum crmEnum = CrmEnum.LEADS;
                    Map<String, Object> map = new HashMap<>();
                    for (CrmModelFiledVO crmModelFiledVO : crmModelSaveBO.getField()) {
                        map.put(crmModelFiledVO.getFieldName(), crmModelFiledVO.getValue());
                    }
                    for (CrmModelFiledVO field : uniqueList) {
                        if (field.getFieldType() == 1) {
                            Object value = crmModelSaveBO.getEntity().get(field.getFieldName());
                            if (ObjectUtil.isEmpty(value)) {
                                continue;
                            }
                            Integer count = crmFieldMapper.verifyFixedField(crmEnum.getTableName(), StrUtil.toUnderlineCase(field.getFieldName()), value.toString(), null, crmEnum.getType());
                            if (count > 0) {
                                crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 2).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                                throw new CrmException(CrmCodeEnum.CRM_FIELD_ALREADY_EXISTS, field.getName());
                            }
                        } else {
                            if (ObjectUtil.isEmpty(map.get(field.getFieldName()))) {
                                continue;
                            }
                            Integer count = crmFieldMapper.verifyField(crmEnum.getTableName(), field.getFieldId(), map.getOrDefault(field.getFieldName(), "").toString(), null);
                            if (count > 0) {
                                crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 2).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                                throw new CrmException(CrmCodeEnum.CRM_FIELD_ALREADY_EXISTS, field.getName());
                            }
                        }
                    }
                    crmLeadsService.addOrUpdate(crmModelSaveBO, false);
                    crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 1).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                } else if (crmType.equals(CUSTOMER)) {
                    List<CrmModelFiledVO> filedList = customerService.queryField(null);
                    List<CrmModelFiledVO> uniqueList = filedList.stream().filter(field -> field.getIsUnique() != null && field.getIsUnique().equals(1)).collect(Collectors.toList());
                    CrmEnum crmEnum = CrmEnum.CUSTOMER;
                    Map<String, Object> map = new HashMap<>();
                    for (CrmModelFiledVO crmModelFiledVO : crmModelSaveBO.getField()) {
                        map.put(crmModelFiledVO.getFieldName(), crmModelFiledVO.getValue().toString());
                    }
                    for (CrmModelFiledVO field : uniqueList) {
                        if (field.getFieldType() == 1) {
                            Object value = crmModelSaveBO.getEntity().get(field.getFieldName());
                            if (ObjectUtil.isEmpty(value)) {
                                continue;
                            }
                            Integer count = crmFieldMapper.verifyFixedField(crmEnum.getTableName(), StrUtil.toUnderlineCase(field.getFieldName()), value.toString(), null, crmEnum.getType());
                            if (count > 0) {
                                crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 2).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                                throw new CrmException(CrmCodeEnum.CRM_FIELD_ALREADY_EXISTS, field.getName());
                            }
                        } else {
                            if (ObjectUtil.isEmpty(map.get(field.getFieldName()))) {
                                continue;
                            }
                            Integer count = crmFieldMapper.verifyField(crmEnum.getTableName(), field.getFieldId(), map.getOrDefault(field.getFieldName(), "").toString(), null);
                            if (count > 0) {
                                crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 2).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                                throw new CrmException(CrmCodeEnum.CRM_FIELD_ALREADY_EXISTS, field.getName());
                            }
                        }
                    }
                    customerService.addOrUpdate(crmModelSaveBO, false, null);
                    crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 1).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                }
            } catch (CrmException e) {
                crmMarketingInfoService.lambdaUpdate().set(CrmMarketingInfo::getStatus, 2).eq(CrmMarketingInfo::getRId, marketingInfo.getRId()).update();
                throw new CrmException(CrmCodeEnum.CRM_SYNC_FAILED, e.getMsg());
            }
        }
    }

    /***
     * 添加crm营销关系
     * @param crmMarketing 实体
     * <AUTHOR> sir
     * @date 2021/11/18
     */
    private void addMarketingRelation(CrmMarketing crmMarketing) {
        // 添加关系
        Set<Long> relationUserIds = TagUtil.toLongSet(crmMarketing.getRelationUserId());
        Set<Long> relationDeptIds = TagUtil.toLongSet(crmMarketing.getRelationDeptId());
        List<CrmMarketingRelation> relationList = new ArrayList<>(relationUserIds.size() + relationDeptIds.size());
        // 追加用户ids
        relationUserIds.forEach(uid -> relationList.add(CrmMarketingRelation
                .builder()
                .marketingId(crmMarketing.getMarketingId())
                .relationId(uid)
                .type(CrmMarketingRelationEnum.USER.getType())
                .build()));
        // 追加部门ids
        relationDeptIds.forEach(uid -> relationList.add(CrmMarketingRelation
                .builder()
                .marketingId(crmMarketing.getMarketingId())
                .relationId(uid)
                .type(CrmMarketingRelationEnum.DEPT.getType())
                .build()));
        marketingRelationService.saveBatch(relationList, relationList.size());
    }
}
