package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.*;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.common.ResourcesUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmProductMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Service
@Slf4j
public class CrmProductServiceImpl extends BaseServiceImpl<CrmProductMapper, CrmProduct> implements ICrmProductService, CrmPageService {

    private static final String PRODUCT_STATUS_URL = "/crmProduct/updateStatus";

    @Autowired
    private ICrmProductDataService crmProductDataService;

    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    @Autowired
    private ICrmProductCategoryService crmProductCategoryService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    private ICrmProductDetailImgService productDetailImgService;

    @Autowired
    private FieldService fieldService;

    private static final String FIELD_TYPE = "fieldType";

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryById(id);
        crmModel.setLabel(getLabel().getType());
        List<CrmModelFiledVO> crmModelFiledVoS = crmFieldService.queryField(crmModel);
        for (CrmModelFiledVO crmModelFiledVO : crmModelFiledVoS) {
            if ("categoryId".equals(crmModelFiledVO.getFieldName())) {
                List<Long> list = crmProductCategoryService.queryId(null, Convert.toLong(crmModelFiledVO.getValue()));
                if (CollUtil.isNotEmpty(list)) {
                    crmModelFiledVO.setValue(list);
                } else {
                    crmModelFiledVO.setValue(null);
                }
            }
        }

        int authLevel = 3;
        Long userId = UserUtil.getUserId();
        String key = userId.toString();
        List<String> noAuthMenuUrls = BaseUtil.getRedis().get(key);
        if (noAuthMenuUrls != null && noAuthMenuUrls.contains(PRODUCT_STATUS_URL)) {
            authLevel = 2;
        }
        List<Object> statusList = new ArrayList<>();
        statusList.add(new JSONObject().fluentPut("name", "上架").fluentPut("value", 1));
        statusList.add(new JSONObject().fluentPut("name", "下架").fluentPut("value", 0));
        crmModelFiledVoS.add(new CrmModelFiledVO("status", FieldEnum.SELECT, "是否上下架", 1).setIsNull(1).setSetting(statusList).setValue(crmModel.get("status")).setAuthLevel(authLevel));
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            crmModelFiledVoS.addAll(modelFiledVOS);
        }
        return crmModelFiledVoS;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryById(id);
        if (ObjectUtil.isNotEmpty(id)) {
            //去除编辑掩码
            crmModel.put("update", true);
        }
        crmModel.setLabel(getLabel().getType());
        List<List<CrmModelFiledVO>> crmModelFiledVoS = crmFieldService.queryFormPositionFieldVO(crmModel);
        for (List<CrmModelFiledVO> filedVOList : crmModelFiledVoS) {
            for (CrmModelFiledVO crmModelFiledVO : filedVOList) {
                if ("categoryId".equals(crmModelFiledVO.getFieldName())) {
                    List<Long> list = crmProductCategoryService.queryId(null, Convert.toLong(crmModelFiledVO.getValue()));
                    if (CollUtil.isNotEmpty(list)) {
                        crmModelFiledVO.setValue(list);
                    } else {
                        crmModelFiledVO.setValue(null);
                    }
                }
            }
        }

        int authLevel = 3;
        Long userId = UserUtil.getUserId();
        String key = userId.toString();
        List<String> noAuthMenuUrls = BaseUtil.getRedis().get(key);
        if (noAuthMenuUrls != null && noAuthMenuUrls.contains(PRODUCT_STATUS_URL)) {
            authLevel = 2;
        }
        List<Object> statusList = new ArrayList<>();
        statusList.add(new JSONObject().fluentPut("name", "上架").fluentPut("value", 1));
        statusList.add(new JSONObject().fluentPut("name", "下架").fluentPut("value", 0));
        CrmModelFiledVO crmModelFiledVO = new CrmModelFiledVO("status", FieldEnum.SELECT, "是否上下架", 1).setIsNull(1).setSetting(statusList).setValue(crmModel.get("status")).setAuthLevel(authLevel);
        crmModelFiledVO.setStylePercent(50);
        crmModelFiledVoS.add(ListUtil.toList(crmModelFiledVO));
        return crmModelFiledVoS;
    }

    /**
     * 分页查询
     *
     * @param search 搜索添加
     * @return data
     */
    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        BasePage<Map<String, Object>> basePage = queryList(search, false);
        basePage.getList().forEach(map -> {
            String status = map.get("status").toString();
            map.put("status", Objects.equals("1", status) ? "上架" : "下架");
        });
        return basePage;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            Integer count = lambdaQuery().eq(CrmProduct::getProductId, id).ne(CrmProduct::getStatus, 3).count();
            if (count == 0) {
                throw new CrmException(CrmCodeEnum.CRM_DATE_REMOVE_ERROR);
            }
            crmModel = getBaseMapper().queryById(id, UserUtil.getUserId());
            crmModel.setLabel(CrmEnum.PRODUCT.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            crmProductDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
            Optional<CrmProductDetailImg> detailImgOpt = productDetailImgService.lambdaQuery().eq(CrmProductDetailImg::getProductId, id).oneOpt();
            if (detailImgOpt.isPresent()) {
                CrmProductDetailImg detailImg = detailImgOpt.get();
                if (detailImg.getMainFileIds() != null) {
                    List<FileEntity> mainFileList = adminFileService.queryByIds(TagUtil.toLongSet(detailImg.getMainFileIds())).getData();
                    crmModel.put("mainFileList", mainFileList);
                } else {
                    crmModel.put("mainFileList", new ArrayList<>());
                }
                if (detailImg.getDetailFileIds() != null) {
                    List<FileEntity> detailFileList = adminFileService.queryByIds(TagUtil.toLongSet(detailImg.getDetailFileIds())).getData();
                    crmModel.put("detailFileList", detailFileList);
                } else {
                    crmModel.put("detailFileList", new ArrayList<>());
                }
            } else {
                crmModel.put("mainFileList", new ArrayList<>());
                crmModel.put("detailFileList", new ArrayList<>());
            }
        } else {
            crmModel = new CrmModel(CrmEnum.PRODUCT.getType());
        }
        return crmModel;
    }

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(CrmModelSaveBO crmModel, boolean isExcel) {
        CrmProduct crmProduct = BeanUtil.copyProperties(crmModel.getEntity(), CrmProduct.class);
        String batchId = StrUtil.isNotEmpty(crmProduct.getBatchId()) ? crmProduct.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_product_data"));
        crmProductDataService.saveData(crmModel.getField(), batchId);
        if (crmProduct.getProductId() == null) {
            crmProduct.setCreateUserId(UserUtil.getUserId());
            crmProduct.setCreateTime(LocalDateTimeUtil.now());
            crmProduct.setUpdateTime(LocalDateTimeUtil.now());
            if (crmProduct.getOwnerUserId() == null) {
                crmProduct.setOwnerUserId(UserUtil.getUserId());
            }
            crmProduct.setBatchId(batchId);
            save(crmProduct);
            actionRecordUtil.addRecord(crmProduct.getProductId(), CrmEnum.PRODUCT, crmProduct.getName());
        } else {
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(getById(crmProduct.getProductId())), BeanUtil.beanToMap(crmProduct), CrmEnum.PRODUCT, crmProduct.getName(), crmProduct.getProductId());
            crmProduct.setUpdateTime(LocalDateTimeUtil.now());
            updateById(crmProduct);
            crmProduct = getById(crmProduct.getProductId());
        }
        Optional<CrmProductDetailImg> detailImgOpt = productDetailImgService.lambdaQuery().eq(CrmProductDetailImg::getProductId, crmProduct.getProductId()).oneOpt();
        if (detailImgOpt.isPresent()) {
            CrmProductDetailImg crmProductDetailImg = detailImgOpt.get();
            crmProductDetailImg.setDetailFileIds((String) crmModel.getEntity().get("detailFileIds"));
            crmProductDetailImg.setMainFileIds((String) crmModel.getEntity().get("mainFileIds"));
            productDetailImgService.updateById(crmProductDetailImg);
        } else {
            CrmProductDetailImg crmProductDetailImg = new CrmProductDetailImg();
            crmProductDetailImg.setProductId(crmProduct.getProductId());
            crmProductDetailImg.setDetailFileIds((String) crmModel.getEntity().get("detailFileIds"));
            crmProductDetailImg.setMainFileIds((String) crmModel.getEntity().get("mainFileIds"));
            productDetailImgService.save(crmProductDetailImg);
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmProduct));
        savePage(crmModel, crmProduct.getProductId(), isExcel);
    }

    @Override
    public void setOtherField(Map<String, Object> map) {
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
        CrmProductCategory productCategory = crmProductCategoryService.getById((Serializable) map.get("categoryId"));
        if (productCategory != null) {
            map.put("categoryName", productCategory.getName());
        } else {
            map.put("categoryName", "");
        }
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
    }


    /**
     * 删除数据
     *
     * @param ids ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        LambdaQueryWrapper<CrmProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CrmProduct::getBatchId);
        wrapper.in(CrmProduct::getProductId, ids);
        List<String> batchIdList = listObjs(wrapper, Object::toString);
        //删除字段操作记录
        crmActionRecordService.deleteActionRecord(CrmEnum.PRODUCT, ids);
        if (CollUtil.isNotEmpty(batchIdList)) {
            //删除自定义字段
            //TODO 不删除,产品单位是自定义字段,删除后关联的产品没有单位
//            crmProductDataService.deleteByBatchId(batchIdList);
        }
        LambdaUpdateWrapper<CrmProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CrmProduct::getStatus, 3);
        updateWrapper.in(CrmProduct::getProductId, ids);
        update(updateWrapper);
        //todo 删除文件,暂不处理
        //删除es数据
        deletePage(ids);
    }

    /**
     * 修改负责人
     */
    @Override
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO) {
        Long newOwnerUserId = changeOwnerUserBO.getOwnerUserId();
        List<Long> ids = changeOwnerUserBO.getIds();
        LambdaUpdateWrapper<CrmProduct> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CrmProduct::getProductId, ids);
        wrapper.set(CrmProduct::getOwnerUserId, newOwnerUserId);
        update(wrapper);
        for (Long id : ids) {
            actionRecordUtil.addConversionRecord(id, CrmEnum.PRODUCT, newOwnerUserId, getById(id).getName());
        }
        //修改es
        String ownerUserName = UserCacheUtil.getUserName(newOwnerUserId);
        Map<String, Object> map = new HashMap<>();
        map.put("ownerUserId", newOwnerUserId);
        map.put("ownerUserName", ownerUserName);
        updateField(map, ids);
    }

    /**
     * 下载导入模板
     *
     * @param response 产品id
     * @throws IOException exception
     */
    @Override
    public void downloadExcel(HttpServletResponse response) throws IOException {
        List<CrmModelFiledVO> crmModelFiledList = queryField(null);
        int k = 0;
        for (int i = 0; i < crmModelFiledList.size(); i++) {
            if ("name".equals(crmModelFiledList.get(i).getFieldName())) {
                k = i;
                continue;
            }
            if ("categoryId".equals(crmModelFiledList.get(i).getFieldName())) {
                crmModelFiledList.get(i).setSetting(crmProductCategoryService.queryListName());
            }
        }
        crmModelFiledList.add(k + 1, new CrmModelFiledVO("ownerUserId", FieldEnum.TEXT, "负责人", 1).setIsNull(1));
        ExcelParseUtil.importExcel(new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "产品";
            }
        }, crmModelFiledList, response, "crm");
    }

    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.product, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        exportExcel(search, headList, response, isXls, null);

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 修改产品状态
     *
     * @param productStatus status
     */
    @Override
    public void updateStatus(CrmProductStatusBO productStatus) {
        Integer status = Objects.equals(0, productStatus.getStatus()) ? 0 : 1;
        LambdaUpdateWrapper<CrmProduct> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CrmProduct::getStatus, status);
        wrapper.in(CrmProduct::getProductId, productStatus.getIds());
        update(wrapper);
        updateField("status", status, productStatus.getIds());
    }

    @Override
    public List<CrmModelFiledVO> information(Long productId) {
        return queryField(productId, true);
    }

    /**
     * 查询文件数量
     *
     * @param productId id
     * @return data
     */
    @Override
    public CrmInfoNumVO num(Long productId) {
        CrmProduct crmProduct = getById(productId);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        List<CrmField> crmFields = crmFieldService.queryFileField();
        List<String> batchIdList = new ArrayList<>();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmProductData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmProductData::getValue);
            wrapper.eq(CrmProductData::getBatchId, crmProduct.getBatchId());
            wrapper.in(CrmProductData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            batchIdList.addAll(crmProductDataService.listObjs(wrapper, Object::toString));
        }
        batchIdList.add(crmProduct.getBatchId());
        batchIdList.addAll(crmActivityService.queryFileBatchId(crmProduct.getProductId(), getLabel().getType()));
        CrmInfoNumVO infoNumVO = new CrmInfoNumVO();
        infoNumVO.setFileCount(fileService.queryNum(batchIdList).getData());
        return infoNumVO;
    }

    /**
     * 查询文件列表
     *
     * @param productId id
     * @return file
     */
    @Override
    public List<FileEntity> queryFileList(Long productId) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmProduct crmProduct = getById(productId);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmProduct.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmProductData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmProductData::getValue);
            wrapper.eq(CrmProductData::getBatchId, crmProduct.getBatchId());
            wrapper.in(CrmProductData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = fileService.queryFileList(crmProductDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("产品详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        return fileEntityList;
    }

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"name"};
    }

    /**
     * 获取crm列表类型
     *
     * @return data
     */
    @Override
    public CrmEnum getLabel() {
        return CrmEnum.PRODUCT;
    }

    /**
     * 查询所有字段
     *
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("status", FieldEnum.TEXT, 1));
        return filedList;
    }


    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long productId = updateInformationBO.getId();
        updateInformationBO.getList().forEach(record -> {
            CrmProduct oldProduct = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldProductMap = BeanUtil.beanToMap(oldProduct);
            int two = 2;
            if (record.getInteger(FIELD_TYPE) == 1) {
                Map<String, Object> crmProductMap = new HashMap<>(oldProductMap);
                crmProductMap.put(record.getString("fieldName"), record.get("value"));
                CrmProduct crmProduct = BeanUtil.toBeanIgnoreCase(crmProductMap, CrmProduct.class, true);
                actionRecordUtil.updateRecord(oldProductMap, crmProductMap, CrmEnum.PRODUCT, crmProduct.getName(), crmProduct.getProductId());
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("product_id", updateInformationBO.getId()).update();
            } else if (record.getInteger(FIELD_TYPE) == 0 || record.getInteger(FIELD_TYPE) == two) {
                CrmProductData productData = crmProductDataService.lambdaQuery().select(CrmProductData::getValue, CrmProductData::getId).eq(CrmProductData::getFieldId, record.getLong("fieldId"))
                        .eq(CrmProductData::getBatchId, batchId).one();
                String value = productData != null ? productData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.PRODUCT, BehaviorEnum.UPDATE, productId, oldProduct.getName(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmProductData crmProductData = new CrmProductData();
                crmProductData.setId(productData != null ? productData.getId() : null);
                crmProductData.setFieldId(record.getLong("fieldId"));
                crmProductData.setName(record.getString("fieldName"));
                crmProductData.setValue(newValue);
                crmProductData.setCreateTime(LocalDateTimeUtil.now());
                crmProductData.setBatchId(batchId);
                crmProductDataService.saveOrUpdate(crmProductData);
            }
            updateField(record, productId);
            String categoryId = "categoryId";
            String fieldName = "fieldName";
            if (Objects.equals(categoryId, record.get(fieldName))) {
                record.put("fieldName", "categoryName");
                record.put("type", 1);
                CrmProductCategory category = crmProductCategoryService.getById(record.getLong("value"));
                record.put("value", category.getName());
                updateField(record, productId);
            }
        });
        this.lambdaUpdate().set(CrmProduct::getUpdateTime, new Date()).eq(CrmProduct::getProductId, productId).update();
    }

}
