package com.kakarote.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.kakarote.core.common.enums.DataAuthEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.DataTypeEnum;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.feign.bi.service.BiService;
import com.kakarote.core.feign.crm.entity.BiAuthority;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.feign.crm.entity.BiParams;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.BiTimeUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmInstrumentQueryBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.BO.CrmSearchParamsBO;
import com.kakarote.crm.entity.PO.CrmBusiness;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import com.kakarote.crm.mapper.CrmActivityMapper;
import com.kakarote.crm.mapper.CrmInstrumentMapper;
import com.kakarote.crm.mapper.CrmReceivablesPlanMapper;
import com.kakarote.crm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仪表盘 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Service
public class CrmInstrumentServiceImpl implements CrmInstrumentService {

    @Autowired
    private CrmInstrumentMapper crmInstrumentMapper;

    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    private ICrmBusinessService crmBusinessService;

    @Autowired
    private CrmActivityMapper crmActivityMapper;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ICrmReceivablesPlanService crmReceivablesPlanService;

//    @Autowired
//    @Lazy
//    private BiService biService;

    private static final String CRM_TYPE = "crmType";

    /**
     * 销售简报的详情
     *
     * @param biParams 参数
     * @return data
     */
    @Override
    public BasePage<Map<String, Object>> queryBulletinInfo(CrmInstrumentQueryBO biParams) {
        CrmEnum crmEnum = CrmEnum.parse(biParams.getLabel());
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(crmEnum));
        /*BiTimeUtil.BiTimeEntity biTimeEntity = BiTimeUtil.analyzeTime(biParams);
        BiAuthority biAuthority = handleDataType(biParams);
        CrmEnum crmEnum = CrmEnum.parse(biParams.getLabel());
        List<Long> userIds = AuthUtil.filterUserIdList(crmEnum, CrmAuthEnum.LIST, biAuthority.getUserIds());*/
        if (biTimeEntity.getUserIds().isEmpty()) {
            return new BasePage<>();
        }
        List<Long> userIds = biTimeEntity.getUserIds();
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setSearch(biParams.getSearch());
        searchBO.setLabel(biParams.getLabel());
        searchBO.setOrder(biParams.getType());
        searchBO.setSortField(biParams.getSortField());
        searchBO.getSearchList().add(new CrmSearchBO.Search("ownerUserId", "text", CrmSearchBO.FieldSearchEnum.IS, userIds.stream().map(Object::toString).collect(Collectors.toList())));
        switch (crmEnum) {
            case CONTRACT:
                searchBO.getSearchList().add(new CrmSearchBO.Search("orderDate", "date", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDate(biTimeEntity.getBeginDate()), DateUtil.formatDate(biTimeEntity.getEndDate()))));
                break;
            case RECEIVABLES:
                searchBO.getSearchList().add(new CrmSearchBO.Search("returnTime", "date", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDate(biTimeEntity.getBeginDate()), DateUtil.formatDate(biTimeEntity.getEndDate()))));
                break;
            default:
                searchBO.getSearchList().add(new CrmSearchBO.Search("createTime", "datetime", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDateTime(biTimeEntity.getBeginDate()), DateUtil.formatDateTime(DateUtil.endOfDay(biTimeEntity.getEndDate())))));
                break;
        }
        if (biParams.getCheckStatus() != null && biParams.getCheckStatus() == 1) {
            searchBO.getSearchList().add(new CrmSearchBO.Search("checkStatus", "text", CrmSearchBO.FieldSearchEnum.IS, Arrays.asList("1", "10")));
        }
        switch (crmEnum) {
            case CUSTOMER:
                return ApplicationContextHolder.getBean(ICrmCustomerService.class).queryPageList(searchBO);
            case CONTACTS:
                return ApplicationContextHolder.getBean(ICrmContactsService.class).queryPageList(searchBO);
            case BUSINESS:
                return ApplicationContextHolder.getBean(ICrmBusinessService.class).queryPageList(searchBO);
            case CONTRACT:
                searchBO.getSearchList().add(new CrmSearchBO.Search("checkStatus", "text", CrmSearchBO.FieldSearchEnum.IS, Arrays.asList("1", "10")));
                return ApplicationContextHolder.getBean(ICrmContractService.class).queryPageList(searchBO);
            case RECEIVABLES:
                return ApplicationContextHolder.getBean(ICrmReceivablesService.class).queryPageList(searchBO);
            default:
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
    }

    /**
     * 销售漏斗商机状态列表
     * @param crmSearchParamsBO
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> sellFunnelBusinessList(CrmSearchParamsBO crmSearchParamsBO) {
        crmSearchParamsBO.setMenuId(48L);
        BiTimeUtil.BiTimeEntity record = BiTimeUtil.analyzeType(crmSearchParamsBO);
        List<Long> userIds = record.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        Map<String, Object> map = record.toMap();
        CrmSearchBO search = new CrmSearchBO();
        search.setPage(crmSearchParamsBO.getPage());
        search.setLimit(crmSearchParamsBO.getLimit());
        search.setLabel(CrmEnum.BUSINESS.getType());
        List<CrmSearchBO.Search> searchList = new ArrayList<>();
        if (StrUtil.isNotEmpty(crmSearchParamsBO.getSearch())) {
            map.put("name", crmSearchParamsBO.getSearch());
        }
        CrmSearchBO.Search entity = crmSearchParamsBO.getEntity();
        map.put("typeId", entity.getValues().get(0));
        map.put("statusId", entity.getValues().get(1));
        int three = 3;
        if (entity.getValues().size() == three) {
            map.put("isEnd", entity.getValues().get(2));
        }
        List<String> businessIdList = crmInstrumentMapper.sellFunnelBusinessList(map);

        CrmSearchBO.Search entity2 = new CrmSearchBO.Search();
        entity2.setFormType(FieldEnum.TEXT.getFormType());
        entity2.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        entity2.setValues(businessIdList);
        searchList.add(entity2);

        search.setSearchList(searchList);
        return crmBusinessService.queryPageList(search);
    }

    private BiAuthority handleDataType(BiParams biParams) {
        //数据类型
        DataTypeEnum typeEnum = DataTypeEnum.parse(biParams.getDataType());
        List<Long> userIdList = new ArrayList<>();
        List<Long> deptIdList = new ArrayList<>();
        if (typeEnum != null) {
            if (typeEnum == DataTypeEnum.SELF) {
                userIdList.add(UserUtil.getUserId());
            } else if (typeEnum == DataTypeEnum.SELF_AND_CHILD) {
                userIdList.addAll(adminService.queryChildUserId(UserUtil.getUserId()).getData());
                userIdList.add(UserUtil.getUserId());
            } else if (typeEnum == DataTypeEnum.DEPT) {
                deptIdList.add(UserUtil.getUser().getDeptId());
                userIdList.addAll(adminService.queryUserByDeptIds(deptIdList).getData());
            } else if (typeEnum == DataTypeEnum.DEPT_AND_CHILD) {
                deptIdList.addAll(adminService.queryChildDeptId(UserUtil.getUser().getDeptId()).getData());
                deptIdList.add(UserUtil.getUser().getDeptId());
                userIdList.addAll(adminService.queryUserByDeptIds(deptIdList).getData());
            } else {
                userIdList.addAll(adminService.queryUserList(1).getData());
                deptIdList.addAll(adminService.queryChildDeptId(0L).getData());
            }
        } else {
            if (1 == biParams.getIsUser()) {
                List<Long> userIds = biParams.getUserIds();
                if (userIds == null) {
                    userIds = new ArrayList<>();
                }
                if (biParams.getUserId() != null) {
                    userIds.add(biParams.getUserId());
                }
                if (userIds.size() == 0) {
                    if (UserUtil.isAdmin()) {
                        userIdList.addAll(adminService.queryUserList(1).getData());
                    } else {
                        userIdList.addAll(adminService.queryChildUserId(UserUtil.getUserId()).getData());
                        userIdList.add(UserUtil.getUserId());
                    }
                } else {
                    if (UserUtil.isAdmin()) {
                        userIdList.addAll(userIds);
                    } else {
                        for (Long userId : userIds) {
                            UserInfo userInfo = adminService.queryLoginUserInfo(userId).getData();
                            boolean isAdmin = userInfo.getUserId().equals(UserUtil.getSuperUser()) || Optional.ofNullable(userInfo.getRoles()).orElse(new ArrayList<>()).contains(userInfo.getSuperRoleId());
                            if (!isAdmin) {
                                userIdList.add(userId);
                            }
                        }
                    }
                }
            } else if (0 == biParams.getIsUser() && biParams.getDeptId() != null) {
                List<Long> data = adminService.queryChildDeptId(biParams.getDeptId()).getData();
                data.add(biParams.getDeptId());
                deptIdList.addAll(data);
                userIdList.addAll(adminService.queryUserByDeptIds(deptIdList).getData());
            }
        }
        BiAuthority authority = new BiAuthority();
        authority.setUserIds(userIdList);
        authority.setDeptIds(deptIdList);
        return authority;
    }


    private List<Long> handleUserIds(BiParams biParams, List<Long> userIds) {
        Integer subUser = biParams.getSubUser();
        Long userId = biParams.getUserId();
        Long deptId = biParams.getDeptId();
        Integer dataType = biParams.getDataType();
        Integer crmType = biParams.getLabel();
        if (subUser != null) {
            if (subUser == 1 && ObjectUtil.isAllEmpty(userId, deptId)) {
                userIds = adminService.queryChildUserId(UserUtil.getUserId()).getData();
            } else if (subUser == 0) {
                userIds = Collections.singletonList(UserUtil.getUserId());
            }
        }
        if (UserUtil.isAdmin() && ObjectUtil.isAllEmpty(userId, deptId) && subUser == null && dataType == null && userIds.isEmpty()) {
            userIds = adminService.queryUserList(1).getData();
        }
        if (!UserUtil.isAdmin()) {
            Long followRecordReadMenuId = adminService.queryMenuId("crm", "followRecord", "read").getData();
            List<Long> authUserIdList = AuthUtil.getUserIdByAuth(followRecordReadMenuId);
            if (authUserIdList.size() == 0) {
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
            }
        }
        CrmEnum crmEnum = CrmEnum.parse(crmType);
        List<Long> authUserIds = new ArrayList<>(userIds);
        List<Long> longList = AuthUtil.queryAuthUserList(crmEnum, CrmAuthEnum.LIST);
        authUserIds.retainAll(longList);

        return authUserIds;
    }

    /**
     * 查询销售简报的跟进记录统计
     * @param biParams
     * @return
     */
    @Override
    public List<JSONObject> queryRecordCount(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.READ.getMenuId(null));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<JSONObject> recordList = crmInstrumentMapper.queryRecordCount(biTimeEntity);
        if (recordList.stream().noneMatch(record -> CrmEnum.LEADS.getType().equals(record.getInteger(CRM_TYPE)))) {
            recordList.add(new JSONObject().fluentPut("crmType", CrmEnum.LEADS.getType()).fluentPut("count", 0));
        }
        if (recordList.stream().noneMatch(record -> CrmEnum.CUSTOMER.getType().equals(record.getInteger(CRM_TYPE)))) {
            recordList.add(new JSONObject().fluentPut("crmType", CrmEnum.CUSTOMER.getType()).fluentPut("count", 0));
        }
        if (recordList.stream().noneMatch(record -> CrmEnum.CONTACTS.getType().equals(record.getInteger(CRM_TYPE)))) {
            recordList.add(new JSONObject().fluentPut("crmType", CrmEnum.CONTACTS.getType()).fluentPut("count", 0));
        }
        if (recordList.stream().noneMatch(record -> CrmEnum.BUSINESS.getType().equals(record.getInteger(CRM_TYPE)))) {
            recordList.add(new JSONObject().fluentPut("crmType", CrmEnum.BUSINESS.getType()).fluentPut("count", 0));
        }
        if (recordList.stream().noneMatch(record -> CrmEnum.CONTRACT.getType().equals(record.getInteger(CRM_TYPE)))) {
            recordList.add(new JSONObject().fluentPut("crmType", CrmEnum.CONTRACT.getType()).fluentPut("count", 0));
        }
        return recordList;
    }

    /**
     * 查询跟进记录统计列表
     * @param biParams
     * @return
     */
    @Override
    public BasePage<CrmActivityVO> queryRecordList(CrmActivityQueryBO biParams) {
        if (biParams.getDataType() == null) {
            biParams.setDataType(DataAuthEnum.ALL);
        }
        if (biParams.getRecordType() == null) {
            biParams.setRecordType(0);
        }
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.READ.getMenuId(null));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollectionUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        BasePage<CrmActivityVO> page = crmActivityMapper.queryRecordList(biParams.parse(), biTimeEntity, biParams);
        for (CrmActivityVO crmActivity : page.getList()) {
            SimpleUser data = UserCacheUtil.getSimpleUser(crmActivity.getCreateUserId());
            crmActivity.setUserImg(data.getImg());
            crmActivity.setRealname(data.getRealname());
            crmActivityService.buildActivityRelation(crmActivity);
        }
        return page;
    }

    /**
     * 客户遗忘列表
     * @param biParams
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> forgottenCustomerPageList(CrmInstrumentQueryBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.CUSTOMER));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        Integer day = biParams.getType();
        List<Long> customerIds = crmCustomerService.forgottenCustomer(day, userIds, biParams.getSearch());
        if (customerIds.size() == 0) {
            return new BasePage<>();
        }
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        List<String> collect = customerIds.stream().map(Object::toString).collect(Collectors.toList());
        searchBO.getSearchList().add(new CrmSearchBO.Search("_id", "id", CrmSearchBO.FieldSearchEnum.ID, collect));
        return crmCustomerService.queryPageList(searchBO);
    }

    /**
     * 未联系客户列表
     * @param biParams
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> unContactCustomerPageList(CrmInstrumentQueryBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.CUSTOMER));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        String search = biParams.getSearch();
        List<Long> customerIds = crmCustomerService.unContactCustomer(search, userIds);
        if (customerIds.size() == 0) {
            return new BasePage<>();
        }
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        List<String> collect = customerIds.stream().map(Object::toString).collect(Collectors.toList());
        searchBO.getSearchList().add(new CrmSearchBO.Search("_id", "id", CrmSearchBO.FieldSearchEnum.ID, collect));
        return crmCustomerService.queryPageList(searchBO);
    }

    /**
     * 未跟进客户列表
     * @param biParams
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryNoRecordCustomerList(CrmInstrumentQueryBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.CUSTOMER));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        List<String> ids = crmCustomerService.queryNoRecordCustomerList(userIds,biTimeEntity.getBeginDate(),biTimeEntity.getEndDate());
        String search = biParams.getSearch();
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setSearch(biParams.getSearch());
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        searchBO.getSearchList().add(new CrmSearchBO.Search("createTime", "datetime", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDateTime(biTimeEntity.getBeginDate()), DateUtil.formatDateTime(biTimeEntity.getEndDate()))));
        searchBO.getSearchList().add(new CrmSearchBO.Search("ownerUserId", "user", CrmSearchBO.FieldSearchEnum.CONTAINS, userIds.stream().map(u->u.toString()).collect(Collectors.toList())));
        searchBO.getSearchList().add(new CrmSearchBO.Search("followup","text",CrmSearchBO.FieldSearchEnum.IS,Arrays.asList("0")));
        searchBO.getSearchList().add(new CrmSearchBO.Search("_id", "id", CrmSearchBO.FieldSearchEnum.ID, ids));
        return crmCustomerService.queryPageList(searchBO);
    }

    /**
     * 预计回款
     * @param biParams
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryPlanMoneyList(CrmInstrumentQueryBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.RECEIVABLES_PLAN));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        List<String> ids = ApplicationContextHolder.getBean(CrmReceivablesPlanMapper.class).queryPlanMoneyList(biTimeEntity);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setSearch(biParams.getSearch());
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setLabel(CrmEnum.RECEIVABLES_PLAN.getType());
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        search.setFormType(FieldEnum.TEXT.getFormType());
        search.setValues(ids);
        searchBO.getSearchList().add(search);
        return crmReceivablesPlanService.queryPageList(searchBO);
    }

    /**
     * 商机输赢单列表
     * @param biParams
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryContendBusinessList(CrmInstrumentQueryBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.BUSINESS));
        List<Long> userIds = biTimeEntity.getUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return new BasePage<>();
        }
        LambdaQueryChainWrapper<CrmBusiness> wrapper = crmBusinessService.lambdaQuery().select(CrmBusiness::getBusinessId).eq(CrmBusiness::getIsEnd, biParams.getType()).in(CrmBusiness::getOwnerUserId, userIds);
        if(biParams.getCategoryId()!=null){
            wrapper.eq(CrmBusiness::getTypeId,biParams.getCategoryId());
        }
        List<CrmBusiness> crmBusinesses = wrapper.list();
        List<String> collect = crmBusinesses.stream().map(r -> r.getBusinessId().toString()).collect(Collectors.toList());
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setSearch(biParams.getSearch());
        searchBO.setPage(biParams.getPage());
        searchBO.setLimit(biParams.getLimit());
        searchBO.setLabel(CrmEnum.BUSINESS.getType());
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        search.setFormType(FieldEnum.TEXT.getFormType());
        search.setValues(collect);
        searchBO.getSearchList().add(new CrmSearchBO.Search("createTime", "datetime", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDateTime(biTimeEntity.getBeginDate()), DateUtil.formatDateTime(biTimeEntity.getEndDate()))));
        searchBO.getSearchList().add(search);
        return crmBusinessService.queryPageList(searchBO);
    }
}
