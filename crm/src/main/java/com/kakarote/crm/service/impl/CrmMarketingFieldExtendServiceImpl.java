package com.kakarote.crm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.entity.PO.CrmMarketingFieldExtend;
import com.kakarote.crm.mapper.CrmMarketingFieldExtendMapper;
import com.kakarote.crm.service.ICrmMarketingFieldExtendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 市场活动自定义字段明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Service
public class CrmMarketingFieldExtendServiceImpl extends BaseServiceImpl<CrmMarketingFieldExtendMapper, CrmMarketingFieldExtend> implements ICrmMarketingFieldExtendService {

    @Autowired
    private FieldService fieldService;
    @Override
    public List<CrmMarketingFieldExtend> queryCrmFieldExtend(Long parentFieldId) {
        List<CrmMarketingFieldExtend> fieldExtends = lambdaQuery().eq(CrmMarketingFieldExtend::getParentFieldId, parentFieldId).list();
        fieldExtends.forEach(fieldExtend -> recordToFormType(fieldExtend, FieldEnum.parse(fieldExtend.getType())));
        return fieldExtends;
    }

    @Override
    public boolean saveOrUpdateCrmFieldExtend(List<CrmMarketingFieldExtend> crmMarketingFieldExtendList, Long parentFieldId, boolean isUpdate) {
        if (parentFieldId == null){
            return false;
        }
        if (isUpdate) {
            this.deleteCrmFieldExtend(parentFieldId);
        }
        for (CrmMarketingFieldExtend fieldExtend : crmMarketingFieldExtendList) {
            if (ObjectUtil.isEmpty(fieldExtend.getDefaultValue())) {
                fieldExtend.setDefaultValue("");
            } else {
                boolean isNeedHandle = fieldService.equalsByType(fieldExtend.getType(), FieldEnum.AREA, FieldEnum.AREA_POSITION, FieldEnum.CURRENT_POSITION);
                if (isNeedHandle) {
                    fieldExtend.setDefaultValue(JSON.toJSONString(fieldExtend.getDefaultValue()));
                }
                if (fieldExtend.getDefaultValue() instanceof Collection) {
                    fieldExtend.setDefaultValue(StrUtil.join(Const.SEPARATOR, fieldExtend.getDefaultValue()));
                }
            }
            fieldExtend.setId(null);
            fieldExtend.setParentFieldId(parentFieldId);
            save(fieldExtend);
        }
        return true;
    }

    @Override
    public boolean deleteCrmFieldExtend(Long parentFieldId) {
        return lambdaUpdate().eq(CrmMarketingFieldExtend::getParentFieldId,parentFieldId).remove();
    }

    private void recordToFormType(CrmMarketingFieldExtend record, FieldEnum typeEnum) {
        record.setFormType(typeEnum.getFormType());
        switch (typeEnum) {
            case CHECKBOX:
                record.setDefaultValue(StrUtil.splitTrim((CharSequence) record.getDefaultValue(), Const.SEPARATOR));
            case SELECT:
                if(Objects.equals(record.getRemark(),FieldEnum.OPTIONS_TYPE.getFormType())) {
                    LinkedHashMap<String, Object> optionsData = JSON.parseObject(record.getOptions(), LinkedHashMap.class);
                    record.setOptionsData(optionsData);
                    record.setSetting(new ArrayList<>(optionsData.keySet()));
                }else {
                    record.setSetting(StrUtil.splitTrim(record.getOptions(), Const.SEPARATOR));
                }
                break;
            case DATE_INTERVAL:
                String dataValueStr = Optional.ofNullable(record.getDefaultValue()).orElse("").toString();
                record.setDefaultValue(StrUtil.split(dataValueStr, Const.SEPARATOR));
                break;
            case USER:
            case STRUCTURE:
                record.setDefaultValue(new ArrayList<>(0));
                break;
            case AREA:
            case AREA_POSITION:
            case CURRENT_POSITION:
                String defaultValue = Optional.ofNullable(record.getDefaultValue()).orElse("").toString();
                record.setDefaultValue(JSON.parse(defaultValue));
                break;
            default:
                record.setSetting(new ArrayList<>());
                break;
        }
    }
}
