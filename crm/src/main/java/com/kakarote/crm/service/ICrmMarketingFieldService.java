package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.BO.MarketingFieldBO;
import com.kakarote.crm.entity.PO.CrmMarketingField;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
public interface ICrmMarketingFieldService extends BaseService<CrmMarketingField> {

    List<CrmMarketingField> queryField(Long id);

    void recordToFormType(List<CrmMarketingField> list);

    void transferFieldList(List<CrmMarketingField> recordList, Integer isDetail);

    void saveField(MarketingFieldBO marketingFieldBO);

    List<List<CrmModelFiledVO>> queryFormPositionField(Long id);
}
