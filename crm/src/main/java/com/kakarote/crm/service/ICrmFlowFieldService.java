package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmFlowField;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 自定义字段表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface ICrmFlowFieldService extends BaseService<CrmFlowField> {

    /**
     * 保存阶段流程表单信息
     *
     * @param settingId settingId
     * @param fieldList 字段列表
     */
    public Map<String,String> saveFlowField(Long settingId, List<CrmFlowField> fieldList);

    /**
     * 查询阶段流程表单信息
     *
     * @param settingId settingId
     * @return data
     */
    public List<List<CrmFlowField>> queryFieldList(Long settingId, boolean queryExamine);
}
