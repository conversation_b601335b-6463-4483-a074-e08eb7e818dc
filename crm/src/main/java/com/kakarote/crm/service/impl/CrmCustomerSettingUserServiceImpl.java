package com.kakarote.crm.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.entity.PO.CrmCustomerSettingUser;
import com.kakarote.crm.mapper.CrmCustomerSettingUserMapper;
import com.kakarote.crm.service.ICrmCustomerSettingUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 员工拥有以及锁定客户员工关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@Service
public class CrmCustomerSettingUserServiceImpl extends BaseServiceImpl<CrmCustomerSettingUserMapper, CrmCustomerSettingUser> implements ICrmCustomerSettingUserService {

}
