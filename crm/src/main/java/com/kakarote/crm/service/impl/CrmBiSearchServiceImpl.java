package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.enums.DateFilterEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.bi.service.BiService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.BiSearchBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.PO.CrmOwnerRecord;
import com.kakarote.crm.entity.PO.CrmReturnVisit;
import com.kakarote.crm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * 商业智能查询
 * @description:
 * @author: zyy
 * @date: 2022-02-28
 */
@Service
public class CrmBiSearchServiceImpl implements CrmBiSearchService {

    @Autowired
    ICrmCustomerService crmCustomerService;

    @Autowired
    ICrmCustomerPoolService crmCustomerPoolService;

    @Autowired
    ICrmReturnVisitService crmReturnVisitService;

    @Autowired
    ICrmContactsService crmContactsService;

    @Autowired
    ICrmContractService crmContractService;

    @Autowired
    ICrmBusinessService crmBusinessService;

    @Autowired
    ICrmInvoiceService crmInvoiceService;

    @Autowired
    ICrmProductService crmProductService;

    @Autowired
    ICrmReceivablesService crmReceivablesService;

    @Autowired
    ICrmReceivablesPlanService crmReceivablesPlanService;

    @Autowired
    @Lazy
    BiService biService;


    @Override
    public BasePage<Map<String, Object>> queryVisitContractPageList(BiSearchBO biSearchBO) {
        List<String> data = crmReturnVisitService.lambdaQuery().select(CrmReturnVisit::getContractId).eq(CrmReturnVisit::getOwnerUserId, biSearchBO.getId()).list()
                .stream().map(r -> r.getContractId().toString()).collect(Collectors.toList());
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setSearch(biSearchBO.getSearch());
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setLabel(CrmEnum.CONTRACT.getType());
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        search.setFormType(FieldEnum.TEXT.getFormType());
        search.setValues(data);
        biSearchBO.getSearchList().add(search);
        searchBO.setSearchList(biSearchBO.getSearchList());
        return crmContractService.queryPageList(searchBO);
    }

    @Override
    public BasePage<Map<String, Object>> searchCustomerPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        BasePage<Map<String, Object>> basePage = crmCustomerService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchBusinessPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.BUSINESS.getType());
        BasePage<Map<String, Object>> basePage = crmBusinessService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchContractPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.CONTRACT.getType());
        BasePage<Map<String, Object>> basePage = crmContractService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchContactsPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.CONTACTS.getType());
        BasePage<Map<String, Object>> basePage = crmContactsService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchInvoicePageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.INVOICE.getType());
        BasePage<Map<String, Object>> basePage = crmInvoiceService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchProductPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.PRODUCT.getType());
        BasePage<Map<String, Object>> basePage = crmProductService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchReceivablesPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.RECEIVABLES.getType());
        BasePage<Map<String, Object>> basePage = crmReceivablesService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchReceivablesPlanPageList(BiSearchBO biSearchBO) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setName("ownerUserId");
        search.setFormType(FieldEnum.USER.getFormType());
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.CONTAINS);
        search.setValues(allUserList.stream().map(String::valueOf).collect(Collectors.toList()));
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.RECEIVABLES_PLAN.getType());
        BasePage<Map<String, Object>> basePage = crmReceivablesPlanService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> searchPoolCustomerPageList(BiSearchBO biSearchBO) {
        BiParamsUtil.BiTimeEntity timeEntity = new BiParamsUtil.BiTimeEntity();
        //用户要根据每个类型取不同数据，只是date
        BiParamsUtil.analyzeDate(timeEntity, biSearchBO);
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biSearchBO);
        List<Long> userIds = new ArrayList<>();
        if(biSearchBO.getId()!=null){
            if (!allUserList.contains(biSearchBO.getId()) ){
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
            }
            userIds.add(biSearchBO.getId());
        }else {
            userIds.addAll(allUserList);
        }
        List<CrmOwnerRecord> list = ApplicationContextHolder.getBean(ICrmOwnerRecordService.class).lambdaQuery().eq(CrmOwnerRecord::getType, 9).between(CrmOwnerRecord::getCreateTime, timeEntity.getBeginDate(), timeEntity.getEndDate())
                .in(CrmOwnerRecord::getPreOwnerUserId, userIds).list();
        List<String> collect = list.stream().map(crm -> crm.getTypeId().toString()).collect(Collectors.toList());
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.CUSTOMER_POOL.getType());
        searchBO.getSearchList().add(new CrmSearchBO.Search("customerId", "text" , CrmSearchBO.FieldSearchEnum.IS, collect));
        BasePage<Map<String, Object>> basePage = crmCustomerPoolService.queryPageList(searchBO,false);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> queryCustomerRecordList(BiSearchBO biSearchBO) {
        biSearchBO.setType(CrmEnum.CUSTOMER.getType());
        JSONObject map = BeanUtil.copyProperties(biSearchBO, JSONObject.class);
        map.put("dataType",biSearchBO.getDataType().getValue());
        map.put("dateFilter",biSearchBO.getDateFilter().getValue());
        map.entrySet().removeIf(entry -> entry.getValue() == null);
        List<String> data = biService.queryRecordCustomerList(map).getData();
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        search.setFormType(FieldEnum.TEXT.getFormType());
        search.setValues(data);
        biSearchBO.getSearchList().add(search);
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setPage(biSearchBO.getPage());
        searchBO.setLimit(biSearchBO.getLimit());
        searchBO.setSearchList(biSearchBO.getSearchList());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        BasePage<Map<String, Object>> basePage = crmCustomerService.queryPageList(searchBO);
        return basePage;
    }

    @Override
    public BasePage<Map<String, Object>> employeesSatisfactionTable(BiSearchBO biEntityParams) {
        DateFilterEnum dateFilter = biEntityParams.getDateFilter();
        CrmSearchBO search = new CrmSearchBO();
        search.setSearchList(biEntityParams.getSearchList());
        search.setSearch(biEntityParams.getSearch());
        Map<String, Object> map = BeanUtil.beanToMap(biEntityParams);
        map.put("dataType",biEntityParams.getDataType().getValue());
        map.put("dateFilter",dateFilter.getValue());
        map.entrySet().removeIf(entry -> entry.getValue() == null);
        List<String> data = ApplicationContextHolder.getBean(BiService.class).queryContactsByCustomerSatisfaction(map).getData();
        CrmSearchBO.Search customer = new CrmSearchBO.Search();
        customer.setFormType(FieldEnum.TEXT.getFormType());
        customer.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        customer.setValues(data);
        search.getSearchList().add(customer);
        BasePage<Map<String, Object>> page = crmContractService.queryPageList(search);
        return page;
    }

    @Override
    public BasePage<Map<String, Object>> productSatisfactionTable(BiSearchBO biEntityParams) {
        CrmSearchBO search = new CrmSearchBO();
        search.setSearchList(biEntityParams.getSearchList());
        search.setSearch(biEntityParams.getSearch());
        Map<String, Object> map = BeanUtil.beanToMap(biEntityParams);
        map.put("dataType",biEntityParams.getDataType().getValue());
        map.put("dateFilter",biEntityParams.getDateFilter().getValue());
        map.put("id",biEntityParams.getId());
        map.entrySet().removeIf(entry -> entry.getValue() == null);
        List<String> data = ApplicationContextHolder.getBean(BiService.class).queryContractsByProductSatisfaction(map).getData();
        CrmSearchBO.Search contacts = new CrmSearchBO.Search();
        contacts.setFormType(FieldEnum.TEXT.getFormType());
        contacts.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        contacts.setValues(data);
        search.getSearchList().add(contacts);
        BasePage<Map<String, Object>> page = crmContractService.queryPageList(search);
        return page;
    }

    @Override
    public BasePage<Map<String, Object>> queryProductSucceedCustomerList(BiSearchBO biEntityParams) {
        Map<String,Object> map = BeanUtil.copyProperties(biEntityParams, Map.class);
        map.put("dataType",biEntityParams.getDataType().getValue());
        map.put("dateFilter",biEntityParams.getDateFilter().getValue());
        map.put("categoryId",biEntityParams.getId());
        map.entrySet().removeIf(entry -> entry.getValue() == null);
        List<String> data = biService.queryProductCustomerList(map).getData();
        CrmSearchBO searchBO = new CrmSearchBO();
        searchBO.setSearch(biEntityParams.getSearch());
        searchBO.setPage(biEntityParams.getPage());
        searchBO.setLimit(biEntityParams.getLimit());
        searchBO.setLabel(CrmEnum.CUSTOMER.getType());
        CrmSearchBO.Search search = new CrmSearchBO.Search();
        search.setSearchEnum(CrmSearchBO.FieldSearchEnum.ID);
        search.setFormType(FieldEnum.TEXT.getFormType());
        search.setValues(data);
        searchBO.getSearchList().add(search);
        return crmCustomerService.queryPageList(searchBO);
    }
}
