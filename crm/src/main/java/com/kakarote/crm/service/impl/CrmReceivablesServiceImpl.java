package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.feign.examine.entity.ExamineInfoVo;
import com.kakarote.core.feign.examine.entity.ExamineRecordReturnVO;
import com.kakarote.core.feign.examine.entity.ExamineRecordSaveBO;
import com.kakarote.core.feign.examine.service.ExamineService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.AdminMessageUtil;
import com.kakarote.core.utils.BaseUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.*;
import com.kakarote.crm.constant.*;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmReceivablesMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.kakarote.core.servlet.ApplicationContextHolder.getBean;

/**
 * <p>
 * 回款表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
@Slf4j
public class CrmReceivablesServiceImpl extends BaseServiceImpl<CrmReceivablesMapper, CrmReceivables> implements ICrmReceivablesService, CrmPageService {

    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmReceivablesDataService crmReceivablesDataService;

    @Autowired
    private ICrmContractService crmContractService;

    @Autowired
    private ICrmExamineRecordService examineRecordService;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmReceivablesPlanService crmReceivablesPlanService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    @Lazy
    private ExamineService examineService;

    @Autowired
    private FieldService fieldService;

    @Autowired
    private Redis redis;

    private static final int ONE_ZERO = 10;

    private static final int FIVE = 5;

    private static final int TWO = 2;

    private static final int FOUR = 4;

    private static final String FIELD_TYPE = "fieldType";

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"number", "customerName"};
    }

    @Override
    public CrmEnum getLabel() {
        return CrmEnum.RECEIVABLES;
    }

    /**
     * 查询所有字段
     *
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("lastTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("checkStatus", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("receivablesPlanId", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("teamMemberIds", FieldEnum.USER, 0));
        return filedList;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            JSONObject customer = new JSONObject();
            customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            crmModel.put("customerId", customerList);
            crmModel.put("contractId", Collections.singletonList(new JSONObject().fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum"))));
        }
        return crmFieldService.queryField(crmModel);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            JSONObject customer = new JSONObject();
            customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            crmModel.put("customerId", customerList);
            crmModel.put("contractId", Collections.singletonList(new JSONObject().fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum"))));
        }
        List<CrmModelFiledVO> filedVOS = crmFieldService.queryField(crmModel);
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            filedVOS.addAll(modelFiledVOS);
        }
        return filedVOS;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            JSONObject customer = new JSONObject();
            customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            crmModel.put("customerId", customerList);
            crmModel.put("contractId", Collections.singletonList(new JSONObject().fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum"))));
            // 去除编辑掩码
            crmModel.put("update", true);
        }
        return crmFieldService.queryFormPositionFieldVO(crmModel);
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        CrmSearchBO search1 = ObjectUtil.cloneByStream(search);
        BasePage<Map<String, Object>> basePage = queryList(search, false);
        SearchRequest searchRequest = new SearchRequest(getIndex());
        searchRequest.types(getDocType());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = createQueryBuilder(search1);
        queryBuilder.must(QueryBuilders.termsQuery("checkStatus", Arrays.asList(1, 10)));
        setCrmDataAuth(queryBuilder);
        sourceBuilder.query(queryBuilder);
        sourceBuilder.aggregation(AggregationBuilders.sum("receivablesMoney").field("money"));
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse searchCount = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = searchCount.getAggregations();
            Map<String, Object> countMap = new HashMap<>();
            ParsedSum receivablesMoney = aggregations.get("receivablesMoney");
            countMap.put("receivablesMoney", receivablesMoney.getValue());
            basePage.setExtraData(new JSONObject().fluentPut("money", countMap));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return basePage;
    }

    @Override
    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<CrmReceivables> list = lambdaQuery().select(CrmReceivables::getReceivablesId, CrmReceivables::getNumber).in(CrmReceivables::getReceivablesId, ids).list();
        return list.stream().map(crmLeads -> {
            SimpleCrmEntity simpleCrmEntity = new SimpleCrmEntity();
            simpleCrmEntity.setId(crmLeads.getReceivablesId());
            simpleCrmEntity.setName(crmLeads.getNumber());
            return simpleCrmEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryById(id, UserUtil.getUserId());
            crmModel.setLabel(CrmEnum.RECEIVABLES.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            crmModel.put("createUserName", UserCacheUtil.getUserName((Long) crmModel.get("createUserId")));
            crmReceivablesDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
        } else {
            crmModel = new CrmModel(CrmEnum.RECEIVABLES.getType());
        }
        return crmModel;
    }

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.receivables, action = CrmMsgActionEnum.save)
    public void addOrUpdate(CrmContractSaveBO crmModel) {
        CrmReceivables crmReceivables = BeanUtil.copyProperties(crmModel.getEntity(), CrmReceivables.class);
        CrmContract crmContract = crmContractService.getById(crmReceivables.getContractId());
        if (crmContract == null || !Arrays.asList(1, ONE_ZERO).contains(crmContract.getCheckStatus())) {
            throw new CrmException(CrmCodeEnum.CRM_RECEIVABLES_ADD_ERROR);
        }
        if (crmReceivables.getCustomerId() == null) {
            crmReceivables.setCustomerId(crmContract.getCustomerId());
        }
        crmReceivables.setCreateUserId(UserUtil.getUserId());
        String batchId = StrUtil.isNotEmpty(crmReceivables.getBatchId()) ? crmReceivables.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_business_data"));
        crmReceivablesDataService.saveData(crmModel.getField(), batchId);
        ExamineRecordSaveBO examineRecordSaveBO = crmModel.getExamineFlowData();
        ExamineRecordReturnVO examineData = null;
        // 是否新建
        Boolean isAdd = false;

        Long receivablesId = crmReceivables.getReceivablesId() == null ? BaseUtil.getNextId() : crmReceivables.getReceivablesId();

        //把数据放入缓存，供系统消息展示预览数据使用
        redis.setNx(CrmCacheKey.CRM_ADMIN_MESSAGE + receivablesId, 60L, BeanUtil.beanToMap(crmReceivables));
        if (crmReceivables.getReceivablesId() == null) {
            crmReceivables.setCreateTime(LocalDateTimeUtil.now());
            crmReceivables.setUpdateTime(LocalDateTimeUtil.now());
            crmReceivables.setBatchId(batchId);
            crmReceivables.setOwnerUserId(UserUtil.getUserId());
            if (crmReceivables.getOwnerUserId() == null) {
                crmReceivables.setOwnerUserId(UserUtil.getUserId());
            }
            final Optional<CrmModelFiledVO> modelFiledOptional = queryDefaultField().stream().filter(data -> FieldEnum.SERIAL_NUMBER.getType().equals(data.getType())).findFirst();
            if (modelFiledOptional.isPresent()) {
                Map<String, Object> map = BeanUtil.beanToMap(crmReceivables);
                crmModel.getField().forEach(field -> {
                    map.put(field.getFieldName(), field.getValue());
                });
                final String generateNumber = getBean(ICrmFieldNumberDataService.class).generateNumber(getLabel(), modelFiledOptional.get(), map);
                crmReceivables.setNumber(generateNumber);
            }
            crmReceivables.setReceivablesId(receivablesId);
            save(crmReceivables);
            if (crmReceivables.getCheckStatus() == null || crmReceivables.getCheckStatus() != FIVE) {
                this.supplementFieldInfo(2, crmReceivables.getReceivablesId(), null, examineRecordSaveBO);
                examineRecordSaveBO.setTitle(crmReceivables.getNumber());
                examineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
                crmReceivables.setExamineRecordId(examineData.getRecordId());
                crmReceivables.setCheckStatus(examineData.getExamineStatus());
            }
            updateById(crmReceivables);
            CrmReceivablesPlan crmReceivablesPlan = crmReceivablesPlanService.getById(crmReceivables.getReceivablesPlanId());
            if (crmReceivablesPlan != null) {
                crmReceivablesPlan.setReceivablesId(crmReceivables.getReceivablesId());
                crmReceivablesPlan.setUpdateTime(LocalDateTimeUtil.now());
                ElasticUtil.updateField(restHighLevelClient, "receivablesId", crmReceivables.getReceivablesId(), Collections.singletonList(crmReceivablesPlan.getReceivablesPlanId()), CrmEnum.RECEIVABLES_PLAN.getIndex());
                crmReceivablesPlanService.updateById(crmReceivablesPlan);
            }
            crmActivityService.addActivity(2, CrmActivityEnum.RECEIVABLES, crmReceivables.getReceivablesId());
            actionRecordUtil.addRecord(crmReceivables.getReceivablesId(), CrmEnum.RECEIVABLES, crmReceivables.getNumber());
            crmReceivables = getById(crmReceivables.getReceivablesId());
            if (crmReceivables.getCheckStatus() == 1 || crmReceivables.getCheckStatus() == ONE_ZERO) {
                examineRecordService.updateContractMoney(crmReceivables.getReceivablesId());
                crmReceivablesPlanService.updateReceivedStatus(CrmEnum.RECEIVABLES, crmReceivables, crmReceivables.getCheckStatus());
            }
        } else {
            isAdd = true;
            CrmReceivables receivables = getById(crmReceivables.getReceivablesId());
            if (receivables.getCheckStatus() == 1) {
                throw new CrmException(CrmCodeEnum.CRM_RECEIVABLES_EXAMINE_PASS_ERROR);
            }
            if (!Arrays.asList(TWO, FOUR, FIVE, ONE_ZERO).contains(receivables.getCheckStatus())) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
            }
            if (crmReceivables.getCheckStatus() == null || crmReceivables.getCheckStatus() != FIVE) {
                this.supplementFieldInfo(2, receivables.getReceivablesId(), receivables.getExamineRecordId(), examineRecordSaveBO);
                examineRecordSaveBO.setTitle(crmReceivables.getNumber());
                examineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
                crmReceivables.setExamineRecordId(examineData.getRecordId());
                crmReceivables.setCheckStatus(examineData.getExamineStatus());
            }
            crmReceivables.setUpdateTime(LocalDateTimeUtil.now());
            crmReceivablesPlanService.update().eq("receivables_id", receivables.getReceivablesId()).set("receivables_id", null).update();
            CrmReceivablesPlan crmReceivablesPlan = crmReceivablesPlanService.getById(crmReceivables.getReceivablesPlanId());
            if (crmReceivablesPlan != null) {
                crmReceivablesPlan.setReceivablesId(crmReceivables.getReceivablesId());
                crmReceivablesPlan.setUpdateTime(LocalDateTimeUtil.now());
                ElasticUtil.updateField(restHighLevelClient, "receivablesId", crmReceivables.getReceivablesId(), Collections.singletonList(crmReceivablesPlan.getReceivablesPlanId()), CrmEnum.RECEIVABLES_PLAN.getIndex());
                crmReceivablesPlanService.updateById(crmReceivablesPlan);
            }
            ApplicationContextHolder.getBean(ICrmBackLogDealService.class).deleteByTypes(null, CrmEnum.RECEIVABLES, crmReceivables.getReceivablesId(), CrmBackLogEnum.CHECK_RECEIVABLES);
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(receivables), BeanUtil.beanToMap(crmReceivables), CrmEnum.CONTRACT, crmContract.getName(), crmContract.getContractId());
            updateById(crmReceivables);
            crmReceivables = getById(crmReceivables.getReceivablesId());
            if (crmReceivables.getCheckStatus() == ONE_ZERO) {
                examineRecordService.updateContractMoney(crmReceivables.getReceivablesId());
                crmReceivablesPlanService.updateReceivedStatus(CrmEnum.RECEIVABLES, crmReceivables, crmReceivables.getCheckStatus());
            }
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmReceivables));
        savePage(crmModel, crmReceivables.getReceivablesId(), false);

        if (isAdd) {
            // 发送消息
            MsgBodyBO msgBody = new MsgBodyBO();
            msgBody.setMsgKey(IdUtil.simpleUUID());
            msgBody.setMsgTag(getMsgLabelEnum().name());
            msgBody.setAction(CrmMsgActionEnum.save.name());
            msgBody.setCurrentUser(UserUtil.getUser());
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmReceivables.getReceivablesId());
            operateObject.put("name", crmReceivables.getNumber());
            operateObject.put("ownerUserId", crmReceivables.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmReceivables.getOwnerUserId()));
            String title = ResourcesUtil.getMessage("m2", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), "@NAME");
            msgBody.setTitle(title);
            msgBody.setOperateObject(Arrays.asList(operateObject));
            AdminMessageUtil.setMsgBody(msgBody);
        }
    }

    @Override
    public void setOtherField(Map<String, Object> map) {
        String customerName = crmCustomerService.getCustomerName((Long) map.get("customerId"));
        map.put("customerName", customerName);
        CrmContract contract = crmContractService.getById((Serializable) map.get("contractId"));
        if (contract != null) {
            map.put("contractNum", contract.getNum());
            map.put("contractMoney", contract.getMoney());
        }
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);


        Long receivablesPlanId = (Long) map.get("receivablesPlanId");
        if (receivablesPlanId != null) {
            CrmReceivablesPlan receivablesPlan = crmReceivablesPlanService.lambdaQuery().select(CrmReceivablesPlan::getNum).eq(CrmReceivablesPlan::getReceivablesPlanId, receivablesPlanId).one();
            map.put("planNum", receivablesPlan.getNum());
        }

    }

    @Override
    public Dict getSearchTransferMap() {
        return Dict.create()
                .set("customerId", "customerName").set("contractId", "contractNum").set("receivablesPlanId", "planNum");
    }

    /**
     * 删除回款数据
     *
     * @param ids ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        Integer count = crmReceivablesPlanService.lambdaQuery().in(CrmReceivablesPlan::getReceivablesId, ids).count();
        if (count != 0) {
            throw new CrmException(CrmCodeEnum.CRM_DATA_JOIN_ERROR);
        }
        for (Long id : ids) {

            CrmReceivables receivables = getById(id);
            boolean bol = (receivables.getCheckStatus() != 4 && receivables.getCheckStatus() != 5) && !UserUtil.isAdmin();
            if (bol) {
                throw new CrmException(CrmCodeEnum.CAN_ONLY_DELETE_WITHDRAWN_AND_SUBMITTED_EXAMINE);
            }
            //删除跟进记录
            crmActivityService.deleteActivityRecord(Collections.singletonList(id));
            //删除字段操作记录
            crmActionRecordService.deleteActionRecord(CrmEnum.RECEIVABLES, Collections.singletonList(receivables.getReceivablesId()));
            //删除自定义字段
            crmReceivablesDataService.deleteByBatchId(Collections.singletonList(receivables.getBatchId()));
            //删除文件
            adminFileService.delete(Collections.singletonList(receivables.getBatchId()));
            if (ObjectUtil.isNotEmpty(receivables.getExamineRecordId())) {
                examineService.deleteExamineRecord(receivables.getExamineRecordId());
            }
            receivables.setCheckStatus(7);
            updateById(receivables);
            //还原合同金额
            examineRecordService.updateContractMoney(id);
        }
        deletePage(ids);
    }

    /**
     * 修改回款负责人
     *
     * @param changeOwnerUserBO 负责人变更BO
     */
    @Override
    @Message(label = CrmMsgLabelEnum.receivables, action = CrmMsgActionEnum.transfer)
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO) {
        ;
        LambdaUpdateWrapper<CrmReceivables> wrapper = new LambdaUpdateWrapper<>();
        Long newOwnerUserId = changeOwnerUserBO.getOwnerUserId();
        List<Long> ids = changeOwnerUserBO.getIds();
        wrapper.in(CrmReceivables::getReceivablesId, ids);
        wrapper.set(CrmReceivables::getOwnerUserId, newOwnerUserId);
        update(wrapper);
        // 操作对象数据
        List<JSONObject> operateObjects = new ArrayList<>();
        for (Long id : ids) {
            CrmReceivables receivables = getById(id);
            actionRecordUtil.addConversionRecord(id, CrmEnum.RECEIVABLES, newOwnerUserId, receivables.getNumber());
            if (2 == changeOwnerUserBO.getTransferType() && !newOwnerUserId.equals(receivables.getOwnerUserId())) {
                ApplicationContextHolder.getBean(ICrmTeamMembersService.class).addSingleMember(getLabel(), receivables.getReceivablesId(), receivables.getOwnerUserId(), changeOwnerUserBO.getPower(), changeOwnerUserBO.getExpiresTime(), receivables.getNumber());
            }
            ApplicationContextHolder.getBean(ICrmTeamMembersService.class).deleteMember(getLabel(), new CrmMemberSaveBO(id, newOwnerUserId));

            // 构建操作对象数据
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", receivables.getReceivablesId());
            operateObject.put("name", receivables.getNumber());
            operateObject.put("ownerUserId", receivables.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(receivables.getOwnerUserId()));
            operateObjects.add(operateObject);
        }
        //修改es
        String ownerUserName = UserCacheUtil.getUserName(newOwnerUserId);
        Map<String, Object> map = new HashMap<>();
        map.put("ownerUserId", newOwnerUserId);
        map.put("ownerUserName", ownerUserName);
        updateField(map, ids);

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.transform.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m3", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(),
                "@NAME", ownerUserName);
        msgBody.setTitle(title);
        msgBody.setOperateObject(operateObjects);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.receivables, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        exportExcel(search, headList, response, isXls, (record, headMap) -> {
            for (String fieldName : headMap.keySet()) {
                record.put(fieldName, ActionRecordUtil.parseExportValue(record.get(fieldName), headMap.get(fieldName), false));
            }
            String checkS = "checkStatus";
            if (ObjectUtil.isEmpty(record.get(checkS))) {
                return;
            }
            String checkStatus;
            //0待审核、1通过、2拒绝、3审核中 4:撤回 5 未提交
            switch (TypeUtils.castToInt(record.get("checkStatus"))) {
                case 1:
                    checkStatus = "通过";
                    break;
                case 2:
                    checkStatus = "拒绝";
                    break;
                case 3:
                    checkStatus = "审核中";
                    break;
                case 4:
                    checkStatus = "撤回";
                    break;
                case 5:
                    checkStatus = "未提交";
                    break;
                case 7:
                    checkStatus = "已删除";
                    break;
                case 8:
                    checkStatus = "作废";
                    break;
                case 10:
                    checkStatus = "正常";
                    break;
                default:
                    checkStatus = "待审核";
            }
            record.put("checkStatus", checkStatus);
        });
        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 查询文件数量
     *
     * @param id id
     * @return data
     */
    @Override
    public CrmInfoNumVO num(Long id) {
        List<String> batchIdList = new ArrayList<>();
        CrmReceivables crmReceivables = getById(id);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmReceivablesData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmReceivablesData::getValue);
            wrapper.eq(CrmReceivablesData::getBatchId, crmReceivables.getBatchId());
            wrapper.in(CrmReceivablesData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            batchIdList.addAll(crmReceivablesDataService.listObjs(wrapper, Object::toString));
        }
        CrmInfoNumVO infoNumVO = new CrmInfoNumVO();
        infoNumVO.setFileCount(fileService.queryNum(batchIdList).getData());
        infoNumVO.setMemberCount(ApplicationContextHolder.getBean(ICrmTeamMembersService.class).queryMemberCount(getLabel(), crmReceivables.getReceivablesId(), crmReceivables.getOwnerUserId()));
        return infoNumVO;
    }

    /**
     * 查询文件列表
     *
     * @param id id
     * @return file
     */
    @Override
    public List<FileEntity> queryFileList(Long id) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmReceivables crmReceivables = getById(id);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmReceivables.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmReceivablesData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmReceivablesData::getValue);
            wrapper.eq(CrmReceivablesData::getBatchId, crmReceivables.getBatchId());
            wrapper.in(CrmReceivablesData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = fileService.queryFileList(crmReceivablesDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("回款详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        return fileEntityList;
    }

    @Override
    public List<CrmModelFiledVO> information(Long contractId) {
        return queryField(contractId, true);
    }


    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long receivablesId = updateInformationBO.getId();
        List<ExamineInfoVo> infoVos = examineService.queryNormalExamine(2).getData();
        CrmReceivables receivables = getById(receivablesId);
        if (!receivables.getCreateUserId().equals(UserUtil.getUserId()) && CollUtil.isNotEmpty(infoVos)) {
            throw new CrmException(CrmCodeEnum.CRM_RECEIVABLES_EDIT_ERROR);
        }
        if (receivables.getCheckStatus() == 1) {
            throw new CrmException(CrmCodeEnum.CRM_RECEIVABLES_EXAMINE_PASS_ERROR);
        }
        if (!Arrays.asList(TWO, FOUR, FIVE, ONE_ZERO).contains(receivables.getCheckStatus())) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
        }
        updateInformationBO.getList().forEach(record -> {
            CrmReceivables oldReceivables = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldReceivablesMap = BeanUtil.beanToMap(oldReceivables);
            if (record.getInteger(FIELD_TYPE) == 1) {
                Map<String, Object> crmReceivablesMap = new HashMap<>(oldReceivablesMap);
                crmReceivablesMap.put(record.getString("fieldName"), record.get("value"));
                CrmReceivables crmReceivables = BeanUtil.toBeanIgnoreCase(crmReceivablesMap, CrmReceivables.class, true);
                actionRecordUtil.updateRecord(oldReceivablesMap, crmReceivablesMap, CrmEnum.RECEIVABLES, crmReceivables.getNumber(), crmReceivables.getReceivablesId());
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("receivables_id", updateInformationBO.getId()).update();
            } else if (record.getInteger(FIELD_TYPE) == 0 || record.getInteger(FIELD_TYPE) == TWO) {
                CrmReceivablesData receivablesData = crmReceivablesDataService.lambdaQuery().select(CrmReceivablesData::getValue, CrmReceivablesData::getId).eq(CrmReceivablesData::getFieldId, record.getInteger("fieldId"))
                        .eq(CrmReceivablesData::getBatchId, batchId).one();
                String value = receivablesData != null ? receivablesData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.RECEIVABLES, BehaviorEnum.UPDATE, receivablesId, oldReceivables.getNumber(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmReceivablesData crmReceivabelsData = new CrmReceivablesData();
                crmReceivabelsData.setId(receivablesData != null ? receivablesData.getId() : null);
                crmReceivabelsData.setFieldId(record.getLong("fieldId"));
                crmReceivabelsData.setName(record.getString("fieldName"));
                crmReceivabelsData.setValue(newValue);
                crmReceivabelsData.setCreateTime(LocalDateTimeUtil.now());
                crmReceivabelsData.setBatchId(batchId);
                crmReceivablesDataService.saveOrUpdate(crmReceivabelsData);

            }
            updateField(record, receivablesId);
        });
        this.lambdaUpdate().set(CrmReceivables::getUpdateTime, new Date()).eq(CrmReceivables::getReceivablesId, receivablesId).update();
    }

    @Override
    public BasePage<JSONObject> queryListByContractId(BasePage<JSONObject> page, Long contractId, CrmEnum crmEnum, CrmAuthEnum crmAuthEnum, Integer readOnly) {
        List<Long> userIds = AuthUtil.queryAuthUserList(crmEnum, crmAuthEnum);
        BasePage<JSONObject> jsonObjects = getBaseMapper().queryListByContractId(page, contractId, readOnly, userIds, UserUtil.getUserId(), crmAuthEnum.getValue());
        for (JSONObject jsonObject : jsonObjects.getList()) {
            Long ownerUserId = jsonObject.getLong("ownerUserId");
            String ownerUserName = UserCacheUtil.getUserName(ownerUserId);
            jsonObject.put("ownerUserName", ownerUserName);
        }
        return jsonObjects;
    }

    @Override
    public void updateFieldValue(String fieldName, Object value, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        updateField("receivablesPlanId", null, ids);
        updateField("planNum", null, ids);
    }

}
