package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.PageEntity;
import com.kakarote.core.feign.crm.entity.CrmActivityBO;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.constant.CrmActivityEnum;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmActivitySaveBO;
import com.kakarote.crm.entity.PO.CrmActivity;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * crm活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
public interface ICrmActivityService extends BaseService<CrmActivity> {

    /**
     * 删除活动记录
     *
     * @param ids 主键列表
     */
    public void deleteActivityRecord(List<Long> ids);

    /**
     * 新增活动记录
     *
     * @param type           type
     * @param activityEnum   类型
     * @param activityTypeId 类型ID
     */
    public void addActivity(Integer type, CrmActivityEnum activityEnum, Long activityTypeId);

    /**
     * 新增变更活动记录
     *
     * @param type           type
     * @param activityEnum   类型
     * @param activityTypeId 类型ID
     */
    public void addActivity(Integer type, CrmActivityEnum activityEnum, Long activityTypeId,String newStr ,String oldStr);

    /**
     * 生成关联业务活动记录
     * @param crmActivityBO
     */
    void addRelationActivity(CrmActivityBO crmActivityBO);

    /**
     * 查询活动记录列表
     *
     * @param crmActivityQueryBO 查询参数
     * @return data
     */
    public BasePage<CrmActivityVO> getCrmActivityPageList(CrmActivityQueryBO crmActivityQueryBO);

    /**
     * 添加活动记录
     *
     * @param crmActivityBO crmActivity
     */
    public void addCrmActivityRecord(CrmActivitySaveBO crmActivityBO);

    /**
     * 查询文件batchId
     *
     * @param id           id
     * @param activityType 类型
     * @return data
     */
    public List<String> queryFileBatchId(Long id, Integer activityType);


    public void buildActivityRelation(CrmActivityVO record);

    /**
     * 删除跟进记录
     *
     * @param activityId 活动ID
     */
    public void deleteCrmActivityRecord(Long activityId);

    /**
     * 修改跟进记录
     *
     * @param crmActivity bo
     * @return data
     */
    public void updateActivityRecord(CrmActivitySaveBO crmActivity);

    /**
     * 外勤签到
     */
    public void outworkSign(CrmActivitySaveBO crmActivity);

    /**
     * app外勤统计
     */
    public BasePage<JSONObject> queryOutworkStats(PageEntity entity, String startTime, String endTime);

    /**
     * app外勤详情
     */
    public BasePage<CrmActivity> queryOutworkList(PageEntity entity, String startTime, String endTime, Long userId);
    public BasePage<CrmActivityVO> queryOutworkList(CrmActivityQueryBO CrmActivityQueryBO);

    /**
     * app 查询签到照片上传设置
     */
    public Integer queryPictureSetting();

    /**
     * app 查询签到照片上传设置
     */
    public void setPictureSetting(Integer status);

    /**
     * 删除外勤签到
     */
    public void deleteOutworkSign(Long activityId);

    /**
     * 导出跟进记录
     */
    public List<Map<String,Object>> exportRecordList(CrmActivityQueryBO biParams);

    /**
     * 导入跟进记录
     */
    public JSONObject importRecordList(MultipartFile file, Integer crmType);

}
