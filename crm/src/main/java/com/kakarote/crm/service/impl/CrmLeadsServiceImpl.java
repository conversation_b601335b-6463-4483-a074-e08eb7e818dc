package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.feign.crm.entity.QueryEventCrmPageBO;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.*;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.common.ResourcesUtil;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmBackLogEnum;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmChangeOwnerUserBO;
import com.kakarote.crm.entity.BO.CrmModelSaveBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.BO.CrmUpdateInformationBO;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmLeadsMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 线索表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-21
 */
@Service
@Slf4j
public class CrmLeadsServiceImpl extends BaseServiceImpl<CrmLeadsMapper, CrmLeads> implements ICrmLeadsService, CrmPageService {


    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmLeadsDataService crmLeadsDataService;

    @Autowired
    private ICrmLeadsUserStarService crmLeadsUserStarService;

    @Autowired
    private ICrmBackLogDealService crmBackLogDealService;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ICrmCustomerSettingService crmCustomerSettingService;

    @Autowired
    private ICrmCustomerDataService crmCustomerDataService;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    private FieldService fieldService;

    @Resource
    private CrmPageService customerService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    private Redis redis;

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"leadsName", "telephone", "mobile"};
    }

    @Override
    public CrmEnum getLabel() {
        return CrmEnum.LEADS;
    }

    /**
     * 查询所有字段
     *
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("lastTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("lastContent", FieldEnum.TEXTAREA, 1));
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("ownerUserName", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("createUserName", FieldEnum.TEXT, 1));
        return filedList;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryById(id);
        List<CrmModelFiledVO> filedVOS = crmFieldService.queryField(crmModel);
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            filedVOS.addAll(modelFiledVOS);
        }
        return filedVOS;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryById(id);
        if (ObjectUtil.isNotEmpty(id)) {
            //去除编辑掩码
            crmModel.put("update", true);
        }
        return crmFieldService.queryFormPositionFieldVO(crmModel);
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        BasePage<Map<String, Object>> basePage = queryList(search, false);
        Long userId = UserUtil.getUserId();
        List<Long> starIds = crmLeadsUserStarService.starList(userId);
        basePage.getList().forEach(map -> {
            map.put("star", starIds.contains((Long) map.get("leadsId")) ? 1 : 0);
        });
        return basePage;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryById(id, UserUtil.getUserId());
            crmModel.setLabel(CrmEnum.LEADS.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            crmLeadsDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
        } else {
            crmModel = new CrmModel(CrmEnum.LEADS.getType());
        }
        return crmModel;
    }


    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.leads, action = CrmMsgActionEnum.save)
    public void addOrUpdate(CrmModelSaveBO crmModel, boolean isExcel) {
        setData(crmModel.getEntity());
        CrmLeads crmLeads = BeanUtil.copyProperties(crmModel.getEntity(), CrmLeads.class);
        String batchId = StrUtil.isNotEmpty(crmLeads.getBatchId()) ? crmLeads.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_leads_data"));
        crmLeadsDataService.saveData(crmModel.getField(), batchId);
        if (StrUtil.isEmpty(crmLeads.getEmail())) {
            crmLeads.setEmail(null);
        }
        //修改下次联系时间,待办事项需要提醒,需要同步最后跟进时间
        if (crmLeads.getNextTime() != null) {
            crmLeads.setLastTime(LocalDateTimeUtil.now());
        }
        // 是否新建
        Boolean isAdd = false;
        if (crmLeads.getLeadsId() != null) {
            crmLeads.setCustomerId(0L);
            crmLeads.setUpdateTime(LocalDateTimeUtil.now());
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(getById(crmLeads.getLeadsId())), BeanUtil.beanToMap(crmLeads), CrmEnum.LEADS, crmLeads.getLeadsName(), crmLeads.getLeadsId());
            updateById(crmLeads);
            //查询一次保存es,因为有些字段没有保存es会出现null
            crmLeads = getById(crmLeads.getLeadsId());
            crmBackLogDealService.deleteByType(crmLeads.getOwnerUserId(), CrmEnum.LEADS, CrmBackLogEnum.FOLLOW_LEADS, crmLeads.getLeadsId());
            redis.del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + crmLeads.getOwnerUserId());
        } else {
            isAdd = true;
            crmLeads.setCreateTime(LocalDateTimeUtil.now());
            crmLeads.setUpdateTime(LocalDateTimeUtil.now());
            crmLeads.setCreateUserId(UserUtil.getUserId());
            crmLeads.setIsTransform(0);
            if (!isExcel) {
                crmLeads.setFollowup(0);
            }
            crmLeads.setCreateUserId(UserUtil.getUserId());
            if (crmLeads.getOwnerUserId() == null) {
                crmLeads.setOwnerUserId(UserUtil.getUserId());
            }
            crmLeads.setLastTime(LocalDateTimeUtil.now());
            crmLeads.setBatchId(batchId);
            save(crmLeads);
            actionRecordUtil.addRecord(crmLeads.getLeadsId(), CrmEnum.LEADS, crmLeads.getLeadsName());
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmLeads));
        savePage(crmModel, crmLeads.getLeadsId(), isExcel);

        if (!isExcel && isAdd) {
            // 发送消息
            MsgBodyBO msgBody = new MsgBodyBO();
            msgBody.setMsgKey(IdUtil.simpleUUID());
            msgBody.setMsgTag(getMsgLabelEnum().name());
            msgBody.setAction(CrmMsgActionEnum.save.name());
            msgBody.setCurrentUser(UserUtil.getUser());
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmLeads.getLeadsId());
            operateObject.put("name", crmLeads.getLeadsName());
            operateObject.put("ownerUserId", crmLeads.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmLeads.getOwnerUserId()));
            String title = ResourcesUtil.getMessage("m2", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), "@NAME");
            msgBody.setTitle(title);
            msgBody.setOperateObject(Arrays.asList(operateObject));
            AdminMessageUtil.setMsgBody(msgBody);
        }
    }

    /**
     * 判断类型
     */
    private void setData(Map<String, Object> entity) {
        List<CrmField> fieldList = crmFieldService.lambdaQuery().eq(CrmField::getLabel, getLabel().getType())
                .eq(CrmField::getFieldType, 1).list();
        for (CrmField crmField : fieldList) {
            if (entity.get(crmField.getFieldName()) != null) {
                String value = fieldService.convertObjectValueToString(crmField.getType(),
                        entity.get(crmField.getFieldName()), entity.get(crmField.getFieldName()).toString());
                entity.put(crmField.getFieldName(), value);
            }
        }
    }

    @Override
    public void setOtherField(Map<String, Object> map) {
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
    }

    /**
     * 删除线索数据
     *
     * @param ids ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        LambdaQueryWrapper<CrmLeads> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CrmLeads::getBatchId);
        wrapper.in(CrmLeads::getLeadsId, ids);
        List<String> batchIdList = listObjs(wrapper, Object::toString);
        //删除跟进记录
        crmActivityService.deleteActivityRecord(ids);
        //删除字段操作记录
        crmActionRecordService.deleteActionRecord(CrmEnum.LEADS, ids);
        //删除自定义字段
        crmLeadsDataService.deleteByBatchId(batchIdList);
        //todo 删除文件,暂不处理
        removeByIds(ids);
        //删除es数据
        deletePage(ids);
    }

    /**
     * 修改线索负责人
     *
     * @param changeOwnerUserBO 负责人转移BO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.leads, action = CrmMsgActionEnum.transfer)
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO) {
        List<Long> leadsIds = changeOwnerUserBO.getIds();
        Long ownerUserId = changeOwnerUserBO.getOwnerUserId();
        LambdaUpdateWrapper<CrmLeads> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CrmLeads::getLeadsId, leadsIds);
        wrapper.set(CrmLeads::getOwnerUserId, ownerUserId);
        wrapper.set(CrmLeads::getFollowup, 0);
        wrapper.set(CrmLeads::getIsReceive, 1);
        // 操作对象数据
        List<JSONObject> operateObjects = new ArrayList<>();
        String ownerUserName = UserCacheUtil.getUserName(ownerUserId);
        for (Long leadsId : leadsIds) {
            CrmLeads crmLeads = getById(leadsId);
            BaseUtil.getRedis().del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + crmLeads.getOwnerUserId().toString());
            actionRecordUtil.addConversionRecord(leadsId, CrmEnum.LEADS, ownerUserId, crmLeads.getLeadsName());
            // 构建操作对象数据
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmLeads.getLeadsId());
            operateObject.put("name", crmLeads.getLeadsName());
            operateObject.put("ownerUserId", ownerUserId);
            operateObject.put("ownerUserName", ownerUserName);
            operateObjects.add(operateObject);
        }
        update(wrapper);
        BaseUtil.getRedis().del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + ownerUserId.toString());
        //修改es
        Map<String, Object> map = new HashMap<>();
        map.put("ownerUserId", ownerUserId);
        map.put("ownerUserName", ownerUserName);
        map.put("followup", 0);
        map.put("isReceive", 1);
        updateField(map, leadsIds);

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.transform.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m3", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(),
                "@NAME", ownerUserName);
        msgBody.setTitle(title);
        msgBody.setOperateObject(operateObjects);
        AdminMessageUtil.setMsgBody(msgBody);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.leads, action = CrmMsgActionEnum.transform)
    public void transfer(List<Long> leadsIds) {
        List<Long> customerIds = new ArrayList<>();
        Map<Long, CrmModelSaveBO> crmModelSaveBoMap = new HashMap<>(8);
//        BaseUtil.getRedis().del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + UserUtil.getUser().getUserId().toString());
        // 操作对象数据
        List<JSONObject> operateObjects = new ArrayList<>();
        for (Long leadsId : leadsIds) {
            boolean auth = AuthUtil.isCrmAuth(CrmEnum.LEADS, leadsId, CrmAuthEnum.EDIT);
            if (auth) {
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
            }
            CrmModel leadsMap = queryById(leadsId);
            CrmLeads crmLeads = BeanUtil.copyProperties(leadsMap, CrmLeads.class);
            if (crmLeads.getIsTransform() == 1) {
                throw new CrmException(CrmCodeEnum.CRM_LEADS_TRANSFER_ERROR);
            }
            BaseUtil.getRedis().del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + crmLeads.getOwnerUserId());
            CrmCustomer crmCustomer = new CrmCustomer();
            crmCustomer.setCustomerName(crmLeads.getLeadsName());
            crmCustomer.setNextTime(crmLeads.getNextTime());
            crmCustomer.setMobile(crmLeads.getMobile());
            crmCustomer.setTelephone(crmLeads.getTelephone());
            crmCustomer.setDealStatus(0);
            crmCustomer.setCreateUserId(UserUtil.getUserId());
            crmCustomer.setOwnerUserId(crmLeads.getOwnerUserId());
            crmCustomer.setCreateTime(LocalDateTimeUtil.now());
            crmCustomer.setUpdateTime(LocalDateTimeUtil.now());
            crmCustomer.setReceiveTime(LocalDateTimeUtil.now());

            crmCustomer.setLocation("");
            StringBuilder address = new StringBuilder();
            StringBuilder detailAddress = new StringBuilder();
            if (StrUtil.isNotEmpty(crmLeads.getAddress())) {
                JSONArray array = JSON.parseArray(crmLeads.getAddress());
                for (int i = 0; i < array.size(); i++) {
                    JSONObject json = array.getJSONObject(i);
                    if (json.containsKey("id")&&json.getInteger("id") <= 3) {
                        if (address.length() != 0) {
                            address.append(",");
                        }
                        address.append(json.get("name"));
                    } else {
                        detailAddress.append(json.get("name"));
                    }
                }
            }
            crmCustomer.setDetailAddress(detailAddress.toString());
            crmCustomer.setAddress(address.toString());
            crmCustomer.setLng("");
            crmCustomer.setLat("");
            crmCustomer.setRemark("");
            crmCustomer.setEmail(crmLeads.getEmail());
            crmCustomer.setStatus(1);
            crmCustomer.setLastContent(crmLeads.getLastContent());
            crmCustomer.setLastTime(crmLeads.getLastTime());
            String customerBatchId = IdUtil.simpleUUID();
            crmCustomer.setBatchId(customerBatchId);
            List<CrmField> leadsFields = crmFieldService.list(CrmEnum.LEADS.getType(), false);
            List<CrmField> customerFields = crmFieldService.list(CrmEnum.CUSTOMER.getType(), true);
            List<CrmCustomerData> customerDataList = new ArrayList<>();
            Map<String, Object> customerExtraMap = new HashMap<>();
            for (CrmField leadsField : leadsFields) {
                for (CrmField customerField : customerFields) {
                    Integer isUnique = customerField.getIsUnique();
                    boolean bol = ("客户来源".equals(customerField.getName()) && "线索来源".equals(leadsField.getName()))
                            || ("客户行业".equals(customerField.getName()) && "客户行业".equals(leadsField.getName()))
                            || ("客户级别".equals(customerField.getName()) && "客户级别".equals(leadsField.getName()));
                    if (bol) {
                        if (isUnique == 1 && crmFieldService.queryCustomerFieldDuplicateByNoFixed(customerField.getName(), leadsMap.get(leadsField.getName())) > 0) {
                            throw new CrmException(CrmCodeEnum.CRM_FIELD_EXISTED, customerField.getName());
                        }
                        CrmCustomerData crmCustomerData = new CrmCustomerData();
                        crmCustomerData.setValue((String) leadsMap.get(leadsField.getFieldName()));
                        crmCustomerData.setFieldId(customerField.getFieldId());
                        crmCustomerData.setName(customerField.getName());
                        crmCustomerData.setFieldName(customerField.getFieldName());
                        customerDataList.add(crmCustomerData);
                        continue;
                    }
                    if (leadsField.getRelevant() != null && customerField.getFieldId().equals(leadsField.getRelevant())) {
                        if (customerField.getFieldType().equals(1)) {
                            customerExtraMap.put(customerField.getFieldName(), leadsMap.get(StrUtil.toCamelCase(leadsField.getFieldName())));
                        } else {
                            CrmCustomerData crmCustomerData = new CrmCustomerData();
                            crmCustomerData.setValue(TypeUtils.castToString(leadsMap.get(StrUtil.toCamelCase(leadsField.getFieldName()))));
                            crmCustomerData.setFieldId(customerField.getFieldId());
                            crmCustomerData.setName(customerField.getName());
                            crmCustomerData.setFieldName(StrUtil.toCamelCase(customerField.getFieldName()));
                            customerDataList.add(crmCustomerData);
                        }
                    }

                }
            }
            BeanUtil.fillBeanWithMap(customerExtraMap, crmCustomer, true);
            crmCustomer.setBatchId(customerBatchId);
            for (CrmField customerField : customerFields) {
                Integer isUnique = customerField.getIsUnique();
                String name = customerField.getName();
                Map<String, Object> customerMap = BeanUtil.beanToMap(crmCustomer);
                for (String key : customerMap.keySet()) {
                    if (key.equals(StrUtil.toCamelCase(customerField.getFieldName()))) {
                        Object value = customerMap.get(key);
                        if (value != null && !"".equals(value.toString())) {
                            if (isUnique == 1 && crmFieldService.queryCustomerFieldDuplicateByFixed(customerField.getFieldName(), value) > 0) {
                                throw new CrmException(CrmCodeEnum.CRM_FIELD_EXISTED, name);
                            }
                        }
                    }
                }
            }
            if (!crmCustomerSettingService.queryCustomerSettingNum(1, crmCustomer.getOwnerUserId())) {
                throw new CrmException(CrmCodeEnum.THE_NUMBER_OF_CUSTOMERS_HAS_REACHED_THE_LIMIT);
            }
            crmCustomerService.save(crmCustomer);
            Long customerId = crmCustomer.getCustomerId();
            customerIds.add(customerId);
            //保存自定义字段
            saveCustomerField(customerDataList, customerBatchId);
            CrmModelSaveBO crmModelSaveBO = new CrmModelSaveBO();
            crmModelSaveBO.setEntity(BeanUtil.beanToMap(crmCustomer));
            List<CrmModelFiledVO> collect = customerDataList.stream().map(field -> BeanUtil.copyProperties(field, CrmModelFiledVO.class)).collect(Collectors.toList());
            crmModelSaveBO.setField(collect);
            crmModelSaveBoMap.put(customerId, crmModelSaveBO);

            actionRecordUtil.addConversionCustomerRecord(crmCustomer.getCustomerId(), CrmEnum.CUSTOMER, crmCustomer.getCustomerName());
            lambdaUpdate().set(CrmLeads::getIsTransform, 1).set(CrmLeads::getUpdateTime, new Date()).set(CrmLeads::getCustomerId, crmCustomer.getCustomerId())
                    .eq(CrmLeads::getLeadsId, leadsId).update();

            // 构建操作对象数据
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmLeads.getLeadsId());
            operateObject.put("name", crmLeads.getLeadsName());
            operateObject.put("ownerUserId", crmLeads.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmLeads.getOwnerUserId()));
            operateObjects.add(operateObject);

            //转移操作记录
            List<CrmActionRecord> crmActionRecordList = crmActionRecordService.lambdaQuery().eq(CrmActionRecord::getActionId, leadsId).eq(CrmActionRecord::getTypes, 1).list();
            crmActionRecordList.forEach(crmActionRecord -> {
                crmActionRecord.setId(null);
                crmActionRecord.setTypes(CrmEnum.CUSTOMER.getType());
                crmActionRecord.setActionId(crmCustomer.getCustomerId());
            });
            crmActionRecordService.saveBatch(crmActionRecordList, 500);

            //转移活动数据
            List<CrmActivity> crmActivityList = crmActivityService.lambdaQuery().eq(CrmActivity::getActivityType, 1).eq(CrmActivity::getType, 1).eq(CrmActivity::getActivityTypeId, leadsId).list();
            List<Long> adminFileIdList = new ArrayList<>();
            if (crmActivityList.size() != 0) {
                crmActivityList.forEach(crmActivity -> {
                    List<FileEntity> leadsRecordFiles = adminFileService.queryFileList(crmActivity.getBatchId()).getData();
                    String customerRecordBatchId = IdUtil.simpleUUID();
                    List<Long> fileIds = leadsRecordFiles.stream().map(FileEntity::getFileId).collect(Collectors.toList());
                    crmActivity.setBatchId(customerRecordBatchId);
                    crmActivity.setId(null);
                    crmActivity.setActivityType(CrmEnum.CUSTOMER.getType());
                    crmActivity.setActivityTypeId(crmCustomer.getCustomerId());
                    crmActivity.setActivityId(crmCustomer.getCustomerId());
                    adminFileService.saveBatchFileEntity(fileIds, customerRecordBatchId);
                });
                crmActivityService.saveBatch(crmActivityList, 100);
            }
            List<FileEntity> fileList = adminFileService.queryFileList(crmLeads.getBatchId()).getData();
            if (fileList.size() != 0) {
                fileList.forEach(adminFile -> {
                    adminFileIdList.add(adminFile.getFileId());
                });
            }
            adminFileService.saveBatchFileEntity(adminFileIdList, customerBatchId);
        }
        for (int i = 0; i < leadsIds.size(); i++) {
            Long leadsId = leadsIds.get(i);
            Long customerId = customerIds.get(i);

            Map<String, Object> map = new HashMap<>();
            map.put("isTransform", 1);
            map.put("updateTime", DateUtil.formatDateTime(new Date()));
            map.put("customerId", customerId);

            UpdateQuery updateQuery = UpdateQuery.builder(leadsId.toString())
                    .withDocument(Document.from(map))
                    .build();

            try {
                elasticsearchRestTemplate.update(updateQuery, IndexCoordinates.of(CrmEnum.LEADS.getIndex()));
            } catch (Exception e) {
                log.error("es 更新异常!", e);
            }

            customerService.savePage(crmModelSaveBoMap.get(customerId), customerId, false);
        }

        elasticsearchRestTemplate.indexOps(IndexCoordinates.of(getIndex())).refresh();

        // 发送MQ
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.transform.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        msgBody.setOperateObject(operateObjects);
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    private void saveCustomerField(List<CrmCustomerData> customerDataList, String batchId) {
        if (CollUtil.isEmpty(customerDataList) || StrUtil.isEmpty(batchId)) {
            return;
        }
        crmCustomerDataService.lambdaUpdate().eq(CrmCustomerData::getBatchId, batchId)
                .remove();
        customerDataList.forEach(fieldv -> {
            fieldv.setId(null);
            fieldv.setName(fieldv.getFieldName());
            fieldv.setCreateTime(LocalDateTimeUtil.now());
            fieldv.setBatchId(batchId);
            crmCustomerDataService.save(fieldv);
        });
    }


    /**
     * 下载导入模板
     *
     * @param response 线索id
     */
    @Override
    public void downloadExcel(HttpServletResponse response) throws IOException {
        List<CrmModelFiledVO> crmModelFiledList = queryField(null);
        int k = 0;
        for (int i = 0; i < crmModelFiledList.size(); i++) {
            if ("leadsName".equals(crmModelFiledList.get(i).getFieldName())) {
                k = i;
                break;
            }
        }
        crmModelFiledList.add(k + 1, new CrmModelFiledVO("ownerUserId", FieldEnum.TEXT, "负责人", 1).setIsNull(1));
        ExcelParseUtil.importExcel(new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "线索";
            }
        }, crmModelFiledList, response, "crm");
    }

    /**
     * 导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.leads, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        exportExcel(search, headList, response, isXls, null);

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 标星
     *
     * @param leads 线索id
     */
    @Override
    public void star(Long leads) {
        LambdaQueryWrapper<CrmLeadsUserStar> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmLeadsUserStar::getLeadsId, leads);
        wrapper.eq(CrmLeadsUserStar::getUserId, UserUtil.getUserId());
        CrmLeadsUserStar star = crmLeadsUserStarService.getOne(wrapper);
        if (star == null) {
            star = new CrmLeadsUserStar();
            star.setLeadsId(leads);
            star.setUserId(UserUtil.getUserId());
            crmLeadsUserStarService.save(star);
        } else {
            crmLeadsUserStarService.removeById(star.getId());
        }
    }

    @Override
    public List<CrmModelFiledVO> information(Long leadsId) {
        return queryField(leadsId, true);
    }

    /**
     * 查询文件数量
     *
     * @param leadsId id
     * @return data
     */
    @Override
    public CrmInfoNumVO num(Long leadsId) {
        List<String> batchIdList = new ArrayList<>();
        CrmLeads crmLeads = getById(leadsId);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmLeadsData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmLeadsData::getValue);
            wrapper.eq(CrmLeadsData::getBatchId, crmLeads.getBatchId());
            wrapper.in(CrmLeadsData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            batchIdList.addAll(crmLeadsDataService.listObjs(wrapper, Object::toString));
        }
        batchIdList.add(crmLeads.getBatchId());
        batchIdList.addAll(crmActivityService.queryFileBatchId(crmLeads.getLeadsId(), getLabel().getType()));
        CrmInfoNumVO numVO = new CrmInfoNumVO();
        numVO.setFileCount(fileService.queryNum(batchIdList).getData());
        return numVO;
    }


    /**
     * 查询文件列表
     *
     * @param leadsId id
     * @return file
     */
    @Override
    public List<FileEntity> queryFileList(Long leadsId) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmLeads crmLeads = getById(leadsId);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmLeads.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmLeadsData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmLeadsData::getValue);
            wrapper.eq(CrmLeadsData::getBatchId, crmLeads.getBatchId());
            wrapper.in(CrmLeadsData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = fileService.queryFileList(crmLeadsDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("线索详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        List<String> stringList = crmActivityService.queryFileBatchId(crmLeads.getLeadsId(), getLabel().getType());
        if (stringList.size() > 0) {
            List<FileEntity> data = fileService.queryFileList(stringList).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("跟进记录");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        return fileEntityList;
    }

    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long leadsId = updateInformationBO.getId();
        int two = 2;
        updateInformationBO.getList().forEach(record -> {
            CrmLeads oldLeads = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldLeadsMap = BeanUtil.beanToMap(oldLeads);
            String fieldType = "fieldType";
            if (record.getInteger(fieldType) == 1) {
                Map<String, Object> crmLeadsMap = new HashMap<>(oldLeadsMap);
                int status = 43;
                String type = "type";
                String address = "value";
                if (Arrays.asList(status).contains(record.getInteger(type)) && ObjectUtil.isNotEmpty(record.get(address))) { //线索地址
                    String value = JSONObject.toJSONString(record.get("value"));
                    record.put("value", value);
                }
                crmLeadsMap.put(record.getString("fieldName"), record.get("value"));
                CrmLeads crmLeads = BeanUtil.toBeanIgnoreCase(crmLeadsMap, CrmLeads.class, true);
                actionRecordUtil.updateRecord(oldLeadsMap, crmLeadsMap, CrmEnum.LEADS, crmLeads.getLeadsName(), crmLeads.getLeadsId());
                int four = 4;
                int entityType = 13;
                if (Arrays.asList(four, entityType).contains(record.getInteger(type))) {   //线索日期处理空字符串
                    record.put("value", null);
                }
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("leads_id", updateInformationBO.getId()).update();
            } else if (record.getInteger(fieldType) == 0 || record.getInteger(fieldType) == two) {
                CrmLeadsData leadsData = crmLeadsDataService.lambdaQuery().select(CrmLeadsData::getValue, CrmLeadsData::getId).eq(CrmLeadsData::getFieldId, record.getLong("fieldId"))
                        .eq(CrmLeadsData::getBatchId, batchId).one();
                String value = leadsData != null ? leadsData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.LEADS, BehaviorEnum.UPDATE, leadsId, oldLeads.getLeadsName(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmLeadsData crmLeadsData = new CrmLeadsData();
                crmLeadsData.setId(leadsData != null ? leadsData.getId() : null);
                crmLeadsData.setFieldId(record.getLong("fieldId"));
                crmLeadsData.setName(record.getString("fieldName"));
                crmLeadsData.setValue(newValue);
                crmLeadsData.setCreateTime(LocalDateTimeUtil.now());
                crmLeadsData.setBatchId(batchId);
                crmLeadsDataService.saveOrUpdate(crmLeadsData);

            }
            updateField(record, leadsId);
        });
        this.lambdaUpdate().set(CrmLeads::getUpdateTime, new Date()).eq(CrmLeads::getLeadsId, leadsId).update();
    }

    @Override
    public List<String> eventLeads(CrmEventBO crmEventBO) {
        return getBaseMapper().eventLeads(crmEventBO);
    }

    @Override
    public BasePage<Map<String, Object>> eventLeadsPageList(QueryEventCrmPageBO eventCrmPageBO) {
        Long userId = eventCrmPageBO.getUserId();
        Long time = eventCrmPageBO.getTime();
        if (userId == null) {
            userId = UserUtil.getUserId();
        }
        List<Long> leadsIds = getBaseMapper().eventLeadsList(userId, new Date(time));
        if (leadsIds.size() == 0) {
            return new BasePage<>();
        }
        List<String> collect = leadsIds.stream().map(Object::toString).collect(Collectors.toList());
        CrmSearchBO crmSearchBO = new CrmSearchBO();
        crmSearchBO.setSearchList(Collections.singletonList(new CrmSearchBO.Search("_id", "text", CrmSearchBO.FieldSearchEnum.ID, collect)));
        crmSearchBO.setLabel(CrmEnum.LEADS.getType());
        crmSearchBO.setPage(eventCrmPageBO.getPage());
        crmSearchBO.setLimit(eventCrmPageBO.getLimit());
        return queryPageList(crmSearchBO);
    }

}
