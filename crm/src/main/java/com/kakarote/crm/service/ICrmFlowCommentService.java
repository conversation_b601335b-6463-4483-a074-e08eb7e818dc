package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmFlowComment;

import java.util.List;

/**
 * <p>
 * 流程阶段评论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
public interface ICrmFlowCommentService extends BaseService<CrmFlowComment> {
    /**
     * 查询评论列表
     * @param settingId settingId
     * @return data
     */
    public List<CrmFlowComment> queryCommentList(Long settingId);

    /**
     * 新增评论
     * @param taskComment taskComment
     */
    public void setComment(CrmFlowComment taskComment);
}
