package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.SimpleDept;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.TagUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.entity.BO.MarketingFieldBO;
import com.kakarote.crm.entity.PO.CrmMarketingField;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmMarketingFieldMapper;
import com.kakarote.crm.service.ICrmMarketingFieldExtendService;
import com.kakarote.crm.service.ICrmMarketingFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
@Service
public class CrmMarketingFieldServiceImpl extends BaseServiceImpl<CrmMarketingFieldMapper, CrmMarketingField> implements ICrmMarketingFieldService {

    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    private FieldService fieldService;

    @Autowired
    private ICrmMarketingFieldExtendService crmMarketingFieldExtendService;


    @Override
    public List<CrmMarketingField> queryField(Long id) {
        List<CrmMarketingField> list = lambdaQuery().eq(CrmMarketingField::getFormId, id).orderByAsc(CrmMarketingField::getSorting).list();
        recordToFormType(list);
        return list;
    }

    @Override
    public void recordToFormType(List<CrmMarketingField> list) {
        for (CrmMarketingField record : list) {
            FieldEnum typeEnum = FieldEnum.parse(record.getType());
            record.setFormType(typeEnum.getFormType());
            switch (typeEnum) {
                case ATTENTION:
                    if(record.getDefaultValue() == null){
                        record.setDefaultValue(0);
                    } else {
                        record.setDefaultValue(TypeUtils.castToInt(record.getDefaultValue()));
                    }
                    if(record.getValue() == null){
                        record.setValue(0);
                    } else {
                        record.setValue(TypeUtils.castToInt(record.getValue()));
                    }
                case SERIAL_NUMBER:
                    record.setSetting(parseArray(record.getOptions(),typeEnum));
                    break;
                case TAG:
                case CHECKBOX:
                    record.setDefaultValue(parseArray1(record.getDefaultValue(),typeEnum));
                    record.setValue(parseArray(record.getValue(),typeEnum));
                case SELECT:
                    if(Objects.equals(record.getRemark(),FieldEnum.OPTIONS_TYPE.getFormType())) {
                        if (CollUtil.isEmpty(record.getOptionsData())) {
                            JSONObject optionsData = JSON.parseObject(record.getOptions());
                            record.setOptionsData(optionsData);
                            record.setSetting(new ArrayList<>(optionsData.keySet()));
                        }
                    }else {
                        if (CollUtil.isEmpty(record.getSetting())) {
                            record.setSetting(parseArray(record.getOptions(),typeEnum));
                        }
                    }
                    break;
                case USER:
                case STRUCTURE:
                    record.setDefaultValue(new ArrayList<>(0));
                    break;
                case DETAIL_TABLE:
                    if (CollUtil.isEmpty(record.getFieldExtendList())) {
                        record.setFieldExtendList(crmMarketingFieldExtendService.queryCrmFieldExtend(record.getFieldId()));
                    }
                    break;
                default:
                    record.setSetting(new ArrayList<>());
                    break;
            }
        }
    }

    /**
     * 格式化setting
     * @param content content
     * @return array
     */
    private List<Object> parseArray(Object content,FieldEnum fieldEnum) {
        if(content == null) {
            return Collections.emptyList();
        }
        if(content instanceof String) {
            try {
                if(fieldEnum == FieldEnum.SELECT || fieldEnum == FieldEnum.CHECKBOX) {
                    return new ArrayList<>(StrUtil.splitTrim((String) content,Const.SEPARATOR));
                }
                return JSON.parseArray((String) content);
            } catch (JSONException exception) {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    private List<Object> parseArray1(Object content,FieldEnum fieldEnum) {
        if(content == null) {
            return Collections.emptyList();
        }
        if(content instanceof String) {
            try {
                if(fieldEnum == FieldEnum.SELECT) {
                    return new ArrayList<>(StrUtil.splitTrim((String) content,Const.SEPARATOR));
                }
                return JSON.parseArray((String) content);
            } catch (JSONException exception) {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }


    @Override
    public void transferFieldList(List<CrmMarketingField> recordList, Integer isDetail) {
        recordList.forEach(record -> {
            Integer dataType = record.getType();
            int two=2;
            if (isDetail == two) {
                if (FieldEnum.USER.getType().equals(dataType)) {
                    if (ObjectUtil.isNotEmpty(record.getValue())) {
                        List<SimpleUser> listResult = UserCacheUtil.getSimpleUsers(TagUtil.toLongSet((String) record.getValue()));
                        record.setValue(listResult);
                    }
                } else if (FieldEnum.STRUCTURE.getType().equals(dataType)) {
                    if (ObjectUtil.isNotEmpty(record.getValue())) {
                        Result<List<SimpleDept>> listResult = adminService.queryDeptByIds(TagUtil.toLongSet((String) record.getValue()));
                        record.setValue(listResult.getData());
                    }
                }else if (FieldEnum.CHECKBOX.getType().equals(dataType)) {
                    if (ObjectUtil.isNotEmpty(record.getValue())) {
                        List<String> split = StrUtil.split((String) record.getValue(), ",");
                        record.setValue(split);
                    }
                }
            } else {
                if (FieldEnum.USER.getType().equals(dataType)) {
                    if (ObjectUtil.isNotEmpty(record.getValue())) {
                        List<SimpleUser> listResult = UserCacheUtil.getSimpleUsers(TagUtil.toLongSet((String) record.getValue()));
                        String value = listResult.stream().map(SimpleUser::getRealname).collect(Collectors.joining(","));
                        record.setValue(value);
                    }
                } else if (FieldEnum.STRUCTURE.getType().equals(dataType)) {
                    if (ObjectUtil.isNotEmpty(record.getValue())) {
                        Result<List<SimpleDept>> listResult = adminService.queryDeptByIds(TagUtil.toLongSet((String) record.getValue()));
                        String value = listResult.getData().stream().map(SimpleDept::getName).collect(Collectors.joining(","));
                        record.setValue(value);
                    }
                }
            }
            if (dataType.equals(FieldEnum.FILE.getType())) {
                if (ObjectUtil.isNotEmpty(record.getValue())) {
                    Result<List<FileEntity>> fileList = adminFileService.queryFileList((String) record.getValue());
                    record.setValue(fileList.getData());
                }
            }
        });
    }

    @Override
    public void saveField(MarketingFieldBO marketingFieldBO) {
        List<CrmMarketingField> data = marketingFieldBO.getData();
        long nameCount = data.stream().map(CrmMarketingField::getName).distinct().count();
        if (data.size() != nameCount) {
            throw new CrmException(CrmCodeEnum.THE_FIELD_NAME_OF_THE_FORM_CANNOT_BE_REPEATED);
        }

        Long formId = marketingFieldBO.getFormId();
        List<Long> arr = data.stream().map(CrmMarketingField::getFieldId).filter(Objects::nonNull).collect(Collectors.toList());
        if (arr.size() > 0) {
            getBaseMapper().deleteByChooseId(arr, formId);
        }else if(data.size() == 0){
            lambdaUpdate().eq(CrmMarketingField::getFormId,formId).remove();
        }
        for (int i = 0; i < data.size(); i++) {
            CrmMarketingField entity = data.get(i);
            entity.setUpdateTime(LocalDateTimeUtil.now());
            if (entity.getFieldType() == null || entity.getFieldType() == 0) {
                entity.setFieldName(entity.getName());
            }
            entity.setFormId(formId);
            entity.setSorting(i);
            if (entity.getFieldId() != null) {
                if (!(entity.getDefaultValue() instanceof String) && entity.getDefaultValue() != null) {
                    if (Objects.equals(entity.getType(),FieldEnum.DATE_INTERVAL.getType()) ||
                            Objects.equals(entity.getType(),FieldEnum.CHECKBOX.getType())){
                        entity.setDefaultValue(JSON.toJSONString(entity.getDefaultValue()));
                    }else {
                        entity.setDefaultValue(entity.getDefaultValue().toString());
                    }
                }
                if (FieldEnum.DETAIL_TABLE.getType().equals(entity.getType())){
                    crmMarketingFieldExtendService.saveOrUpdateCrmFieldExtend(entity.getFieldExtendList(),entity.getFieldId(),true);
                }
                updateById(entity);
            } else {
                if (!(entity.getDefaultValue() instanceof String)&& entity.getDefaultValue() != null) {
                    if (Objects.equals(entity.getType(),FieldEnum.DATE_INTERVAL.getType()) ||
                            Objects.equals(entity.getType(),FieldEnum.CHECKBOX.getType())){
                        entity.setDefaultValue(JSON.toJSONString(entity.getDefaultValue()));
                    }else {
                        entity.setDefaultValue(entity.getDefaultValue().toString());
                    }
                }
                save(entity);
                if (FieldEnum.DETAIL_TABLE.getType().equals(entity.getType())){
                    crmMarketingFieldExtendService.saveOrUpdateCrmFieldExtend(entity.getFieldExtendList(),entity.getFieldId(),false);
                }
            }
        }
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        List<CrmMarketingField> list = lambdaQuery().eq(CrmMarketingField::getFormId, id).orderByAsc(CrmMarketingField::getSorting).list();
        //recordToFormType(list);
        List<CrmModelFiledVO> fieldList = list.stream().map(field -> {
            recordToFormType2(field,FieldEnum.parse(field.getType()));
            return BeanUtil.copyProperties(field, CrmModelFiledVO.class);
        }).collect(Collectors.toList());
        return fieldService.convertFormPositionFieldList(fieldList, CrmModelFiledVO::getXAxis,CrmModelFiledVO::getYAxis,CrmModelFiledVO::getSorting);
    }

    private void recordToFormType2(CrmMarketingField record, FieldEnum typeEnum) {
        record.setFormType(typeEnum.getFormType());
        switch (typeEnum) {
            case ATTENTION:
                if(record.getDefaultValue() == null){
                    record.setDefaultValue(0);
                } else {
                    record.setDefaultValue(TypeUtils.castToInt(record.getDefaultValue()));
                }
                if(record.getValue() == null){
                    record.setValue(0);
                } else {
                    record.setValue(TypeUtils.castToInt(record.getValue()));
                }
            case SERIAL_NUMBER:
                record.setSetting(parseArray(record.getOptions(),typeEnum));
                break;
            case TAG:
            case CHECKBOX:
                record.setDefaultValue(parseArray1(record.getDefaultValue(),typeEnum));
                record.setValue(parseArray(record.getValue(),typeEnum));
            case SELECT:
                if(Objects.equals(record.getRemark(),FieldEnum.OPTIONS_TYPE.getFormType())) {
                    if (CollUtil.isEmpty(record.getOptionsData())) {
                        JSONObject optionsData = JSON.parseObject(record.getOptions());
                        record.setOptionsData(optionsData);
                        record.setSetting(new ArrayList<>(optionsData.keySet()));
                    }
                }else {
                    if (CollUtil.isEmpty(record.getSetting())) {
                        record.setSetting(parseArray(record.getOptions(),typeEnum));
                    }
                }
                break;
            case DATE_INTERVAL:
                String dataValueStr = Optional.ofNullable(record.getDefaultValue()).orElse("").toString();
                if(StrUtil.isNotEmpty(dataValueStr)) {
                    record.setDefaultValue(parseArray(dataValueStr, typeEnum));
                }else {
                    record.setDefaultValue(new ArrayList<>());
                }
                break;
            case USER:
            case STRUCTURE:
                record.setDefaultValue(new ArrayList<>(0));
                break;
            case AREA:
            case AREA_POSITION:
            case CURRENT_POSITION:
                String defaultValue = Optional.ofNullable(record.getDefaultValue()).orElse("").toString();
                record.setDefaultValue(JSON.parse(defaultValue));
                break;
            case DETAIL_TABLE:
                record.setFieldExtendList(crmMarketingFieldExtendService.queryCrmFieldExtend(record.getFieldId()));
                break;
            default:
                record.setSetting(new ArrayList<>());
                break;
        }
    }
}
