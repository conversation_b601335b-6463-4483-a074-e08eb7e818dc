package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmFlowData;

/**
 * <p>
 * 阶段数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface ICrmFlowDataService extends BaseService<CrmFlowData> {

    /**
     * 根据ID查询数据
     *
     * @param id id
     * @return data
     */
    public CrmFlowData queryFlowDataInfo(Long id);

    /**
     * 保存阶段流程流转信息
     *
     * @param flowData 保存数据
     */
    public void saveFlowData(CrmFlowData flowData);

    /**
     * 修改审批状态
     * @param dataId 数据ID
     * @param status 状态
     */
    public void updateExamineStatus(Long dataId, Integer status);

    /**
     * 修改流程阶段
     *
     * @param dataId 数据ID
     * @param finalStatus 最终状态
     */
    public void updateFlowDataStatus(Long dataId, Integer finalStatus,String remark);
}
