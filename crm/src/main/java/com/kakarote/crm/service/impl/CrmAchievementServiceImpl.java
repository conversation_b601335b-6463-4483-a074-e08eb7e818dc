package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.ExcelParseUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.entity.BO.AchievementBO;
import com.kakarote.crm.entity.PO.CrmAchievement;
import com.kakarote.crm.mapper.CrmAchievementMapper;
import com.kakarote.crm.service.ICrmAchievementService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 * 业绩目标 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@Service
public class CrmAchievementServiceImpl extends BaseServiceImpl<CrmAchievementMapper, CrmAchievement> implements ICrmAchievementService {

    private static final int MAX_AVAILABLE_DIGITS = 16;

    @Autowired
    @Lazy
    private AdminService adminService;

    /**
     * 查询业绩目标
     *
     * @param achievement bo
     * @return data
     */
    @Override
    public List<CrmAchievement> queryAchievementList(AchievementBO achievement) {
        if (achievement.getType() == null) {
            achievement.setType(2);
        }
        LambdaQueryWrapper<CrmAchievement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmAchievement::getYear, achievement.getYear());
        wrapper.eq(CrmAchievement::getType, achievement.getType());
        wrapper.eq(CrmAchievement::getStatus, achievement.getStatus());
        int three=3;
        if (achievement.getType() == three) {
            if (achievement.getUserId() == null) {
                if (null != achievement.getDeptId()) {
                    List<Long> data = adminService.queryChildDeptId(achievement.getDeptId()).getData();
                    data.add(achievement.getDeptId());
                    List<Long> ids = adminService.queryUserByDeptIds(data).getData();
                    if (CollUtil.isEmpty(ids)) {
                        return new ArrayList<>();
                    }
                    wrapper.in(CrmAchievement::getObjId, ids);
                }
            } else {
                wrapper.eq(CrmAchievement::getObjId, achievement.getUserId());
            }
        } else {
            if (null != achievement.getDeptId()){
                List<Long> data = adminService.queryChildDeptId(achievement.getDeptId()).getData();
                data.add(achievement.getDeptId());
                wrapper.in(CrmAchievement::getObjId, data);
            }
        }
        List<CrmAchievement> list = list(wrapper);
        list.forEach(crmAchievement -> {
            if (achievement.getType() == three) {
                crmAchievement.setObjName(UserCacheUtil.getUserName(crmAchievement.getObjId().longValue()));
            } else {
                crmAchievement.setObjName(UserCacheUtil.getDeptName(crmAchievement.getObjId()));
            }
        });
        return list;
    }

    /**
     * 保存业绩目标
     *
     * @param achievement achievement
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAchievement(CrmAchievement achievement) {
        if (achievement.getObjIds().size() == 0) {
            return;
        }
        List<CrmAchievement> crmAchievements = new ArrayList<>();
        LambdaQueryWrapper<CrmAchievement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmAchievement::getType, achievement.getType());
        wrapper.eq(CrmAchievement::getYear, achievement.getYear());
        wrapper.eq(CrmAchievement::getStatus, achievement.getStatus());
        wrapper.in(CrmAchievement::getObjId, achievement.getObjIds());
        remove(wrapper);
        achievement.getObjIds().forEach(obj -> {
            CrmAchievement crmAchievement = BeanUtil.copyProperties(achievement, CrmAchievement.class);
            this.verifyCrmAchievementData(crmAchievement);
            crmAchievement.setAchievementId(null);
            crmAchievement.setObjIds(null);
            crmAchievement.setObjId(obj);
            crmAchievements.add(crmAchievement);
        });
        saveBatch(crmAchievements);
    }


    @Override
    public void verifyCrmAchievementData(List<CrmAchievement> crmAchievements) {
        if (CollUtil.isNotEmpty(crmAchievements)) {
            crmAchievements.forEach(this::verifyCrmAchievementData);
        }
    }

    @Override
    public void downloadExcel(HttpServletResponse response, Integer type) throws IOException {
        List<JSONObject> list = queryField(type);
        ExcelParseUtil.importExcel(new ExcelParseUtil.ExcelParseService() {
            @Override
            public int addCell(ExcelWriter writer, Integer x, Integer y, String fieldName) {
                if (writer == null) {
                    return 0;
                }
                String status="status";
                if (status.equals(fieldName)) {
                    Workbook wb = writer.getWorkbook();
                    Sheet sheet = writer.getSheet();
                    int two=2;
                    for (int i = 0; i < two; i++) {
                        writer.setColumnWidth(x + i, 20);
                    }
                    Cell cell1 = writer.getOrCreateCell(x, y);
                    cell1.setCellValue("类型");
                    Sheet hideSheet = wb.createSheet(fieldName);
                    wb.setSheetHidden(wb.getSheetIndex(hideSheet), true);
                    int rowId = 0;
                    // 设置第一行，科目类型的信息
                    Row provinceRow = hideSheet.createRow(rowId++);
                    provinceRow.createCell(0).setCellValue("类型");
                    int line = 2;
                    List<String> provinces = new ArrayList<>();
                    Cell provinceCell = provinceRow.createCell(line);
                    provinceCell.setCellValue("合同");
                    provinceCell.setCellValue("回款");
                    provinces.add("合同");
                    provinces.add("回款");
                    String[] provinceList = provinces.toArray(new String[]{});
                    CellRangeAddressList provRangeAddressList = new CellRangeAddressList(2, 10004, x, x);
                    DataValidationHelper validationHelper = sheet.getDataValidationHelper();
                    DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(provinceList);
                    //设置下拉框数据
                    DataValidation dataValidation = validationHelper.createValidation(constraint, provRangeAddressList);
                    dataValidation.createErrorBox("error", "请选择正确的类型");
                    sheet.addValidationData(dataValidation);
                }
                return 0;
            }

            @Override
            public String getExcelName() {
                if (type == 1) {
                    return "部门业绩目标";
                } else {
                    return "员工业绩目标";
                }
            }
        }, list, response, "achievement");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject excelImport(MultipartFile file, Integer type) {
        List<List<Object>> errList = new ArrayList<>();
        String filePath = getFilePath(file);
        List<JSONObject> list = queryField(type);
        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            map.put(list.get(i).getString("name"),i);
        }
        AtomicReference<Integer> num = new AtomicReference<>(0);
        ExcelUtil.readBySax(filePath, 0, (int sheetIndex, long rowIndex, List<Object> rowList) -> {
            if (rowIndex > 1) {
                num.getAndSet(num.get() + 1);
                JSONObject json = new JSONObject();
                for (JSONObject jsonObject : list) {
                    String name = jsonObject.getString("name");
                    Integer index = map.get(name);
                    if (index == null) {
                        rowList.add(0, "模板错误，请核对后再导入！");
                        errList.add(rowList);
                        return;
                    }
                    Object object = rowList.get(index);
                    if (jsonObject.getInteger("isNull") == 1 && StrUtil.isEmpty(object.toString())) {
                        rowList.add(0, name + "不能为空");
                        errList.add(rowList);
                        return;
                    }
                    if (ObjectUtil.equal(jsonObject.get("type"), FieldEnum.NUMBER.getType()) ||
                            ObjectUtil.equal(jsonObject.get("type"), FieldEnum.FLOATNUMBER.getType())) {
                        String reg = "\\d+(\\.\\d+)?";
                        if (!object.toString().matches(reg)) {
                            rowList.add(0, name + "只能为数字！");
                            errList.add(rowList);
                            return;
                        }
                    }
                    json.put(jsonObject.getString("fieldName"), rowList.get(index));
                }
                json.put("status", Objects.equals("回款", json.get("status")) ? 2 : 1);

                CrmAchievement achievement = json.toJavaObject(CrmAchievement.class);
                if (type == 1) {
                    Long deptId = adminService.getIdByDeptName(json.getString("deptName")).getData();
                    if (deptId == null) {
                        rowList.add(0, "未存在该部门名称，请核对后再导入");
                        errList.add(rowList);
                        return;
                    }
                    achievement.setType(2);
                    achievement.setObjId(deptId);
                } else {
                    Long userId = adminService.getIdByUserName(json.getString("userName")).getData();
                    if (userId == null || userId == 0L) {
                        rowList.add(0, "未存在该员工名称，请核对后再导入");
                        errList.add(rowList);
                        return;
                    }
                    achievement.setType(3);
                    achievement.setObjId(userId);
                }
                achievement.setYeartarget(achievement.getJanuary().add(achievement.getFebruary()).add(achievement.getMarch())
                        .add(achievement.getApril())
                        .add(achievement.getMay()).add(achievement.getJune()).add(achievement.getJuly())
                        .add(achievement.getAugust()).add(achievement.getSeptember()).add(achievement.getOctober())
                        .add(achievement.getNovember()).add(achievement.getDecember()));
                CrmAchievement crmAchievement = lambdaQuery().eq(CrmAchievement::getStatus, achievement.getStatus())
                        .eq(CrmAchievement::getType, achievement.getType()).eq(CrmAchievement::getYear, achievement.getYear())
                        .eq(CrmAchievement::getObjId, achievement.getObjId()).one();
                if (crmAchievement != null) {
                    achievement.setAchievementId(crmAchievement.getAchievementId());
                    updateById(achievement);
                } else {
                    save(achievement);
                }
            } else {
                if (rowIndex == 1) {
                    rowList.add(0, "错误信息");
                }
                errList.add(Convert.toInt(rowIndex), rowList);
            }
        });
        FileUtil.del(filePath);
        JSONObject result = new JSONObject().fluentPut("totalSize", num.get()).fluentPut("errSize", 0);
        int two=2;
        if (errList.size() > two) {
            BigExcelWriter writer = null;
            try {
                String token = IdUtil.simpleUUID();
                writer = ExcelUtil.getBigWriter(FileUtil.getTmpDirPath() + "/" + token);
                // 取消数据的黑色边框以及数据左对齐
                CellStyle cellStyle = writer.getCellStyle();
                cellStyle.setFillBackgroundColor(IndexedColors.BLACK.getIndex());
                cellStyle.setBorderTop(BorderStyle.NONE);
                cellStyle.setBorderBottom(BorderStyle.NONE);
                cellStyle.setBorderLeft(BorderStyle.NONE);
                cellStyle.setBorderRight(BorderStyle.NONE);
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                Font defaultFont = writer.createFont();
                defaultFont.setFontHeightInPoints((short) 11);
                cellStyle.setFont(defaultFont);
                // 取消数字格式的数据的黑色边框以及数据左对齐
                CellStyle cellStyleForNumber = writer.getStyleSet().getCellStyleForNumber();
                cellStyleForNumber.setBorderTop(BorderStyle.NONE);
                cellStyleForNumber.setBorderBottom(BorderStyle.NONE);
                cellStyleForNumber.setBorderLeft(BorderStyle.NONE);
                cellStyleForNumber.setBorderRight(BorderStyle.NONE);
                cellStyleForNumber.setAlignment(HorizontalAlignment.LEFT);
                cellStyleForNumber.setFillBackgroundColor(IndexedColors.BLACK.getIndex());
                cellStyleForNumber.setFont(defaultFont);

                CellStyle textStyle = writer.getWorkbook().createCellStyle();
                DataFormat format = writer.getWorkbook().createDataFormat();
                textStyle.setDataFormat(format.getFormat("@"));

                writer.merge(errList.get(1).size() + 1, errList.get(0).get(0).toString().trim(), true);
                writer.getHeadCellStyle().setAlignment(HorizontalAlignment.LEFT);
                writer.getHeadCellStyle().setWrapText(true);
                Font headFont = writer.createFont();
                headFont.setFontHeightInPoints((short) 11);
                writer.getHeadCellStyle().setFont(headFont);
                writer.getHeadCellStyle().setFillPattern(FillPatternType.NO_FILL);
                writer.getOrCreateRow(0).setHeightInPoints(120);
                writer.setRowHeight(-1, 20);

                //writer.merge(6, "系统用户导入模板(*)为必填项");
                for (int i = 0; i < errList.get(1).size(); i++) {
                    writer.getSheet().setDefaultColumnStyle(i, textStyle);
                }
                errList.remove(0);
                writer.write(errList);
                result.fluentPut("errSize", errList.size() - 1).fluentPut("token", token);
            } finally {
                if (writer != null) {
                    writer.close();
                }
            }
        }
        return result;
    }

    private String getFilePath(MultipartFile file) {
        String dirPath = FileUtil.getTmpDirPath();
        try {
            InputStream inputStream = file.getInputStream();
            File fromStream = FileUtil.writeFromStream(inputStream, dirPath + "/" + IdUtil.simpleUUID() + file.getOriginalFilename());
            return fromStream.getAbsolutePath();
        } catch (IOException e) {
            throw new CrmException(SystemCodeEnum.SYSTEM_UPLOAD_FILE_ERROR);
        }
    }

    private List<JSONObject> queryField(Integer type) {
        List<JSONObject> list = new ArrayList<>();
        if (type == 1) {
            list.add(queryField("deptName", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "部门名称", 1));
        } else {
            list.add(queryField("userName", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "员工名称", 1));
        }
        list.add(queryField("year", FieldEnum.NUMBER.getFormType(), FieldEnum.NUMBER.getType(), "年", 1));
        list.add(queryField("status", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "类型", 1));
        list.add(queryField("january", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "一月", 1));
        list.add(queryField("february", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "二月", 1));
        list.add(queryField("march", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "三月", 1));
        list.add(queryField("april", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "四月", 1));
        list.add(queryField("may", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "五月", 1));
        list.add(queryField("june", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "六月", 1));
        list.add(queryField("july", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "七月", 1));
        list.add(queryField("august", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "八月", 1));
        list.add(queryField("september", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "九月", 1));
        list.add(queryField("october", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "十月", 1));
        list.add(queryField("november", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "十一月", 1));
        list.add(queryField("december", FieldEnum.FLOATNUMBER.getFormType(), FieldEnum.FLOATNUMBER.getType(), "十二月", 1));
        return list;
    }

    private JSONObject queryField(String fieldName, String formType, Integer type, String name, Integer isNull) {
        JSONObject json = new JSONObject();
        json.fluentPut("fieldName", fieldName)
                .fluentPut("formType", formType)
                .fluentPut("type", type)
                .fluentPut("name", name).fluentPut("isNull", isNull);
        return json;
    }

    /**
     * 验证业绩目标数据
     *
     * @param crmAchievement
     * @return void
     * @date 2020/11/19 14:39
     **/
    public void verifyCrmAchievementData(CrmAchievement crmAchievement) {
        this.verifyBigDecimalData(crmAchievement.getJanuary());
        this.verifyBigDecimalData(crmAchievement.getFebruary());
        this.verifyBigDecimalData(crmAchievement.getMarch());
        this.verifyBigDecimalData(crmAchievement.getApril());
        this.verifyBigDecimalData(crmAchievement.getMay());
        this.verifyBigDecimalData(crmAchievement.getJune());
        this.verifyBigDecimalData(crmAchievement.getJuly());
        this.verifyBigDecimalData(crmAchievement.getAugust());
        this.verifyBigDecimalData(crmAchievement.getSeptember());
        this.verifyBigDecimalData(crmAchievement.getOctober());
        this.verifyBigDecimalData(crmAchievement.getNovember());
        this.verifyBigDecimalData(crmAchievement.getYeartarget());
    }


    /**
     * 验证数据是否超出数据库位数
     *
     * @param bigDecimal
     * @return void
     * @date 2020/11/19 14:39
     **/
    private void verifyBigDecimalData(BigDecimal bigDecimal) {
        if (bigDecimal != null) {
            int scale = bigDecimal.scale();
            int scaleNum = scale > 0 ? scale + 1 : 0;
            String bigDecimalStr = bigDecimal.toPlainString().replace("-", "");
            if (bigDecimalStr.length() - scaleNum > MAX_AVAILABLE_DIGITS) {
                throw new CrmException(CrmCodeEnum.CRM_ACHIEVEMENT_DATA_PARSE_ERROR);
            }
        }
    }
}
