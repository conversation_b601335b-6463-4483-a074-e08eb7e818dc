package com.kakarote.crm.service.impl;

import cn.hutool.core.map.MapProxy;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.examine.entity.ExamineRecordReturnVO;
import com.kakarote.core.feign.examine.entity.ExamineRecordSaveBO;
import com.kakarote.core.feign.examine.service.ExamineService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.common.ElasticUtil;
import com.kakarote.crm.constant.CrmActivityEnum;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.PO.CrmBusiness;
import com.kakarote.crm.entity.PO.CrmFlow;
import com.kakarote.crm.entity.PO.CrmFlowData;
import com.kakarote.crm.mapper.CrmFlowDataMapper;
import com.kakarote.crm.service.ICrmActivityService;
import com.kakarote.crm.service.ICrmBusinessService;
import com.kakarote.crm.service.ICrmFlowDataService;
import com.kakarote.crm.service.ICrmFlowService;
import io.seata.spring.annotation.GlobalTransactional;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 阶段数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
public class CrmFlowDataServiceImpl extends BaseServiceImpl<CrmFlowDataMapper, CrmFlowData> implements ICrmFlowDataService {

    @Resource
    @Lazy
    private ExamineService examineService;

    @Resource
    private ICrmFlowService crmFlowService;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private FieldService fieldService;

    @Resource
    private ICrmBusinessService crmBusinessService;

    @Resource
    private ICrmActivityService crmActivityService;

    /**
     * 根据ID查询数据
     *
     * @param id id
     * @return data
     */
    @Override
    public CrmFlowData queryFlowDataInfo(Long id) {
        CrmFlowData crmFlowData = getById(id);
        if(crmFlowData == null){
            return null;
        }
        boolean auth = AuthUtil.isCrmAuth(CrmEnum.parse(crmFlowData.getLabel()), crmFlowData.getTypeId(), CrmAuthEnum.READ);
        if(auth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        if (crmFlowData.getFieldData() != null) {
            JSONArray jsonArray = JSON.parseArray(crmFlowData.getFieldData().toString());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Object value = jsonObject.get("value");
                FieldEnum fieldEnum = FieldEnum.parse(jsonObject.getInteger("type"));
                if (value != null && !"".equals(value)) {
                    if (fieldEnum.equals(FieldEnum.DATE_INTERVAL)) {
                        jsonObject.put("value", value);
                    } else {
                        jsonObject.put("value", fieldService.convertValueByFormType(value, fieldEnum));
                    }
                }
            }
            crmFlowData.setFieldData(jsonArray);
        }
        JSONArray jsonArray = JSON.parseArray(crmFlowData.getTaskData().toString());
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.size(); i++) {
                Long createUserId = jsonArray.getJSONObject(i).getLong("createUserId");
                if (createUserId != null) {
                    jsonArray.getJSONObject(i).put("createUserName", UserCacheUtil.getUserName(createUserId));
                }
            }
        }
        crmFlowData.setTaskData(jsonArray);
        if (crmFlowData.getExamineStatus() == null) {
            crmFlowData.setExamineStatus(0);
        }
        return crmFlowData;
    }

    /**
     * 20220228 wwl
     * 抽取的共通代码 保存的流程数据
     *
     * @param flowData flowData 页面传递来的参数
     * @param crmFlowData crmFlowData 本地查到的
     */
    private void doSaveFlowData(CrmFlowData flowData, CrmFlowData crmFlowData) {
        int four = 4;
        int two = 2;
        if (!Objects.equals(0L, crmFlowData.getExamineRecordId()) && !Objects.equals(four, crmFlowData.getExamineStatus()) && !Objects.equals(two, crmFlowData.getExamineStatus())) {
            throw new CrmException(CrmCodeEnum.CRM_FLOW_EXAMINE_STATUS_ERROR);
        }
        LambdaUpdateChainWrapper<CrmFlowData> chainWrapper = lambdaUpdate();
        boolean examineFlag = flowData.getExamineFlowData() != null && !Objects.equals(0L, crmFlowData.getExamineId()) && !Objects.equals(0, flowData.getStatus());
        if (examineFlag) {
            ExamineRecordSaveBO examineFlowData = flowData.getExamineFlowData();
            examineFlowData.setLabel(20);
            examineFlowData.setTypeId(flowData.getId());
            examineFlowData.setCategoryId(crmFlowData.getExamineId());
            examineFlowData.setRecordId(null);
            if (examineFlowData.getDataMap() != null) {
                examineFlowData.getDataMap().put("createUserId", UserUtil.getUserId());
            } else {
                examineFlowData.setDataMap(Collections.singletonMap("createUserId", UserUtil.getUserId()));
            }
            ExamineRecordReturnVO data = examineService.addExamineRecord(examineFlowData).getData();
            chainWrapper.set(CrmFlowData::getExamineRecordId, data.getRecordId());
            chainWrapper.set(CrmFlowData::getExamineStatus, data.getExamineStatus());
        }
        if (flowData.getTaskData() instanceof List) {
            ((List<?>) flowData.getTaskData()).forEach(data -> {
                if (data instanceof Map) {
                    MapProxy proxy = MapUtil.createProxy((Map<?, ?>) data);
                    String isOk = "isOk";
                    String createTime = "createTime";
                    if (Objects.equals(1, proxy.getInt(isOk))) {
                        proxy.put("createUserId", UserUtil.getUserId());
                        if (!proxy.containsKey(createTime)) {
                            proxy.put("createTime", System.currentTimeMillis());
                        }
                    } else {
                        proxy.put("createTime", null);
                    }
                }
            });
        }
        if (flowData.getTaskData() != null) {
            chainWrapper.set(CrmFlowData::getTaskData, JSON.toJSONString(flowData.getTaskData()));
        }
        if (flowData.getFieldData() != null) {
            chainWrapper.set(CrmFlowData::getFieldData, JSON.toJSONString(flowData.getFieldData()));
        }
        chainWrapper.eq(CrmFlowData::getId, flowData.getId());
        //如果不是保存为草稿
        if (!Objects.equals(two, flowData.getStatus())) {
            List<CrmFlowData> flowDataList = lambdaQuery()
                    .eq(CrmFlowData::getLabel, crmFlowData.getLabel())
                    .eq(CrmFlowData::getTypeId, crmFlowData.getTypeId())
                    .orderByAsc(CrmFlowData::getOrderNum)
                    .list();
            Optional<CrmFlowData> min = flowDataList
                    .stream()
                    .filter(data -> Objects.equals(0, data.getStatus()) || Objects.equals(2, data.getStatus()))
                    .min(Comparator.comparingInt(CrmFlowData::getOrderNum));
            if (min.isPresent()) {
                CrmFlowData newFlowData = min.get();
                //判断是否有审批信息
                List<CrmFlowData> collect = flowDataList
                        .stream()
                        .filter(data -> data.getOrderNum() < newFlowData.getOrderNum())
                        .collect(Collectors.toList());

                for (CrmFlowData data : collect) {
                    if (!Objects.equals(0L, data.getExamineRecordId()) && !Objects.equals(1, data.getExamineStatus())) {
                        throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_EXAMINE_ERROR, data.getFlowName());
                    }
                }
                // crmActivityService.addActivity(
                //         3
                //         , crmFlowData.getLabel() == 8 ? CrmActivityEnum.RECEIVABLES_PLAN : CrmActivityEnum.parse(crmFlowData.getLabel())
                //         , crmFlowData.getTypeId()
                //         , newFlowData.getFlowName()
                //         , crmFlowData.getFlowName()
                // );
            } else {
                for (CrmFlowData data : flowDataList) {
                    if (!Objects.equals(0L, data.getExamineRecordId()) && !Objects.equals(1, data.getExamineStatus())) {
                        throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_EXAMINE_ERROR, data.getFlowName());
                    }
                }
            }
            // 20220228 wwl ↓
            if (examineFlag && flowData.getStatus() == 1) {
                // 有审核时， 将状态改为 已提交但未审核 的状态
                chainWrapper.set(CrmFlowData::getStatus, 3).update();
                return;
            }
            // 20220228 wwl ↑
            chainWrapper.set(CrmFlowData::getStatus, 1).update();
            // 获取下一级流程
            getNewFlowName(crmFlowData, flowData, flowDataList);
        }
        // 保存草稿
        else {
            chainWrapper.set(CrmFlowData::getStatus, flowData.getStatus());
            chainWrapper.update();
        }
    }

    /**
     * 保存阶段流程流转信息
     *
     * @param flowData 保存数据
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveFlowData(CrmFlowData flowData) {
        CrmFlowData crmFlowData = getById(flowData.getId());
        if (crmFlowData == null) {
            return;
        }
        //跟随crm权限决定是否可以编辑
        boolean rwAuth = AuthUtil.isRwAuth(crmFlowData.getTypeId(), CrmEnum.parse(crmFlowData.getLabel()), CrmAuthEnum.EDIT);
        if (rwAuth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        if (isCanUpdate(crmFlowData.getLabel(), crmFlowData.getTypeId())) {
            // 20220226 在“谈判审核”阶段-保存草稿-status改为2了，此时还想回退阶段或者 保存阶段草稿
            if (stageDraft(crmFlowData.getLabel(), crmFlowData.getTypeId())) {
                doSaveFlowData(flowData, crmFlowData);
            }
            return;
        }
        doSaveFlowData(flowData, crmFlowData);
    }

    /**
     * 阶段流程推进后，获取最新的阶段流程并修改es
     *
     * @param crmFlowData 流程信息
     */
    private void getNewFlowName(CrmFlowData crmFlowData, CrmFlowData flowData, List<CrmFlowData> flowDataList) {
        // 20220225 wwl 新增 ↓
        if (null == flowDataList) {
            flowDataList = lambdaQuery()
                    .eq(CrmFlowData::getLabel, crmFlowData.getLabel())
                    .eq(CrmFlowData::getTypeId, crmFlowData.getTypeId())
                    .orderByAsc(CrmFlowData::getOrderNum)
                    .list();
        }
        if (null != flowData) {
            // 默认商机组没有审批，所以直接通过，并添加商机活动信息
            if (!(flowData.getExamineFlowData() != null && !Objects.equals(0L, crmFlowData.getExamineId()) && !Objects.equals(0, flowData.getStatus()))) {
                getNewFlowName(crmFlowData, flowDataList);
                return;
            }
        }
        // 20220225 wwl 新增 ↑
        //审批状态未通过时不进入下个阶段
        if (!Objects.equals(1, crmFlowData.getExamineStatus())) {
            return;
        }
        getNewFlowName(crmFlowData, flowDataList);
    }

    private void getNewFlowName(CrmFlowData crmFlowData, List<CrmFlowData> flowDataList) {
        // 20220226 wwl 应测试要求，通过后才能在商机活动中添加信息
        Optional<CrmFlowData> min2 = flowDataList
                .stream()
                .filter(data -> Objects.equals(0, data.getStatus()) || Objects.equals(2, data.getStatus()))
                .filter(data -> data.getOrderNum() > crmFlowData.getOrderNum())
                .min(Comparator.comparingInt(CrmFlowData::getOrderNum));
        if (min2.isPresent()) {
            CrmFlowData newFlowData2 = min2.get();
            String newFlowName = newFlowData2.getFlowName();
            //添加活动记录
            crmActivityService.addActivity(
                    3
                    , crmFlowData.getLabel() == 8 ? CrmActivityEnum.RECEIVABLES_PLAN : CrmActivityEnum.parse(crmFlowData.getLabel())
                    , crmFlowData.getTypeId()
                    , newFlowName
                    , crmFlowData.getFlowName()
            );
        }
        Optional<CrmFlowData> flowDataOptional = lambdaQuery().eq(CrmFlowData::getLabel, crmFlowData.getLabel()).eq(CrmFlowData::getTypeId, crmFlowData.getTypeId()).eq(CrmFlowData::getStatus, 0).orderByAsc(CrmFlowData::getOrderNum).last(" limit 1").oneOpt();
        flowDataOptional.ifPresent(data -> ElasticUtil.updateField(restHighLevelClient, "settingName", data.getFlowName(), Collections.singletonList(crmFlowData.getTypeId()), CrmEnum.parse(crmFlowData.getLabel()).getIndex()));
        int five = 5;
        if (crmFlowData.getLabel() == five) {
            if (flowDataOptional.isPresent()) {
                crmBusinessService.lambdaUpdate().set(CrmBusiness::getStatusId, flowDataOptional.get().getSettingId())
                        .eq(CrmBusiness::getBusinessId, crmFlowData.getTypeId()).update();
                Map<String, Object> map = new HashMap<>();
                map.put("statusId", flowDataOptional.get().getSettingId());
                crmBusinessService.updateEs(map, crmFlowData.getTypeId());
            }
        }
    }

    /**
     * 修改审批状态
     *
     * @param dataId 数据ID
     * @param status 状态
     */
    @Override
    public void updateExamineStatus(Long dataId, Integer status) {
        CrmFlowData flowData = getById(dataId);
        if (flowData == null) {
            return;
        }
        int four = 4;
        if (status == four) {
            if (flowData.getExamineStatus() == 1) {
                throw new CrmException(CrmCodeEnum.CRM_EXAMINE_RECHECK_PASS_ERROR);
            }
        }
        LambdaUpdateChainWrapper<CrmFlowData> chainWrapper = lambdaUpdate();
        chainWrapper.set(CrmFlowData::getExamineStatus, status);
        if (1 == status) {
            chainWrapper.set(CrmFlowData::getStatus, 1).eq(CrmFlowData::getId, dataId).update();
        } else {
            chainWrapper.set(CrmFlowData::getStatus, 0).eq(CrmFlowData::getId, dataId).update();
        }
        if (1 == status) {
            // 20220225 wwl  1 == status 表示审核通过，并赋值给flowData.setExamineStatus，然后再找下一级
            flowData.setExamineStatus(status);
            getNewFlowName(flowData, null, null);
        }
    }


    private void doUpdateFlowDataStatus(CrmFlowData crmFlowData, Integer finalStatus, String remark, boolean stageDraftFlag) {
        //跟随crm权限决定是否可以编辑
        boolean rwAuth = AuthUtil.isRwAuth(crmFlowData.getTypeId(), CrmEnum.parse(crmFlowData.getLabel()), CrmAuthEnum.EDIT);
        if (rwAuth) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        CrmFlow crmFlow = crmFlowService.getById(crmFlowData.getFlowId());
        List<CrmFlowData> flowDataList = lambdaQuery().eq(CrmFlowData::getLabel, crmFlowData.getLabel()).eq(CrmFlowData::getTypeId, crmFlowData.getTypeId()).orderByAsc(CrmFlowData::getOrderNum).list();
        //获得上次阶段
        Optional<CrmFlowData>  min = flowDataList
                    .stream()
                    // 20220228 wwl 草稿的话就取2
                    .filter(data -> Objects.equals(stageDraftFlag ? 2 : 0, data.getStatus()))
                    .min(Comparator.comparingInt(CrmFlowData::getOrderNum));
        String oldFlowName;
        String newFlowName = crmFlowData.getFlowName();
        //添加活动
        if (finalStatus == null && min.isPresent()) {
            // 20220228 wwl 草稿的话就取2
            oldFlowName = flowDataList.stream().filter(data -> Objects.equals(stageDraftFlag ? 2 : 0, data.getStatus())).min(Comparator.comparingInt(CrmFlowData::getOrderNum)).get().getFlowName();
        } else {
            oldFlowName = crmFlowData.getFlowName();
            newFlowName = castToFinalStatus(finalStatus, crmFlow);
        }
        // 20220226 wwl 注释下边一行 移到推进成功后才添加记录
        // crmActivityService.addActivity(3, crmFlowData.getLabel() == 8 ? CrmActivityEnum.RECEIVABLES_PLAN : CrmActivityEnum.parse(crmFlowData.getLabel()), crmFlowData.getTypeId(), newFlowName, oldFlowName);

        Map<String, Object> dataMap = new HashMap<>(2, 1.0F);
        dataMap.put("flowName", crmFlow.getFlowName());
        if (finalStatus == null) {
            // 0初始状态, 2存草稿, 3有审核流-已提交未审核
            Optional<CrmFlowData> flowDataOptional = flowDataList.stream().filter(data -> Arrays.asList(0, 2, 3).contains(data.getStatus())).findFirst();
            //如果不存在未完成的阶段，取最好一个阶段
            if (!flowDataOptional.isPresent()) {
                flowDataOptional = Optional.of(flowDataList.get(flowDataList.size() - 1));
            }
            //要推进的阶段为当前阶段，则直接退出
            if (crmFlowData.getId().equals(flowDataOptional.get().getId())) {
                return;
            }
            CrmFlowData currentFlowData = flowDataOptional.get();
            if (!Objects.equals(0L, currentFlowData.getExamineRecordId())) {
                throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_EXAMINE_ERROR, currentFlowData.getFlowName());
            }
            //是向上推进还是向下推进
            boolean isNext = crmFlowData.getOrderNum() > currentFlowData.getOrderNum();
            List<CrmFlowData> collect;
            int status = isNext ? 1 : 0;
            if (isNext) {
                collect = flowDataList.stream().filter(data -> {
                    if (data.getOrderNum() >= currentFlowData.getOrderNum() && data.getOrderNum() < crmFlowData.getOrderNum()) {
                        data.setStatus(status);
                    }
                    return data.getOrderNum() < crmFlowData.getOrderNum();
                }).collect(Collectors.toList());
            } else {
                collect = flowDataList.stream().filter(data -> {
                    if (data.getOrderNum() < currentFlowData.getOrderNum() && data.getOrderNum() >= crmFlowData.getOrderNum()) {
                        data.setStatus(status);
                    }
                    return data.getOrderNum() >= crmFlowData.getOrderNum();
                }).collect(Collectors.toList());
            }
            //判断是否有审批信息
            for (CrmFlowData flowData : collect) {
                if (isNext) {
                    // getExamineId不为0，则表示有审批
                    if (!Objects.equals(0L, flowData.getExamineId())) {
                        // 202220225 wwl 新增判断 再判断是否已审批通过,1为通过
                        if (null != flowData.getExamineStatus() && 1 == flowData.getExamineStatus()) {
                            continue;
                        }
                        throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_EXAMINE_ERROR, flowData.getFlowName());
                    }
                    // 20220228 wwl 对每个flow得下的task进行判断，是否有必选，且未选中就弹错误提示
                    Object taskData = flowData.getTaskData();
                    if (null != taskData && !"".equals(taskData)) {
                        JSONArray taskDataList = JSONObject.parseArray(taskData.toString());
                        for (Object object : taskDataList) {
                            JSONObject obj = (JSONObject) object;
                            // 必选
                            if (1 == TypeUtils.castToInt(obj.get("isNull"))) {
                                // 没有选中记录时会没有该字段
                                if (null == obj.get("isOk")) {
                                    throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_MUST_CHECK_ERROR, flowData.getFlowName());
                                }
                                // 选中1，未选0
                                if (0 == TypeUtils.castToInt(obj.get("isOk"))) {
                                    throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_MUST_CHECK_ERROR, flowData.getFlowName());
                                }
                            }
                        }
                    }

                } else {
                    if (!Objects.equals(0L, flowData.getExamineRecordId())) {
                        throw new CrmException(CrmCodeEnum.CRM_FLOW_EXIST_EXAMINE_ERROR, flowData.getFlowName());
                    }
                }
            }
            //修改多个流程状态
            saveOrUpdateBatch(flowDataList);
            flowDataOptional = flowDataList.stream().filter(data -> Objects.equals(0, data.getStatus())).findFirst();
            flowDataOptional.ifPresent(flowData -> dataMap.put("settingName", flowData.getFlowName()));
            int five = 5;
            if (crmFlow.getLabel() == five) {
                crmBusinessService.lambdaUpdate().set(CrmBusiness::getStatusId, crmFlowData.getSettingId())
                        .eq(CrmBusiness::getBusinessId, crmFlowData.getTypeId()).update();
                Map<String, Object> map = new HashMap<>();
                map.put("statusId", crmFlowData.getSettingId());
                crmBusinessService.updateEs(map, crmFlowData.getTypeId());
            }
            // 20220226 wwl 在修改成功后添加 商机活动记录
            crmActivityService.addActivity(3, crmFlowData.getLabel() == 8 ? CrmActivityEnum.RECEIVABLES_PLAN : CrmActivityEnum.parse(crmFlowData.getLabel()), crmFlowData.getTypeId(), newFlowName, oldFlowName);
        } else {
            for (CrmFlowData flowData : flowDataList) {
                //只有标记为赢单才必须需要走完审核
                if (Objects.equals(1, finalStatus) && !Objects.equals(0L, flowData.getExamineId()) && !Objects.equals(1, flowData.getExamineStatus())) {
                    throw new CrmException(CrmCodeEnum.CRM_FLOW_EXAMINE_NOT_FINISHED_ERROR, flowData.getFlowName());
                }
            }
            //lambdaUpdate().set(CrmFlowData::getStatus, 1).eq(CrmFlowData::getId,dataId).update();
            CrmFlowData examineFlowData = new CrmFlowData();
            examineFlowData.setLabel(crmFlowData.getLabel());
            examineFlowData.setTypeId(crmFlowData.getTypeId());
            examineFlowData.setFlowId(crmFlowData.getFlowId());
            examineFlowData.setSettingId(0L);
            examineFlowData.setStatus(finalStatus);
            examineFlowData.setFlowName(castToFinalStatus(finalStatus, crmFlow));
            examineFlowData.setOrderNum(999);
            examineFlowData.setExamineId(crmFlowData.getExamineId());
            examineFlowData.setExamineRecordId(crmFlowData.getExamineRecordId());
            examineFlowData.setRemark(remark);
            save(examineFlowData);
            dataMap.put("settingName", examineFlowData.getFlowName());
            dataMap.put("flowStatus", finalStatus);
            int five = 5;
            if (crmFlow.getLabel() == five) {
                crmBusinessService.lambdaUpdate().set(CrmBusiness::getStatusId, examineFlowData.getSettingId())
                        //切换商机组之后状态为未完成
                        .set(CrmBusiness::getIsEnd, finalStatus)
                        .eq(CrmBusiness::getBusinessId, examineFlowData.getTypeId()).update();
                Map<String, Object> map = new HashMap<>();
                map.put("statusId", examineFlowData.getSettingId());
                crmBusinessService.updateEs(map, examineFlowData.getTypeId());
            }
        }

        //修改数据列表页展示字段
        ElasticUtil.updateField(restHighLevelClient, dataMap, crmFlowData.getTypeId(), CrmEnum.parse(crmFlowData.getLabel()).getIndex());
    }

    /**
     * 修改流程阶段
     *
     * @param dataId      数据ID
     * @param finalStatus 最终状态
     */
    @Override
    public void updateFlowDataStatus(Long dataId, Integer finalStatus, String remark) {
        CrmFlowData crmFlowData = getById(dataId);
        if (crmFlowData == null) {
            return;
        }
        if (isCanUpdate(crmFlowData.getLabel(), crmFlowData.getTypeId()) && finalStatus == null) {
            // 20220226 如果阶段都有草稿，status都是2，此时还想回退阶段，就会有问题。所以单独查询下
            if (stageDraft(crmFlowData.getLabel(), crmFlowData.getTypeId())) {
                doUpdateFlowDataStatus(crmFlowData, finalStatus, remark, true);
            }
            return;
        }
        doUpdateFlowDataStatus(crmFlowData, finalStatus, remark, false);
    }

    private String castToFinalStatus(Integer finalStatus, CrmFlow crmFlow) {
        if (CrmEnum.BUSINESS.getType().equals(crmFlow.getLabel())) {
            //1 成功 2 失败 3 无效 4 冻结 0 进行中
            switch (finalStatus) {
                case 1:
                    return "赢单";
                case 2:
                    return "输单";
                case 3:
                    return "无效";
                case 4:
                    return "冻结";
                default:
                    throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
            }
        } else {
            int two = 2;
            if (Objects.equals(two, finalStatus)) {
                return crmFlow.getFailedName();
            } else {
                return crmFlow.getSuccessName();
            }
        }
    }

    private boolean isCanUpdate(Integer label, Long typeId) {
        return lambdaQuery().eq(CrmFlowData::getLabel, label).eq(CrmFlowData::getTypeId, typeId).eq(CrmFlowData::getStatus, 0).count() == 0;
    }

    /**
     * 在isCanUpdate方法后执行本方法
     * 是否存在草稿状态
     * @param label label
     * @param typeId typeId
     * @return boolean
     */
    private boolean stageDraft(Integer label, Long typeId) {
        return lambdaQuery().eq(CrmFlowData::getLabel, label).eq(CrmFlowData::getTypeId, typeId).eq(CrmFlowData::getStatus, 2).count() > 0;
    }
}
