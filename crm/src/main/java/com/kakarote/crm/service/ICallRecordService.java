package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.BO.CallRecordBO;
import com.kakarote.crm.entity.PO.CallRecord;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 通话记录管理
 * <AUTHOR>
 */
public interface ICallRecordService extends BaseService<CallRecord> {
    
    /**
     * 添加通话记录
     * @return
     */
    CallRecord saveRecord(CallRecord callRecord);

    /**
     * 查询通话记录列表
     * @param callRecordBO
     * @return
     */
    BasePage<JSONObject> pageCallRecordList(CallRecordBO callRecordBO);
    /**
     * 上传文件
     * @param file 文件
     * @param id id
     * @param prefix
     * @return
     */
    boolean upload(MultipartFile file, String id, String prefix);
    /**
     * 录音下载
     * @return
     */
    void download(String id, HttpServletResponse response);

    /**
     * 搜索呼入电话 是否存在记录
     * @param search
     * @return
     */
    JSONObject searchPhone(String search);
    /**
     * 查询可呼叫的电话
     * @return
     */
    List<JSONObject> queryPhoneNumber(String model, String modelId);

}
