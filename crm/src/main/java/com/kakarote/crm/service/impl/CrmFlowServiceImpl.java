package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmFlowPageBO;
import com.kakarote.crm.entity.BO.CrmFlowSaveBO;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFlowDataVO;
import com.kakarote.crm.entity.VO.CrmFlowFieldVO;
import com.kakarote.crm.entity.VO.CrmFlowInfoVO;
import com.kakarote.crm.entity.VO.CrmFlowSettingVO;
import com.kakarote.crm.mapper.CrmFlowMapper;
import com.kakarote.crm.service.*;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 阶段流程主信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Service
public class CrmFlowServiceImpl extends BaseServiceImpl<CrmFlowMapper, CrmFlow> implements ICrmFlowService {

    @Autowired
    private ICrmFlowSettingService flowSettingService;

    @Autowired
    private ICrmFlowDataService flowDataService;

    @Autowired
    private ICrmFlowCommentService flowCommentService;

    @Autowired
    private ICrmBusinessService crmBusinessService;

    private static final int TWO = 2;

    /**
     * 查询列表
     *
     * @param crmFlowPageBO 查询对象
     * @return page
     */
    @Override
    public BasePage<CrmFlow> queryList(CrmFlowPageBO crmFlowPageBO) {
        LambdaQueryChainWrapper<CrmFlow> chainWrapper = lambdaQuery();
        if (crmFlowPageBO.getLabel() != null) {
            chainWrapper.eq(CrmFlow::getLabel, crmFlowPageBO.getLabel());
        }
        if (crmFlowPageBO.getStatus() != null) {
            chainWrapper.eq(CrmFlow::getStatus, Objects.equals(1, crmFlowPageBO.getStatus()) ? 1 : 2);
        } else {
            chainWrapper.ne(CrmFlow::getStatus, 3);
        }
        chainWrapper.orderByDesc(CrmFlow::getUpdateTime);
        BasePage<CrmFlow> flowBasePage = chainWrapper.page(crmFlowPageBO.parse());
        for (CrmFlow crmFlow : flowBasePage.getList()) {
            if (StrUtil.isNotEmpty(crmFlow.getUserIds())) {
                JSONArray jsonArray = JSON.parseArray(crmFlow.getUserIds());
                crmFlow.setUserNameList(UserCacheUtil.getUserNameList(jsonArray));
            }
            if (StrUtil.isNotEmpty(crmFlow.getDeptIds())) {
                JSONArray jsonArray = JSON.parseArray(crmFlow.getDeptIds());
                crmFlow.setDeptNameList(UserCacheUtil.getDeptNameList(jsonArray));
            }
            crmFlow.setCreateUserName(UserCacheUtil.getUserName(crmFlow.getCreateUserId()));
            crmFlow.setUpdateUserName(UserCacheUtil.getUserName(crmFlow.getUpdateUserId()));
        }
        return flowBasePage;
    }

    /**
     * 保存阶段流程对象
     *
     * @param flowSaveBO data
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveFlow(CrmFlowSaveBO flowSaveBO) {
        if (flowSaveBO.getSettingList().isEmpty()) {
            throw new CrmException(CrmCodeEnum.CRM_FLOW_DATA_ERROR);
        }
        CrmFlow crmFlow = new CrmFlow();
        //不可修改，修改是新建一个，将原来的废弃
        if (flowSaveBO.getFlowId() != null) {
            CrmFlow flow = getById(flowSaveBO.getFlowId());
            flow.setStatus(3);
            updateById(flow);
            crmFlow.setCreateTime(flow.getCreateTime());
            crmFlow.setCreateUserId(flow.getCreateUserId());
        }
        crmFlow.setLabel(flowSaveBO.getLabel());
        Integer count = lambdaQuery().eq(CrmFlow::getLabel, flowSaveBO.getLabel()).eq(CrmFlow::getStatus, 1).count();
        crmFlow.setFlowName(flowSaveBO.getFlowName());
        crmFlow.setUserIds(JSON.toJSONString(flowSaveBO.getUserIdList()));
        crmFlow.setDeptIds(JSON.toJSONString(flowSaveBO.getDeptIdList()));
        crmFlow.setSuccessName(flowSaveBO.getSuccessName());
        crmFlow.setFailedName(flowSaveBO.getFailedName());
        crmFlow.setUpdateTime(LocalDateTimeUtil.now());
        crmFlow.setUpdateUserId(UserUtil.getUserId());
        if (count > 0 && !CrmEnum.BUSINESS.getType().equals(flowSaveBO.getLabel())) {
            crmFlow.setStatus(2);
        } else {
            crmFlow.setStatus(1);
        }
        save(crmFlow);
        flowSettingService.saveFlowSetting(crmFlow.getFlowId(), flowSaveBO.getSettingList());
    }


    /**
     * 修改阶段流程状态
     *
     * @param flowId 流程ID
     * @param status 状态
     */
    @Override
    public void updateFlowStatus(Long flowId, Integer status) {
        CrmFlow crmFlow = getById(flowId);
        if (crmFlow == null) {
            return;
        }
        //只有停用状态才可以删除
        int three = 3;
        if (Objects.equals(three, status) && crmFlow.getStatus() == TWO) {
            crmFlow.setStatus(3);
            updateById(crmFlow);
        }
        //停用正在启用的阶段流程
        if (Objects.equals(TWO, status) && crmFlow.getStatus() == 1) {
            crmFlow.setStatus(2);
            updateById(crmFlow);
        }
        //停用正在启用的阶段流程
        if (Objects.equals(1, status) && crmFlow.getStatus() == TWO) {
            CrmEnum crmEnum = CrmEnum.parse(crmFlow.getLabel());
            if (crmEnum != CrmEnum.BUSINESS) {
                Optional<CrmFlow> startFlow = lambdaQuery().eq(CrmFlow::getLabel, crmFlow.getLabel()).eq(CrmFlow::getStatus, 1).oneOpt();
                if (startFlow.isPresent()) {
                    throw new CrmException(CrmCodeEnum.CRM_FLOW_START_ERROR, crmEnum.getRemarks(), startFlow.get().getFlowName());
                }
            }
            crmFlow.setStatus(1);
            updateById(crmFlow);
        }

    }

    /**
     * 阶段流程详情
     *
     * @param flowId flowId
     * @return data
     */
    @Override
    public CrmFlowInfoVO flowInfo(Long flowId) {
        CrmFlow crmFlow = getById(flowId);
        CrmFlowInfoVO crmFlowInfoVO = BeanUtil.copyProperties(crmFlow, CrmFlowInfoVO.class);
        crmFlowInfoVO.setUserList(JSON.parseArray(crmFlow.getUserIds(), Long.class));
        crmFlowInfoVO.setDeptList(JSON.parseArray(crmFlow.getDeptIds(), Long.class));
        List<CrmFlowSettingVO> flowSettingVOS = flowSettingService.queryFlowSettingList(crmFlow.getFlowId(), true);
        for (CrmFlowSettingVO settingVO : flowSettingVOS) {
            for (List<CrmFlowField> fieldList : settingVO.getFormList()) {
                for (CrmFlowField crmFlowField : fieldList) {
                    recordToFormType(crmFlowField, FieldEnum.parse(crmFlowField.getType()));
                }
            }
        }
        crmFlowInfoVO.setSettingList(flowSettingVOS);
        return crmFlowInfoVO;
    }

    /**
     * 查询阶段流程列表
     *
     * @param label  同CRM label
     * @param typeId 对应数据的主键ID
     * @return data
     */
    @Override
    public CrmFlowDataVO queryFlowSettingList(Integer label, Long typeId) {
        CrmFlowDataVO crmFlowDataVO = new CrmFlowDataVO();
        List<CrmFlowData> flowDataList = flowDataService
                .lambdaQuery()
                .select(CrmFlowData::getFlowId, CrmFlowData::getStatus, CrmFlowData::getFlowName, CrmFlowData::getRate, CrmFlowData::getId, CrmFlowData::getExamineStatus, CrmFlowData::getSettingId)
                .eq(CrmFlowData::getLabel, label).eq(CrmFlowData::getTypeId, typeId).ne(CrmFlowData::getSettingId, 0).orderByAsc(CrmFlowData::getOrderNum).list();
        crmFlowDataVO.setLabel(label);
        crmFlowDataVO.setTypeId(typeId);
        if (flowDataList.isEmpty()) {
            crmFlowDataVO.setSettingList(Collections.emptyList());
            return crmFlowDataVO;
        }
        CrmFlow crmFlow = getById(flowDataList.get(0).getFlowId());
        crmFlowDataVO.setSuccessName(crmFlow.getSuccessName());
        crmFlowDataVO.setFailedName(crmFlow.getFailedName());
        Optional<CrmFlowData> finalStatusData = flowDataService.lambdaQuery().eq(CrmFlowData::getLabel, label).eq(CrmFlowData::getTypeId, typeId).eq(CrmFlowData::getSettingId, 0).oneOpt();
        crmFlowDataVO.setFinalStatus(finalStatusData.isPresent() ? finalStatusData.get().getStatus() : 0);
        crmFlowDataVO.setRemark(finalStatusData.map(CrmFlowData::getRemark).orElse(null));
        Long dataId = null;
        List<CrmFlowDataVO.CrmFlowDataSetting> settingList = new ArrayList<>(flowDataList.size());
        for (CrmFlowData flowData : flowDataList) {
            CrmFlowDataVO.CrmFlowDataSetting dataSetting = BeanUtil.copyProperties(flowData, CrmFlowDataVO.CrmFlowDataSetting.class);
            if (dataSetting.getExamineStatus() == null) {
                dataSetting.setExamineStatus(0);
            }
            settingList.add(dataSetting);
            if (dataId == null && 1 != flowData.getStatus()) {
                dataId = flowData.getId();
            }
        }
        if (dataId == null) {
            dataId = flowDataList.get(flowDataList.size() - 1).getId();
        }
        crmFlowDataVO.setDataId(dataId);
        crmFlowDataVO.setSettingList(settingList);
        return crmFlowDataVO;
    }

    /**
     * 保存阶段流程信息
     *
     * @param crmEnum crmEnum
     * @param typeId  对应数据的主键ID
     */
    @Override
    public Map<String, Object> initFlowData(SimpleUser simpleUser, CrmEnum crmEnum, Long typeId, Long flowId) {
        Map<String, Object> objectMap = new HashMap<>(4, 1.0f);
        List<CrmFlowData> dataList = flowDataService.lambdaQuery().select(CrmFlowData::getId).eq(CrmFlowData::getLabel, crmEnum.getType()).eq(CrmFlowData::getTypeId, typeId).list();
        if (dataList.size() > 0) {
            if (crmEnum != CrmEnum.BUSINESS) {
                return objectMap;
            } else {
                List<Long> dataIds = dataList.stream().map(CrmFlowData::getId).collect(Collectors.toList());
                flowCommentService.lambdaUpdate().in(CrmFlowComment::getDataId, dataIds).remove();
                //如果是商机更换阶段，把原来的阶段数据删除
                flowDataService.removeByIds(dataIds);
            }
        }

        if (crmEnum != CrmEnum.BUSINESS) {
            Map<String, Object> dataMap = new HashMap<>(4);
            dataMap.put("userId", simpleUser.getUserId());
            dataMap.put("deptId", simpleUser.getDeptId());
            dataMap.put("label", crmEnum.getType());
            CrmFlow crmFlow = getBaseMapper().queryCrmFlow(dataMap);
            if (crmFlow == null) {
                return objectMap;
            }
            flowId = crmFlow.getFlowId();
        }
        CrmFlow crmFlow = getById(flowId);
        List<CrmFlowSettingVO> vos = flowSettingService.queryFlowSettingList(flowId, false);
        List<CrmFlowData> flowDataList = new ArrayList<>(vos.size());
        List<Integer> orderNums = new ArrayList<>();
        for (CrmFlowSettingVO vo : vos) {
            CrmFlowData flowData = new CrmFlowData();
            flowData.setFlowId(flowId);
            flowData.setFlowName(vo.getName());
            flowData.setLabel(crmEnum.getType());
            flowData.setTypeId(typeId);
            flowData.setSettingId(vo.getSettingId());
            flowData.setElapsedTime(0);
            flowData.setRate(vo.getRate());
            List<List<CrmFlowField>> voFormList = vo.getFormList();
            List<CrmFlowField> fieldList = new ArrayList<>();
            for (List<CrmFlowField> flowFields : voFormList) {
                for (CrmFlowField flowField : flowFields) {
                    recordToFormType(flowField, FieldEnum.parse(flowField.getType()));
                    fieldList.add(flowField);
                }
            }
            flowData.setFieldData(JSON.toJSONString(fieldList));
            //保存数据时，清空任务的创建人
            for (CrmFlowTask crmFlowTask : vo.getTaskList()) {
                crmFlowTask.setCreateTime(null);
                crmFlowTask.setCreateUserId(null);
            }
            flowData.setTaskData(JSON.toJSONString(vo.getTaskList()));
            flowData.setOrderNum(vo.getOrderNum());
            flowData.setStatus(0);
            flowData.setExamineId(vo.getExamineId());
            if (objectMap.isEmpty()) {
                objectMap.put("flowName", crmFlow.getFlowName());
                objectMap.put("settingName", vo.getName());
            }
            flowDataList.add(flowData);
            orderNums.add(vo.getOrderNum());
        }
        if (crmEnum == CrmEnum.BUSINESS) {
            Integer orderNum = Collections.min(orderNums);
            flowDataList.forEach(flowData -> {
                if (ObjectUtil.equal(flowData.getOrderNum(), orderNum)) {
                    crmBusinessService.lambdaUpdate().set(CrmBusiness::getStatusId, flowData.getSettingId())
                            //切换商机组之后状态为未完成
                            .set(CrmBusiness::getIsEnd, 0)
                            .eq(CrmBusiness::getBusinessId, flowData.getTypeId()).update();
                    Map<String, Object> map = new HashMap<>();
                    map.put("statusId", flowData.getSettingId());
                    crmBusinessService.updateEs(map, flowData.getTypeId());
                }
            });
        }
        flowDataService.saveBatch(flowDataList);
        objectMap.put("flowStatus", 0);
        return objectMap;
    }

    @Override
    public List<Object> queryBusinessSetting() {
        return queryCrmFlowSearchFieldInfo(CrmEnum.BUSINESS.getType());
    }

    @Override
    public List<Object> queryCrmFlowSearchFieldInfo(Integer label) {
        List<CrmFlow> crmFlows = lambdaQuery().select(CrmFlow::getFlowName, CrmFlow::getFlowId).eq(CrmFlow::getLabel, label).ne(CrmFlow::getStatus, 3).list();
        return crmFlows.stream().map(data -> {
            CrmFlowFieldVO flowFieldVO = BeanUtil.copyProperties(data, CrmFlowFieldVO.class);
            List<CrmFlowSetting> list = flowSettingService.lambdaQuery().select(CrmFlowSetting::getSettingId, CrmFlowSetting::getName).eq(CrmFlowSetting::getFlowId, data.getFlowId()).list();
            flowFieldVO.setSettingList(list.stream().map(setting -> {
                CrmFlowFieldVO.SettingFieldVO fieldVO = new CrmFlowFieldVO.SettingFieldVO();
                fieldVO.setSettingId(setting.getSettingId());
                fieldVO.setSettingName(setting.getName());
                return fieldVO;
            }).collect(Collectors.toList()));
            return flowFieldVO;
        }).collect(Collectors.toList());
    }

    @Override
    public String queryCrmFlowNameById(Long flowId) {
        CrmFlow crmFlow = getById(flowId);
        if (crmFlow == null) {
            return "空";
        }
        return crmFlow.getFlowName();
    }

    private List<String> parseArray(String content) {
        if (StrUtil.isEmpty(content)) {
            return Collections.emptyList();
        }
        try {
            return StrUtil.splitTrim(content, Const.SEPARATOR);
        } catch (JSONException exception) {
            return Collections.emptyList();
        }
    }

    private void recordToFormType(CrmFlowField record, FieldEnum typeEnum) {
        record.setFormType(typeEnum.getFormType());
        switch (typeEnum) {
            case CHECKBOX:
            case SELECT:
                record.setDefaultValue(parseArray((String) record.getDefaultValue()));
                record.setValue(parseArray((String) record.getValue()));
                if (CollUtil.isEmpty(record.getSetting())) {
                    record.setSetting(parseArray(record.getOptions()));
                }
                break;
            case DATE_INTERVAL:
                record.setDefaultValue(StrUtil.split((String) record.getDefaultValue(), Const.SEPARATOR));
                if (record.getValue() instanceof String) {
                    record.setValue(StrUtil.split((String) record.getValue(), Const.SEPARATOR));
                }
                break;
            case USER:
            case STRUCTURE:
                record.setDefaultValue(new ArrayList<>(0));
                break;
            case AREA:
            case AREA_POSITION:
            case CURRENT_POSITION:
                String defaultValue = Optional.ofNullable(record.getDefaultValue()).orElse("").toString();
                record.setDefaultValue(JSON.parse(defaultValue));
                if (record.getValue() instanceof String) {
                    String value = Optional.ofNullable(record.getValue()).orElse("").toString();
                    record.setValue(JSON.parse(value));
                }
                break;
            case DESC_TEXT:
                record.setValue(record.getDefaultValue());
                break;
            default:
                record.setSetting(Collections.emptyList());
                break;
        }
    }

}
