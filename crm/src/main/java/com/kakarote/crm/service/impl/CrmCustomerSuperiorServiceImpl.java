package com.kakarote.crm.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.PO.CrmCustomerSuperior;
import com.kakarote.crm.mapper.CrmCustomerSuperiorMapper;
import com.kakarote.crm.service.ICrmCustomerService;
import com.kakarote.crm.service.ICrmCustomerSuperiorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-07
 */
@Service
public class CrmCustomerSuperiorServiceImpl extends BaseServiceImpl<CrmCustomerSuperiorMapper, CrmCustomerSuperior> implements ICrmCustomerSuperiorService {

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Override
    public void saveAndUpdate(CrmCustomerSuperior superior) {

        CrmCustomerSuperior crmCustomerSuperior = lambdaQuery()
                .eq(CrmCustomerSuperior::getSubordinateCustomerId,superior.getSubordinateCustomerId()).one();
        if (crmCustomerSuperior != null){
            throw new CrmException(CrmCodeEnum.CRM_SUPERIOR_REPETITION_ERROR);
        }
        crmCustomerSuperior = lambdaQuery()
                .eq(CrmCustomerSuperior::getSuperiorCustomerId,superior.getSubordinateCustomerId())
                .eq(CrmCustomerSuperior::getSubordinateCustomerId,superior.getSuperiorCustomerId()).one();
        if (crmCustomerSuperior != null){
            throw new CrmException(CrmCodeEnum.CRM_SUPERIOR_REPET_ERROR);
        }
        if (superior.getSuperiorId()== null){
            superior.setCreateTime(LocalDateTimeUtil.now());
            superior.setCreateUserId(UserUtil.getUserId());
            save(superior);
        }else {
            updateById(superior);
        }

        crmCustomerService.updateEs(superior.getSubordinateCustomerId(),superior.getSuperiorCustomerId());
    }

    @Override
    public void saveAndSudate(CrmCustomerSuperior superior) {
        CrmCustomerSuperior crmCustomerSuperior = lambdaQuery()
                .eq(CrmCustomerSuperior::getSubordinateCustomerId,superior.getSubordinateCustomerId()).one();
        if (crmCustomerSuperior != null){
            throw new CrmException(CrmCodeEnum.CRM_SUPERIOR_SU_REPETITION_ERROR);
        }
        crmCustomerSuperior = lambdaQuery()
                .eq(CrmCustomerSuperior::getSuperiorCustomerId,superior.getSubordinateCustomerId())
                .eq(CrmCustomerSuperior::getSubordinateCustomerId,superior.getSuperiorCustomerId()).one();
        if (crmCustomerSuperior != null){
            throw new CrmException(CrmCodeEnum.CRM_SUPERIOR_REPET_ERROR);
        }
        if (superior.getSuperiorId()== null){
            superior.setCreateTime(LocalDateTimeUtil.now());
            superior.setCreateUserId(UserUtil.getUserId());
            save(superior);
        }else {
            updateById(superior);
        }

        crmCustomerService.updateEs(superior.getSubordinateCustomerId(),superior.getSuperiorCustomerId());
    }

    @Override
    public void deleteById(Long id) {
        removeById(id);
    }

    @Override
    public void deleteById(CrmCustomerSuperior superior) {
        CrmCustomerSuperior crmCustomerSuperior = lambdaQuery()
                .eq(CrmCustomerSuperior::getSubordinateCustomerId,superior.getSubordinateCustomerId())
                .eq(CrmCustomerSuperior::getSuperiorCustomerId,superior.getSuperiorCustomerId()).one();
        if (crmCustomerSuperior != null){
            removeById(crmCustomerSuperior.getSuperiorId());
        }
    }

    @Override
    public CrmModel querySuperiorById(Long id) {
        CrmModel crmModel = new CrmModel();
        //获取上级客户id
        List<Long> superiorCustomerIds = getBaseMapper().querySuperiorCustomerId(id);
        superiorCustomerIds.add(0L);
        CrmSearchBO search = new CrmSearchBO();
        search.setPageType(0);
        search.setLabel(CrmEnum.CUSTOMER.getType());
        CrmSearchBO.Search entity = new CrmSearchBO.Search();
        entity.setFormType(FieldEnum.TEXT.getFormType());
        entity.setSearchEnum(CrmSearchBO.FieldSearchEnum.IS);
        entity.setName("customerId");
        entity.setValues(superiorCustomerIds.stream().map(Object::toString).collect(Collectors.toList()));
        search.getSearchList().add(entity);
        BasePage<Map<String, Object>> mapBasePage = crmCustomerService.queryPageList(search);
        crmModel.put("superiorCustomer",mapBasePage.getList());
        //获取下级客户id
        List<Long> subordinateCustomerIds = getBaseMapper().querySubordinateCustomerId(id);
        subordinateCustomerIds.add(0L);
        search = new CrmSearchBO();
        search.setPageType(0);
        search.setLabel(CrmEnum.CUSTOMER.getType());
        entity = new CrmSearchBO.Search();
        entity.setFormType(FieldEnum.TEXT.getFormType());
        entity.setSearchEnum(CrmSearchBO.FieldSearchEnum.IS);
        entity.setName("customerId");
        entity.setValues(subordinateCustomerIds.stream().map(Object::toString).collect(Collectors.toList()));
        search.getSearchList().add(entity);
        mapBasePage = crmCustomerService.queryPageList(search);
        crmModel.put("subordinateCustomer",mapBasePage.getList());
        return crmModel;
    }
}
