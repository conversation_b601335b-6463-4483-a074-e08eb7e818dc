package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmBusinessSaveBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.BO.CrmUpdateInformationBO;
import com.kakarote.crm.entity.PO.CrmContract;
import com.kakarote.crm.entity.PO.CrmField;
import com.kakarote.crm.entity.PO.CrmReturnVisit;
import com.kakarote.crm.entity.PO.CrmReturnVisitData;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmReturnVisitMapper;
import com.kakarote.crm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.kakarote.core.servlet.ApplicationContextHolder.getBean;

/**
 * <p>
 * 访问服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-06
 */
@Service
public class CrmReturnVisitServiceImpl extends BaseServiceImpl<CrmReturnVisitMapper, CrmReturnVisit> implements ICrmReturnVisitService, CrmPageService {


    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmReturnVisitDataService crmReturnVisitDataService;

    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;


    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ICrmContractService crmContractService;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private FieldService fieldService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    private static final String CONTACTS_ID = "contactsId";

    private static final String CONTRACT_ID = "contractId";

    private static final String ZERO = "0";

    private static final String FIELD_TYPE = "fieldType";

    private static final int TWO = 2;

    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        return queryList(search, false);
    }

    @Override
    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<CrmReturnVisit> list = lambdaQuery().select(CrmReturnVisit::getVisitId, CrmReturnVisit::getVisitNumber).in(CrmReturnVisit::getVisitId, ids).list();
        return list.stream().map(crmReturnVisit -> {
            SimpleCrmEntity simpleCrmEntity = new SimpleCrmEntity();
            simpleCrmEntity.setId(crmReturnVisit.getVisitId());
            simpleCrmEntity.setName(crmReturnVisit.getVisitNumber());
            return simpleCrmEntity;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(CrmBusinessSaveBO crmModel) {
        CrmReturnVisit crmReturnVisit = BeanUtil.copyProperties(crmModel.getEntity(), CrmReturnVisit.class);
        String batchId = StrUtil.isNotEmpty(crmReturnVisit.getBatchId()) ? crmReturnVisit.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_return_visit_data"));
        crmReturnVisitDataService.saveData(crmModel.getField(), batchId);
        if (ObjectUtil.isNotEmpty(crmReturnVisit.getVisitId())) {
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(getById(crmReturnVisit.getVisitId())), BeanUtil.beanToMap(crmReturnVisit), CrmEnum.RETURN_VISIT, crmReturnVisit.getVisitNumber(), crmReturnVisit.getVisitId());
            crmReturnVisit.setUpdateTime(LocalDateTimeUtil.now());
            updateById(crmReturnVisit);
            crmReturnVisit = getById(crmReturnVisit.getVisitId());
        } else {
            crmReturnVisit.setBatchId(batchId);
            crmReturnVisit.setUpdateTime(LocalDateTimeUtil.now());
            crmReturnVisit.setCreateTime(LocalDateTimeUtil.now());
            if (crmReturnVisit.getOwnerUserId() == null) {
                crmReturnVisit.setOwnerUserId(UserUtil.getUserId());
            }
            final Optional<CrmModelFiledVO> modelFiledOptional = queryDefaultField().stream().filter(data -> FieldEnum.SERIAL_NUMBER.getType().equals(data.getType())).findFirst();
            if (modelFiledOptional.isPresent()) {
                Map<String, Object> map = BeanUtil.beanToMap(crmReturnVisit);
                crmModel.getField().forEach(field -> {
                    map.put(field.getFieldName(), field.getValue());
                });
                final String generateNumber = getBean(ICrmFieldNumberDataService.class).generateNumber(getLabel(), modelFiledOptional.get(), map);
                crmReturnVisit.setVisitNumber(generateNumber);
            }
            save(crmReturnVisit);
            actionRecordUtil.addRecord(crmReturnVisit.getVisitId(), CrmEnum.RETURN_VISIT, crmReturnVisit.getVisitNumber());
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmReturnVisit));
        savePage(crmModel, crmReturnVisit.getVisitId(), false);
    }


    @Override
    public void setOtherField(Map<String, Object> map) {
        String customerName = crmCustomerService.getCustomerName((Long) map.get("customerId"));
        map.put("customerName", customerName);
        if (map.containsKey(CONTACTS_ID) && ObjectUtil.isNotEmpty(map.get(CONTACTS_ID))) {
            String contactsName = crmContactsService.getContactsName((Long) map.get("contactsId"));
            map.put("contactsName", contactsName);
        } else {
            map.put("contactsName", "");
        }
        CrmContract contract = crmContractService.getById((Serializable) map.get("contractId"));
        map.put("contractNum", contract != null ? contract.getNum() : "");
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
    }

    @Override
    public Dict getSearchTransferMap() {
        return Dict.create()
                .set("customerId", "customerName").set("contractId", "contractNum").set("contactsId", "contactsName");
    }

    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryById(id);
            crmModel.setLabel(CrmEnum.RETURN_VISIT.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            crmReturnVisitDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
        } else {
            crmModel = new CrmModel(CrmEnum.RETURN_VISIT.getType());
        }
        return crmModel;
    }

    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = this.supplementCrmModel(id);
        List<CrmModelFiledVO> crmModelFiledVOS = crmFieldService.queryField(crmModel);
        if (id == null) {
            String ownerUserId = "ownerUserId";
            crmModelFiledVOS.forEach(field -> {
                if (ownerUserId.equals(field.getFieldName())) {
                    SimpleUser user = new SimpleUser();
                    user.setUserId(UserUtil.getUserId());
                    user.setRealname(UserUtil.getUser().getRealname());
                    field.setDefaultValue(Collections.singleton(user));
                }
            });
        }
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            crmModelFiledVOS.addAll(modelFiledVOS);
        }
        return crmModelFiledVOS;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = this.supplementCrmModel(id);
        if (ObjectUtil.isNotEmpty(id)) {
            // 去除编辑掩码
            crmModel.put("update", true);
        }
        List<List<CrmModelFiledVO>> crmModelFiledVOS = crmFieldService.queryFormPositionFieldVO(crmModel);
        if (id == null) {
            for (List<CrmModelFiledVO> filedVOList : crmModelFiledVOS) {
                filedVOList.forEach(field -> {
                    if ("ownerUserId".equals(field.getFieldName())) {
                        SimpleUser user = new SimpleUser();
                        user.setUserId(UserUtil.getUserId());
                        user.setRealname(UserUtil.getUser().getRealname());
                        field.setDefaultValue(Collections.singleton(user));
                    }
                });
            }
        }
        return crmModelFiledVOS;
    }

    private CrmModel supplementCrmModel(Long id) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            String customerId = "customerId";
            if (crmModel.get(customerId) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            List<JSONObject> contractList = new ArrayList<>();
            JSONObject contract = new JSONObject();
            if (crmModel.get(CONTRACT_ID) != null && !ZERO.equals(crmModel.get(CONTRACT_ID).toString())) {
                contractList.add(contract.fluentPut("contractId", crmModel.get("contractId")).fluentPut("contractNum", crmModel.get("contractNum")));
                crmModel.put("contractId", contractList);
            }
            List<JSONObject> contactsList = new ArrayList<>();
            JSONObject contacts = new JSONObject();
            if (crmModel.get(CONTACTS_ID) != null && !ZERO.equals(crmModel.get(CONTACTS_ID).toString())) {
                contactsList.add(contacts.fluentPut("contactsId", crmModel.get("contactsId")).fluentPut("name", crmModel.get("contactsName")));
                crmModel.put("contactsId", contactsList);
            }
        }
        return crmModel;
    }

    @Override
    public List<CrmModelFiledVO> information(Long visitId) {
        return queryField(visitId, true);
    }

    @Override
    public List<FileEntity> queryFileList(Long id) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmReturnVisit crmReturnVisit = getById(id);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmReturnVisit.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmReturnVisitData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmReturnVisitData::getValue);
            wrapper.eq(CrmReturnVisitData::getBatchId, crmReturnVisit.getBatchId());
            wrapper.in(CrmReturnVisitData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = fileService.queryFileList(crmReturnVisitDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("回访详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        return fileEntityList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        LambdaQueryWrapper<CrmReturnVisit> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CrmReturnVisit::getVisitId, ids);
        List<String> batchList = listObjs(wrapper, Object::toString);
        //删除自定义字段
        crmReturnVisitDataService.deleteByBatchId(batchList);
        //删除字段操作记录
        crmActionRecordService.deleteActionRecord(CrmEnum.RETURN_VISIT, ids);

        removeByIds(ids);

        deletePage(ids);
    }

    @Override
    public void updateReturnVisitRemindConfig(Integer status, Integer value) {
        AdminConfig adminConfig = adminService.queryFirstConfigByName("returnVisitRemindConfig").getData();
        if (adminConfig == null) {
            adminConfig = new AdminConfig();
        }
        adminConfig.setStatus(status);
        adminConfig.setValue(value != null ? value.toString() : "7");
        adminConfig.setName("returnVisitRemindConfig");
        adminConfig.setDescription("客户回访提醒设置");
        adminService.updateAdminConfig(adminConfig);
    }

    @Override
    public AdminConfig queryReturnVisitRemindConfig() {
        AdminConfig adminConfig = adminService.queryFirstConfigByName("returnVisitRemindConfig").getData();
        if (adminConfig == null) {
            adminConfig = new AdminConfig();
            adminConfig.setStatus(0);
            adminConfig.setValue("7");
            adminConfig.setName("returnVisitRemindConfig");
            adminConfig.setDescription("客户回访提醒设置");
        }
        return adminConfig;
    }

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"visitNumber"};
    }

    /**
     * 获取crm列表类型
     *
     * @return data
     */
    @Override
    public CrmEnum getLabel() {
        return CrmEnum.RETURN_VISIT;
    }

    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        return filedList;
    }


    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long visitId = updateInformationBO.getId();
        updateInformationBO.getList().forEach(record -> {
            CrmReturnVisit oldReturnVisit = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldReturnVisitMap = BeanUtil.beanToMap(oldReturnVisit);
            if (record.getInteger(FIELD_TYPE) == 1) {
                Map<String, Object> crmRetuenVisitMap = new HashMap<>(oldReturnVisitMap);
                crmRetuenVisitMap.put(record.getString("fieldName"), record.get("value"));
                CrmReturnVisit crmReturnVisit = BeanUtil.mapToBean(crmRetuenVisitMap, CrmReturnVisit.class, true);
                actionRecordUtil.updateRecord(oldReturnVisitMap, crmRetuenVisitMap, CrmEnum.RETURN_VISIT, crmReturnVisit.getVisitNumber(), crmReturnVisit.getVisitId());
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("visit_id", updateInformationBO.getId()).update();
            } else if (record.getInteger(FIELD_TYPE) == 0 || record.getInteger(FIELD_TYPE) == TWO) {
                CrmReturnVisitData returnVisitData = crmReturnVisitDataService.lambdaQuery().select(CrmReturnVisitData::getValue, CrmReturnVisitData::getId).eq(CrmReturnVisitData::getFieldId, record.getLong("fieldId"))
                        .eq(CrmReturnVisitData::getBatchId, batchId).one();
                String value = returnVisitData != null ? returnVisitData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.RETURN_VISIT, BehaviorEnum.UPDATE, visitId, oldReturnVisit.getVisitNumber(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmReturnVisitData crmReturnVisitData = new CrmReturnVisitData();
                crmReturnVisitData.setId(returnVisitData != null ? returnVisitData.getId() : null);
                crmReturnVisitData.setFieldId(record.getLong("fieldId"));
                crmReturnVisitData.setName(record.getString("fieldName"));
                crmReturnVisitData.setValue(newValue);
                crmReturnVisitData.setCreateTime(LocalDateTimeUtil.now());
                crmReturnVisitData.setBatchId(batchId);
                crmReturnVisitDataService.saveOrUpdate(crmReturnVisitData);

            }
            updateField(record, visitId);
        });
        this.lambdaUpdate().set(CrmReturnVisit::getUpdateTime, new Date()).eq(CrmReturnVisit::getVisitId, visitId).update();
    }

}
