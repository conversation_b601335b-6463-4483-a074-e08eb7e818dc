package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.examine.entity.*;
import com.kakarote.core.feign.examine.service.ExamineService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.entity.BO.CrmFlowSettingBO;
import com.kakarote.crm.entity.PO.CrmFlowSetting;
import com.kakarote.crm.entity.VO.CrmFlowSettingVO;
import com.kakarote.crm.mapper.CrmFlowSettingMapper;
import com.kakarote.crm.service.ICrmFlowFieldService;
import com.kakarote.crm.service.ICrmFlowSettingService;
import com.kakarote.crm.service.ICrmFlowTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 阶段流程详细配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Service
public class CrmFlowSettingServiceImpl extends BaseServiceImpl<CrmFlowSettingMapper, CrmFlowSetting> implements ICrmFlowSettingService {

    @Autowired
    private ICrmFlowFieldService crmFlowFieldService;

    @Autowired
    private ICrmFlowTaskService crmFlowTaskService;

    @Autowired
    @Lazy
    private ExamineService examineService;

    @Override
    public void saveFlowSetting(Long flowId, List<CrmFlowSettingBO> settingList) {
        int i = 0;
        for (CrmFlowSettingBO crmFlowSettingBO : settingList) {
            CrmFlowSetting crmFlowSetting = BeanUtil.copyProperties(crmFlowSettingBO, CrmFlowSetting.class);
            crmFlowSetting.setOpenForm(crmFlowSettingBO.getFormList().isEmpty() ? 0 : 1);
            crmFlowSetting.setOpenTask(crmFlowSettingBO.getTaskList().isEmpty() ? 0 : 1);
            crmFlowSetting.setFlowId(flowId);
            if (crmFlowSettingBO.getExamineSaveBO() == null) {
                crmFlowSetting.setExamineId(0L);
            }
            crmFlowSetting.setOrderNum(i++);
            save(crmFlowSetting);
            Map<String, String> fieldMap = crmFlowFieldService.saveFlowField(crmFlowSetting.getSettingId(), crmFlowSettingBO.getFormList());
            crmFlowTaskService.saveFlowTask(crmFlowSetting.getSettingId(), crmFlowSettingBO.getTaskList());
            if (crmFlowSettingBO.getExamineSaveBO() != null) {
                crmFlowSettingBO.getExamineSaveBO().setLabel(20);
                //因为前端传值时没有保存fieldName,手动setFieldName
                setExamineFieldName(crmFlowSettingBO.getExamineSaveBO().getDataList(), fieldMap);
                crmFlowSettingBO.getExamineSaveBO().setExamineName("阶段流程审批" + flowId + ":" + i);
                Result<ExamineInfoVo> infoVoResult = examineService.addExamine(crmFlowSettingBO.getExamineSaveBO());
                crmFlowSetting.setExamineId(infoVoResult.getData().getExamineId());
                lambdaUpdate().set(CrmFlowSetting::getExamineId, crmFlowSetting.getExamineId()).eq(CrmFlowSetting::getSettingId, crmFlowSetting.getSettingId()).update();
            }
        }
    }

    /**
     * 设置fieldName
     *
     * @param dataSaveBOList 审批对象
     * @param fieldMap       字段map
     */
    private void setExamineFieldName(List<ExamineDataSaveBO> dataSaveBOList, Map<String, String> fieldMap) {
        for (ExamineDataSaveBO examineDataSaveBO : dataSaveBOList) {
            if (CollUtil.isNotEmpty(examineDataSaveBO.getConditionList())) {
                for (ExamineConditionSaveBO examineConditionSaveBO : examineDataSaveBO.getConditionList()) {
                    for (ExamineConditionDataSaveBO examineConditionDataSaveBO : examineConditionSaveBO.getConditionDataList()) {
                        examineConditionDataSaveBO.setFieldName(fieldMap.get(examineConditionDataSaveBO.getName()));
                    }
                    //判断条件审批下是否还存在下级
                    setExamineFieldName(examineConditionSaveBO.getExamineDataList(), fieldMap);
                }
            }
        }
    }

    /**
     * 查询阶段流程设置
     *
     * @param flowId 阶段ID
     * @return 阶段配置列表
     */
    @Override
    public List<CrmFlowSettingVO> queryFlowSettingList(Long flowId, boolean queryExamine) {
        List<CrmFlowSetting> list = lambdaQuery().eq(CrmFlowSetting::getFlowId, flowId).orderByAsc(CrmFlowSetting::getOrderNum).list();
        return list.stream().map(crmFlowSetting -> {
            CrmFlowSettingVO crmFlowSettingVO = new CrmFlowSettingVO();
            crmFlowSettingVO.setName(crmFlowSetting.getName());
            crmFlowSettingVO.setOrderNum(crmFlowSetting.getOrderNum());
            crmFlowSettingVO.setRate(crmFlowSetting.getRate());
            crmFlowSettingVO.setSettingId(crmFlowSetting.getSettingId());
            crmFlowSettingVO.setExamineId(crmFlowSetting.getExamineId());
            if (0 != crmFlowSetting.getExamineId()) {
                ExaminePreviewVO data = examineService.queryExamineFlowInfo(crmFlowSetting.getExamineId()).getData();
                crmFlowSettingVO.setExaminePreviewList(data);
            }
            if (1 == crmFlowSetting.getOpenForm()) {
                crmFlowSettingVO.setFormList(crmFlowFieldService.queryFieldList(crmFlowSetting.getSettingId(),queryExamine));
            }
            if (1 == crmFlowSetting.getOpenTask()) {
                crmFlowSettingVO.setTaskList(crmFlowTaskService.queryTaskList(crmFlowSetting.getSettingId()));
            }
            return crmFlowSettingVO;
        }).collect(Collectors.toList());
    }
}
