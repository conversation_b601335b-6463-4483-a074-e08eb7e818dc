package com.kakarote.crm.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.entity.PO.CrmFlowTask;
import com.kakarote.crm.mapper.CrmFlowTaskMapper;
import com.kakarote.crm.service.ICrmFlowTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 阶段工作配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Service
public class CrmFlowTaskServiceImpl extends BaseServiceImpl<CrmFlowTaskMapper, CrmFlowTask> implements ICrmFlowTaskService {

    /**
     * 保存阶段流程任务信息
     *
     * @param settingId settingId
     * @param taskList  任务列表
     */
    @Override
    public void saveFlowTask(Long settingId, List<CrmFlowTask> taskList) {
        if (taskList.isEmpty()) {
            return;
        }
        int i = 0;
        for (CrmFlowTask crmFlowTask : taskList) {
            crmFlowTask.setId(null);
            crmFlowTask.setSettingId(settingId);
            crmFlowTask.setSort(i++);
        }
        saveBatch(taskList);
    }

    /**
     * 查询阶段流程任务信息
     *
     * @param settingId settingId
     * @return data
     */
    @Override
    public List<CrmFlowTask> queryTaskList(Long settingId) {
        return lambdaQuery().eq(CrmFlowTask::getSettingId, settingId).orderByAsc(CrmFlowTask::getSort).list();
    }
}
