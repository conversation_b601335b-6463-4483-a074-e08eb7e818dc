package com.kakarote.crm.service;

import com.kakarote.core.feign.crm.entity.SimpleCrmInfo;
import com.kakarote.core.feign.examine.entity.ExamineConditionDataBO;
import com.kakarote.core.feign.examine.entity.ExamineMessageBO;

import java.util.Map;

/**
 * <p>
 * 审核记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface ICrmExamineRecordService {

    /**
     * 更新合同回款金额
     * @param id id
     */
    public void updateContractMoney(Long id);


    /**
     * 自定义审批添加消息
     * @param categoryType categoryType
     * @param examineType  1 待审核 2 通过 3 拒绝
     */
    public void addMessageForNewExamine(Integer categoryType, Integer examineType, Object examineObj, Long ownerUserId);

    public void addMessageForNewExamine(ExamineMessageBO examineMessageBO);

    public Map<String, Object> getDataMapForNewExamine(ExamineConditionDataBO examineConditionDataBO);

    public Boolean updateCheckStatusByNewExamine(ExamineConditionDataBO examineConditionDataBO);


    SimpleCrmInfo getCrmSimpleInfo(ExamineConditionDataBO examineConditionDataBO);
}
