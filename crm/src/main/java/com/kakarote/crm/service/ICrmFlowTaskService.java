package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmFlowTask;

import java.util.List;

/**
 * <p>
 * 阶段工作配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface ICrmFlowTaskService extends BaseService<CrmFlowTask> {

    /**
     * 保存阶段流程任务信息
     * @param settingId settingId
     * @param taskList 任务列表
     */
    public void saveFlowTask(Long settingId, List<CrmFlowTask> taskList);

    /**
     * 查询阶段流程任务信息
     * @param settingId settingId
     * @return data
     */
    public List<CrmFlowTask> queryTaskList(Long settingId);
}
