package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.DataAuthEnum;
import com.kakarote.core.common.enums.DateFilterEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.PageEntity;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.feign.crm.entity.ActivityContent;
import com.kakarote.core.feign.crm.entity.CrmActivityBO;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.BaseUtil;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.constant.*;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmActivitySaveBO;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import com.kakarote.crm.mapper.CrmActivityMapper;
import com.kakarote.crm.mapper.CrmAuthMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * crm活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
@Slf4j
@Service
public class CrmActivityServiceImpl extends BaseServiceImpl<CrmActivityMapper, CrmActivity> implements ICrmActivityService {

    @Autowired
    private ICrmBackLogDealService crmBackLogDealService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    @Lazy
    private AdminFileService adminFileService;

    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    private ICrmActivityRelationService activityRelationService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ICrmBusinessService crmBusinessService;

    @Autowired
    private ICrmContractService crmContractService;

    @Autowired
    private ICrmReceivablesService crmReceivablesService;

    @Autowired
    private ICrmReceivablesPlanService crmReceivablesPlanService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ICrmLeadsService crmLeadsService;

    @Autowired
    private CrmAuthMapper authMapper;

    private static final int TWO = 2;
    private static final int THREE = 3;
    private static final int FIVE = 5;
    private static final int SIX = 6;


    /**
     * 删除活动记录
     *
     * @param ids 主键列表
     */
    @Override
    public void deleteActivityRecord(List<Long> ids) {
        lambdaUpdate().in(CrmActivity::getActivityTypeId, ids).remove();
    }

    /**
     * 新增活动记录
     *
     * @param type           type
     * @param activityEnum   类型
     * @param activityTypeId 类型ID
     */
    @Override
    public void addActivity(Integer type, CrmActivityEnum activityEnum, Long activityTypeId) {
        String content = "创建了" + activityEnum.getRemarks() + ": ";
        CrmActivity crmActivity = new CrmActivity();
        crmActivity.setType(type);
        crmActivity.setActivityType(activityEnum.getType());
        crmActivity.setActivityTypeId(activityTypeId);
        crmActivity.setActivityId(activityTypeId);
        crmActivity.setContent(content);
        crmActivity.setCreateUserId(UserUtil.getUserId());
        crmActivity.setCreateTime(LocalDateTimeUtil.now());

        JSONObject jsonObject = new JSONObject();
        //进行冗余数据处理
        CrmActivityBO crmActivityBO = BeanUtil.copyProperties(crmActivity, CrmActivityBO.class);
        addActivity(activityEnum, activityTypeId, new JSONObject(), crmActivityBO, crmActivity);
        save(crmActivity);
    }

    @Override
    public void addRelationActivity(CrmActivityBO crmActivityBO) {
        Integer activityType = crmActivityBO.getActivityType();
        CrmActivityEnum activityEnum = CrmActivityEnum.parse(activityType);
        crmActivityBO.setCreateTime(LocalDateTimeUtil.now());
        crmActivityBO.setCreateUserId(UserUtil.getUserId());

        JSONObject jsonObject = new JSONObject();
        //进行冗余数据处理
        addActivity(activityEnum, crmActivityBO.getActivityTypeId(), new JSONObject(), crmActivityBO, null);
    }

    /**
     * 新增变更活动记录
     *
     * @param type           type
     * @param activityEnum   类型
     * @param activityTypeId 类型ID
     */
    @Override
    public void addActivity(Integer type, CrmActivityEnum activityEnum, Long activityTypeId, String newStr, String oldStr) {
        String content = oldStr + "变更为" + newStr;
        CrmActivity crmActivity = new CrmActivity();
        crmActivity.setType(type);
        crmActivity.setActivityType(activityEnum.getType());
        crmActivity.setActivityTypeId(activityTypeId);
        crmActivity.setActivityId(activityTypeId);
        crmActivity.setContent(content);
        crmActivity.setCreateUserId(UserUtil.getUserId());
        crmActivity.setCreateTime(LocalDateTimeUtil.now());
        save(crmActivity);
    }

    private void addActivity(CrmActivityEnum activityEnum, Long activityTypeId, JSONObject jsonObject, CrmActivityBO crmActivityBO, CrmActivity crmActivity) {
        CrmActivityEnum parse = CrmActivityEnum.parse(activityEnum.getType());

        //添加冗余数据
        CrmActivity activity = BeanUtil.copyProperties(crmActivityBO, CrmActivity.class);
        ActivityContent activityContent = new ActivityContent();
        ActivityContent content = new ActivityContent();
        switch (parse) {
            case CUSTOMER:
                CrmCustomer crmCustomer = crmCustomerService.getById(activityTypeId);
                content = new ActivityContent(crmCustomer.getCustomerId(), crmCustomer.getCustomerName(), CrmActivityEnum.CUSTOMER.getType());
                crmActivity.setContent(JSONObject.toJSONString(content));
                break;

            case CONTACTS:
                CrmContacts crmContacts = crmContactsService.getById(activityTypeId);

                //处理本体数据
                content = new ActivityContent(crmContacts.getContactsId(), crmContacts.getName(), CrmActivityEnum.CONTACTS.getType());
                content.setMobile(crmContacts.getMobile());
                content.setPost(crmContacts.getPost());
                crmActivity.setContent(JSONObject.toJSONString(content));

                //处理冗余数据
                activity.setActivityType(CrmActivityEnum.CONTACTS.getType());
                activity.setActivityId(crmContacts.getCustomerId());
                activityContent = new ActivityContent(crmContacts.getContactsId(), crmContacts.getName(), CrmActivityEnum.CONTACTS.getType());
                activityContent.setMobile(crmContacts.getMobile());
                activityContent.setPost(crmContacts.getPost());

                activity.setContent(JSONObject.toJSONString(activityContent));
                save(activity);
                break;

            case CONTRACT:
                CrmContract crmContract = crmContractService.getById(activityTypeId);

                content = new ActivityContent(crmContract.getContractId(), crmContract.getName(), CrmActivityEnum.CONTRACT.getType());
                content.setNum(crmContract.getNum());
                content.setMoney(crmContract.getMoney());
                content.setCheckStatus(crmContract.getCheckStatus());
                content.setStartTime(LocalDateTimeUtil.formatNormal(crmContract.getStartTime()));
                content.setEndTime(LocalDateTimeUtil.formatNormal(crmContract.getEndTime()));
                crmActivity.setContent(JSONObject.toJSONString(content));

                activity.setActivityType(CrmActivityEnum.CONTRACT.getType());
                activity.setActivityId(crmContract.getCustomerId());
                activityContent = new ActivityContent(crmContract.getContractId(), crmContract.getName(), CrmActivityEnum.CONTRACT.getType());
                activityContent.setNum(crmContract.getNum());
                activityContent.setMoney(crmContract.getMoney());
                activityContent.setCheckStatus(crmContract.getCheckStatus());
                activityContent.setStartTime(LocalDateTimeUtil.formatNormal(crmContract.getStartTime()));
                activityContent.setEndTime(LocalDateTimeUtil.formatNormal(crmContract.getEndTime()));

                activity.setContent(JSONObject.toJSONString(activityContent));
                save(activity);
                break;

            case BUSINESS:
                CrmBusiness crmBusiness = crmBusinessService.getById(activityTypeId);

                //处理本体数据
                content = new ActivityContent(crmBusiness.getBusinessId(), crmBusiness.getBusinessName(), CrmActivityEnum.BUSINESS.getType());
                content.setMoney(crmBusiness.getMoney());
                content.setFlowName(ApplicationContextHolder.getBean(ICrmFlowService.class).getById(crmBusiness.getTypeId()).getFlowName());
                crmActivity.setContent(JSONObject.toJSONString(content));


                activity.setActivityType(CrmActivityEnum.BUSINESS.getType());
                activity.setActivityId(crmBusiness.getCustomerId());

                //查看商机关联的产品，供活动详情查看
                List<CrmBusinessProduct> businessProducts = ApplicationContextHolder.getBean(ICrmBusinessProductService.class).lambdaQuery()
                        .eq(CrmBusinessProduct::getBusinessId, crmBusiness.getBusinessId()).list();
                List<JSONObject> products = new ArrayList<>();
                if (CollectionUtil.isEmpty(businessProducts)) {
                    products.addAll(businessProducts.stream().map(businessProduct -> new JSONObject().fluentPut("id", businessProduct.getBusinessId())
                            .fluentPut("name", businessProduct.getName())).collect(Collectors.toList()));
                }

                activityContent = new ActivityContent(crmBusiness.getBusinessId(), crmBusiness.getBusinessName(), CrmActivityEnum.BUSINESS.getType());
                activityContent.setMoney(crmBusiness.getMoney());
                activityContent.setFlowName(ApplicationContextHolder.getBean(ICrmFlowService.class).getById(crmBusiness.getTypeId()).getFlowName());
                activity.setContent(JSONObject.toJSONString(activityContent));

                save(activity);
                break;

            case RECEIVABLES:
                CrmReceivables crmReceivables = crmReceivablesService.getById(activityTypeId);

                //处理本体数据
                content = new ActivityContent(crmReceivables.getReceivablesId(), crmReceivables.getNumber(), CrmActivityEnum.RECEIVABLES.getType());
                content.setCheckStatus(crmReceivables.getCheckStatus());
                crmActivity.setContent(JSONObject.toJSONString(content));

                //处理本体数据
                activity.setActivityType(CrmActivityEnum.RECEIVABLES.getType());
                activity.setActivityId(crmReceivables.getCustomerId());

                //获得回款合同
                CrmContract contract = crmContractService.getById(crmReceivables.getContractId());

                activityContent = new ActivityContent(crmReceivables.getReceivablesId(), crmReceivables.getNumber(), CrmActivityEnum.RECEIVABLES.getType());
                activityContent.setCheckStatus(crmReceivables.getCheckStatus());

                JSONObject json = BeanUtil.copyProperties(activityContent, JSONObject.class);

                //添加合同数据、汇款金额  回款时间
                json.fluentPut("contractId", contract.getContractId()).fluentPut("receivablesMoney", crmReceivables.getMoney()).fluentPut("returnTime", LocalDateTimeUtil.format(crmReceivables.getReturnTime(), "yyyy-MM-dd"));

                activity.setContent(json.toJSONString());
                save(activity);
                break;
            case RECEIVABLES_PLAN:
                CrmReceivablesPlan crmReceivablesPlan = crmReceivablesPlanService.getById(activityTypeId);
                //获得回款合同
                CrmContract planContract = crmContractService.getById(crmReceivablesPlan.getContractId());

                //处理本体数据
                content = new ActivityContent(crmReceivablesPlan.getReceivablesId(), crmReceivablesPlan.getNum(), CrmActivityEnum.RECEIVABLES_PLAN.getType());
                content.setNum(planContract.getNum());
                content.setMoney(crmReceivablesPlan.getMoney());

                activity.setActivityType(CrmActivityEnum.RECEIVABLES_PLAN.getType());
                activity.setActivityId(crmReceivablesPlan.getCustomerId());

                activityContent = new ActivityContent(crmReceivablesPlan.getReceivablesId(), crmReceivablesPlan.getNum(), CrmActivityEnum.RECEIVABLES_PLAN.getType());
                activityContent.setNum(planContract.getNum());
                activityContent.setMoney(crmReceivablesPlan.getMoney());

                JSONObject planJson = BeanUtil.copyProperties(activityContent, JSONObject.class);
                //计划回款日期
                planJson.fluentPut("returnDate", crmReceivablesPlan.getRealReturnDate()).fluentPut("contractId", crmReceivablesPlan.getContractId());
                activity.setContent(planJson.toJSONString());
                //保存客户冗余数据
                save(activity);

                //处理合同冗余
                CrmActivity contractActivity = BeanUtil.copyProperties(activity, CrmActivity.class);
                contractActivity.setId(null);
                contractActivity.setActivityType(CrmActivityEnum.RECEIVABLES_PLAN.getType());
                contractActivity.setActivityId(planContract.getContractId());
                //保存合同冗余数据
                save(contractActivity);
                break;
            case EVENT:
            case TASK:
            case LOG:
            case EXAMINE:

                //活动记录
                List<CrmActivity> crmActivities = new ArrayList<>();

                //活动关联业务
                List<CrmActivityRelation> crmActivityRelations = new ArrayList<>();

                if (!CollectionUtil.isEmpty(crmActivityBO.getBusinessIds())) {
                    //处理活动记录
                    handleCrmActivity(crmActivityBO.getBusinessIds(), parse, crmActivityBO.getType(), crmActivityBO, crmActivities, crmActivityRelations);
                }
                if (!CollectionUtil.isEmpty(crmActivityBO.getContactsIds())) {
                    handleCrmActivity(crmActivityBO.getContactsIds(), parse, crmActivityBO.getType(), crmActivityBO, crmActivities, crmActivityRelations);
                }
                if (!CollectionUtil.isEmpty(crmActivityBO.getContractIds())) {
                    handleCrmActivity(crmActivityBO.getContractIds(), parse, crmActivityBO.getType(), crmActivityBO, crmActivities, crmActivityRelations);
                }
                if (!CollectionUtil.isEmpty(crmActivityBO.getCustomerIds())) {
                    handleCrmActivity(crmActivityBO.getCustomerIds(), parse, crmActivityBO.getType(), crmActivityBO, crmActivities, crmActivityRelations);
                }
                saveBatch(crmActivities, crmActivities.size());
                activityRelationService.saveBatch(crmActivityRelations, crmActivityRelations.size());
                break;
        }
    }

    /**
     * 批量处理活动记录
     *
     * @param ids             反推数据ID
     * @param crmActivityEnum 活动模块类型
     * @param type            活动类型
     * @param crmActivityBO   要处理的OA日志或者审批数据
     * @param crmActivities   保存列表
     */
    private void handleCrmActivity(List<Long> ids, CrmActivityEnum crmActivityEnum, Integer type, CrmActivityBO crmActivityBO, List<CrmActivity> crmActivities, List<CrmActivityRelation> crmActivityRelations) {
        ids.forEach(id -> {
            Long nextId = BaseUtil.getNextId();
            crmActivities.add(new CrmActivity(
                    nextId,
                    type,
                    id,
                    crmActivityEnum.getType(),
                    crmActivityBO.getActivityTypeId(),
                    JSONObject.toJSONString(crmActivityBO.getActivityContent()),
                    UserUtil.getUserId(),
                    LocalDateTime.now(),
                    1));

            crmActivityRelations.add(new CrmActivityRelation(nextId, type, id));
        });
    }

    @Override
    public BasePage<CrmActivityVO> getCrmActivityPageList(CrmActivityQueryBO crmActivityQueryBO) {
        if (crmActivityQueryBO.getDataType() == DataAuthEnum.CUSTOM && CollectionUtil.isEmpty(crmActivityQueryBO.getUserList())) {
            crmActivityQueryBO.setDataType(DataAuthEnum.ALL);
        }
        //查询当前查询用户信息列表
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(crmActivityQueryBO, CrmAuthEnum.READ.getMenuId(null));
        if (!crmActivityQueryBO.getIsOa() && (crmActivityQueryBO.getDateFilter() == null || crmActivityQueryBO.getDateFilter() == DateFilterEnum.MONTH)) {
            timeEntity.setBeginDate(null);
            timeEntity.setEndDate(null);
        }
        if (crmActivityQueryBO.getDataType() == DataAuthEnum.MYSELF_AND_SUBORDINATE) {
            timeEntity.getUserIds().removeIf(userId -> userId.equals(UserUtil.getUserId()));
            if (timeEntity.getUserIds().isEmpty()) {
                return new BasePage<>();
            }
        }
        CrmEnum crmEnum = CrmEnum.parse(crmActivityQueryBO.getCrmType());
        if (crmEnum != CrmEnum.NULL && Objects.equals(Boolean.FALSE,crmActivityQueryBO.getIsOa())) {
            Map<String,Object> dataMap = new HashMap<>(6,1.0F);
            dataMap.put("tableName",crmEnum.getTableName());
            dataMap.put("primaryKey",crmEnum.getPrimaryKey(false));
            dataMap.put("id",crmActivityQueryBO.getActivityTypeId());
            dataMap.put("ownerUserId",UserUtil.getUserId());
            dataMap.put("type",crmEnum.getType());
            Integer recordAuth = authMapper.queryRecordAuth(dataMap);
            //当前数据的负责人或者当前数据的团队成员默认可查询全部，不再限制权限
            if (recordAuth > 0) {
                timeEntity.setUserIds(null);
            }
        }
        BasePage<CrmActivityVO> recordList = getBaseMapper().getCrmActivityPageList(crmActivityQueryBO.parse(), timeEntity, crmActivityQueryBO);
        recordList.getList().forEach(this::buildActivityRelation);
        return recordList;
    }

    /**
     * 添加活动记录
     *
     * @param crmActivityBO crmActivityBO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCrmActivityRecord(CrmActivitySaveBO crmActivityBO) {
        if (crmActivityBO.getNextTime() != null && crmActivityBO.getNextTime().isBefore(LocalDateTime.now())) {
            throw new CrmException(CrmCodeEnum.CRM_NEXT_TIME_ERROR);
        }
        UserInfo user = UserUtil.getUser();
        BaseUtil.getRedis().del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + user.getUserId().toString());
        saveActivity(crmActivityBO, user);
        updateNextTime(crmActivityBO, true);
        //删除已忽略的待办事项，因为更新时间已更新
        crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.TODAY_CUSTOMER, crmActivityBO.getActivityTypeId());
        crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.FOLLOW_LEADS, crmActivityBO.getActivityTypeId());
        crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.FOLLOW_CUSTOMER, crmActivityBO.getActivityTypeId());
        crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.TO_ENTER_CUSTOMER_POOL, crmActivityBO.getActivityTypeId());
        //添加操作记录
        actionRecordUtil.addFollowupActionRecord(crmActivityBO.getActivityType(), crmActivityBO.getActivityTypeId(), "");
    }

    /**
     * 查询文件batchId
     *
     * @param id           id
     * @param activityType 类型
     * @return data
     */
    @Override
    public List<String> queryFileBatchId(Long id, Integer activityType) {
        LambdaQueryWrapper<CrmActivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CrmActivity::getBatchId);
        wrapper.eq(CrmActivity::getActivityType, activityType);
        wrapper.eq(CrmActivity::getActivityTypeId, id);
        return listObjs(wrapper, Object::toString);
    }

    /**
     * 更新下次联系时间
     *
     * @param crmActivity activity
     * @param isDel 是否将当前数据重置为未跟进 false为重置
     */
    private void updateNextTime(CrmActivitySaveBO crmActivity, boolean isDel) {
        CrmEnum crmEnum = CrmEnum.parse(crmActivity.getActivityType());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("followup", isDel ? 1 : 0);
        dataMap.put("label", crmEnum.getType());
        dataMap.put("primaryKey", crmEnum.getPrimaryKey(false));
        dataMap.put("tableId", crmActivity.getActivityTypeId());
        dataMap.put("tableName", crmEnum.getTableName());
        dataMap.put("idDel", isDel);
        dataMap.put("lastContent", crmActivity.getContent());
        dataMap.put("nextTime", crmActivity.getNextTime());
        dataMap.put("lastTime", LocalDateTime.now());
        dataMap.put("updateTime", LocalDateTime.now());
        //更新下次联系时间数据
        getBaseMapper().updateNextTime(dataMap);
        //todo es更新功能未实现，放到了canal中
        List<CrmActivityRelation> activityRelationList = new ArrayList<>();
        if (CollUtil.isNotEmpty(crmActivity.getContactsIds())) {
            for (Long contactsId : crmActivity.getContactsIds()) {
                CrmActivityRelation activityRelation = new CrmActivityRelation();
                activityRelation.setActivityId(crmActivity.getId());
                activityRelation.setType(CrmEnum.CONTACTS.getType());
                activityRelation.setTypeId(contactsId);
                activityRelation.setActivityId(crmActivity.getId());
                activityRelationList.add(activityRelation);
            }
            dataMap.put("ids", crmActivity.getContactsIds());
            getBaseMapper().updateRelationNextTime(dataMap);
        }
        if (CollUtil.isNotEmpty(crmActivity.getBusinessIds())) {
            for (Long businessId : crmActivity.getBusinessIds()) {
                CrmActivityRelation activityRelation = new CrmActivityRelation();
                activityRelation.setActivityId(crmActivity.getId());
                activityRelation.setType(CrmEnum.BUSINESS.getType());
                activityRelation.setTypeId(businessId);
                activityRelation.setActivityId(crmActivity.getId());
                activityRelationList.add(activityRelation);
            }
            dataMap.put("ids", crmActivity.getBusinessIds());
            getBaseMapper().updateRelationNextTime(dataMap);
        }
        //因为批量保存底层已经有为空判断，这块不再额外判断
        activityRelationService.saveBatch(activityRelationList, Const.BATCH_SAVE_SIZE);

        //更新es
        String index = crmEnum.getIndex();
        UpdateRequest updateRequest = new UpdateRequest(index, "_doc", crmActivity.getActivityTypeId().toString());
        dataMap.put("lastTime", LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        dataMap.put("updateTime", LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        dataMap.put("nextTime", LocalDateTimeUtil.formatNormal(crmActivity.getNextTime()));
        updateRequest.doc(dataMap);
        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            restHighLevelClient.indices().refresh(new RefreshRequest(index), RequestOptions.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void buildActivityRelation(CrmActivityVO record) {

        //获得本体数据名称
        if (record.getActivityId().equals(record.getActivityTypeId())) {
            CrmEnum parse = CrmEnum.parse(record.getActivityType() == CrmActivityEnum.RECEIVABLES_PLAN.getType() ? CrmActivityEnum.RECEIVABLES_PLAN.getType() : record.getActivityType());
            Long activityTypeId = record.getActivityTypeId();
            switch (parse) {
                case LEADS:
                    record.setActivityTypeName(crmLeadsService.getById(activityTypeId).getLeadsName());
                    break;
                case CUSTOMER:
                    record.setActivityTypeName(crmCustomerService.getById(activityTypeId).getCustomerName());
                    break;
                case CONTACTS:
                    record.setActivityTypeName(crmContactsService.getById(activityTypeId).getName());
                    break;
                case CONTRACT:
                    record.setActivityTypeName(crmContractService.getById(activityTypeId).getName());
                    break;
                case BUSINESS:
                    record.setActivityTypeName(crmBusinessService.getById(activityTypeId).getBusinessName());
                    break;
                case PRODUCT:
                    record.setActivityTypeName(ApplicationContextHolder.getBean(ICrmProductService.class).getById(activityTypeId).getName());
                    break;
                case RECEIVABLES:
                    record.setActivityTypeName(crmReceivablesService.getById(activityTypeId).getNumber());
                    break;
                case RECEIVABLES_PLAN:
                    record.setActivityTypeName(crmReceivablesPlanService.getById(activityTypeId).getNum());
                    break;
                case MARKETING:
                    record.setActivityTypeName(ApplicationContextHolder.getBean(ICrmMarketingService.class).getById(activityTypeId).getMarketingName());
                    break;
                case RETURN_VISIT:
                    record.setActivityTypeName(ApplicationContextHolder.getBean(ICrmReturnVisitService.class).getById(activityTypeId).getVisitNumber());
                    break;
                case INVOICE:
                    record.setActivityTypeName(ApplicationContextHolder.getBean(ICrmInvoiceService.class).getById(activityTypeId).getInvoiceApplyNumber());
                    break;
            }
        }
        record.setCreateUser(UserCacheUtil.getSimpleUser(record.getCreateUserId()));
        if (!ObjectUtil.isEmpty(record.getContent())) {
            JSONObject jsonObject = null;
            try {
                jsonObject = JSONObject.parseObject(record.getContent().toString());
                record.setContent(jsonObject);
            } catch (JSONException e) {
                record.setContent(record.getContent());
            }

            //特殊处理回款计划、回款、任务
            if (jsonObject != null) {
                Integer activityType = record.getActivityType();
                if (record.getType() == TWO) {
                    CrmActivityEnum parse = CrmActivityEnum.parse(activityType);
                    switch (parse) {
                        case CONTRACT:
                            CrmContract crmContract = crmContractService.getById(record.getActivityTypeId());
                            jsonObject.put("checkStatus", crmContract.getCheckStatus());
                            break;
                        case TASK:
                            SimpleUser ownerUserId = UserCacheUtil.getSimpleUser(jsonObject.getLong("ownerUserId"));
                            jsonObject.put("ownerUser", ownerUserId);
                            List<JSONObject> taskJson = getBaseMapper().querySubtasks(record.getActivityTypeId());
                            jsonObject.put("tasks", taskJson);
                            break;
                        case RECEIVABLES:
                            // 20220224 wwl 新增 ↓
                            CrmReceivables crmReceivables = crmReceivablesService.getById(record.getActivityTypeId());
                            jsonObject.put("checkStatus", crmReceivables.getCheckStatus());
                            // 20220224 wwl 新增 ↑
                        case RECEIVABLES_PLAN:
                            CrmContract contractId = crmContractService.getById(jsonObject.getLong("contractId"));
                            if (contractId != null) {
                                jsonObject.put("contractNum", contractId.getNum());
                            }
                            break;
                        case BUSINESS:
                            List<JSONObject> productJson = getBaseMapper().queryBusinessProducts(record.getActivityTypeId());
                            jsonObject.put("products", productJson);
                            break;
                    }
                }
            }
        }

        List<CrmActivityVO.Relation> contactsList = new ArrayList<>();
        List<CrmActivityVO.Relation> businessList = new ArrayList<>();
        List<CrmActivityVO.Relation> contractList = new ArrayList<>();
        List<CrmActivityVO.Relation> customerList = new ArrayList<>();
        if (record.getIsRelation() == 1) {
            List<JSONObject> list = getBaseMapper().queryRelationData(record.getId());
            list.forEach(json -> {
                CrmActivityVO.Relation relation = BeanUtil.copyProperties(json, CrmActivityVO.Relation.class);
                if (relation.getType() == THREE) {
                    contactsList.add(relation);
                } else if (relation.getType() == FIVE) {
                    businessList.add(relation);
                } else if (relation.getType() == TWO) {
                    customerList.add(relation);
                } else if (relation.getType() == SIX) {
                    contractList.add(relation);
                }
            });
        }
        record.setContactsList(contactsList);
        record.setBusinessList(businessList);
        record.setContractList(contractList);
        record.setCustomerList(customerList);

        if (!StrUtil.isEmpty(record.getBatchId())) {
            List<FileEntity> data = adminFileService.queryFileList(record.getBatchId()).getData();
            Map<String, List<FileEntity>> fileMap = data.stream().collect(Collectors.groupingBy(FileEntity::getFileType));
            String img = "img";
            if (fileMap.containsKey(img)) {
                record.setImg(fileMap.get("img"));
            }
            String file = "file";
            if (fileMap.containsKey(file)) {
                record.setFile(fileMap.get("file"));
            }
        }
    }


    /**
     * 删除跟进记录
     *
     * @param id 跟进记录主键ID
     */
    @Override
    public void deleteCrmActivityRecord(Long id) {
        CrmActivity crmActivity = getById(id);
        if (crmActivity == null) {
            return;
        }
        List<Long> longs = AuthUtil.queryAuthUserList(null, CrmAuthEnum.DELETE);
        if (!longs.contains(crmActivity.getCreateUserId())) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        if (crmActivity.getType() != 1) {
            throw new CrmException(CrmCodeEnum.CRM_CAN_ONLY_DELETE_FOLLOW_UP_RECORDS);
        }
        adminFileService.delete(Collections.singletonList(crmActivity.getBatchId()));
        //查询是否存在上一次跟进记录，存在时更新对应数据的最后跟进时间和内容。
        Optional<CrmActivity> lastRecordOpt = lambdaQuery().eq(CrmActivity::getType, 1).eq(CrmActivity::getActivityType, crmActivity.getActivityType())
                .eq(CrmActivity::getActivityTypeId, crmActivity.getActivityTypeId())
                .ne(CrmActivity::getId, id).orderByDesc(CrmActivity::getCreateTime).last("limit 1").oneOpt();
        if (lastRecordOpt.isPresent()) {
            CrmActivity lastRecord = lastRecordOpt.get();
            updateNextTime(BeanUtil.copyProperties(lastRecord, CrmActivitySaveBO.class), true);
        } else {
            CrmActivitySaveBO crmActivityBO = BeanUtil.copyProperties(crmActivity, CrmActivitySaveBO.class);
            crmActivity.setContent(null);
            crmActivity.setNextTime(null);
            updateNextTime(crmActivityBO, false);
        }
        removeById(id);
        //删除冗余数据
        lambdaUpdate().eq(CrmActivity::getPid, crmActivity.getPid()).remove();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityRecord(CrmActivitySaveBO crmActivity) {
        CrmActivity activity = getById(crmActivity.getId());
        //只能编辑类型为跟进记录的数据
        if (activity == null || activity.getType() != CrmActivityTypeEnum.ACTIVITY.getType()) {
            throw new CrmException(CrmCodeEnum.CRM_DATA_DELETED, "跟进记录");
        }
        List<Long> longs = AuthUtil.queryAuthUserList(null, CrmAuthEnum.EDIT);
        if (!longs.contains(activity.getCreateUserId())) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }

        lambdaUpdate().eq(CrmActivity::getPid, activity.getPid()).remove();
        saveActivity(crmActivity, UserUtil.getUser());
        updateNextTime(crmActivity, true);
    }

    private void saveActivity(CrmActivitySaveBO crmActivityBO, UserInfo user) {
        //跟进内容不可为空
        Assert.notNull(crmActivityBO.getContent(), CrmException::new);
        //跟进类型不可为空
        Assert.notNull(crmActivityBO.getActivityType(), CrmException::new);
        Assert.notNull(crmActivityBO.getActivityTypeId(), CrmException::new);
        CrmActivityEnum crmActivityEnum = CrmActivityEnum.parse(crmActivityBO.getActivityType());
        if (crmActivityEnum == null) {
            throw new CrmException();
        }
        List<CrmActivity> activityList = new ArrayList<>();
        CrmActivity crmActivity = BeanUtil.copyProperties(crmActivityBO, CrmActivity.class);
        crmActivity.setType(CrmActivityTypeEnum.ACTIVITY.getType());
        crmActivity.setCreateUserId(user.getUserId());
        crmActivity.setStatus(1);
        crmActivity.setActivityId(crmActivity.getActivityTypeId());
        String pid = IdUtil.fastSimpleUUID();
        crmActivity.setPid(pid);

        Long activityTypeId = crmActivity.getActivityTypeId();
        activityRelationService.lambdaUpdate().eq(CrmActivityRelation::getActivityId, crmActivity.getId()).remove();
        switch (crmActivityEnum) {
            case CUSTOMER: {
                //关联数据列表
                List<CrmActivityRelation> crmActivityRelations = new ArrayList<>();

                //客户保存时，往联系人和商机处保存一条冗余信息
                if (CollUtil.isNotEmpty(crmActivityBO.getContactsIds())) {
                    for (Long contactsId : crmActivityBO.getContactsIds()) {
                        CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                        Long nextId = BaseUtil.getNextId();
                        properties.setId(nextId);
                        properties.setActivityType(CrmActivityEnum.CUSTOMER.getType());
                        properties.setActivityTypeId(activityTypeId);
                        properties.setActivityId(contactsId);
                        properties.setContent(crmActivityBO.getContent());
                        properties.setIsRelation(1);
                        properties.setPid(pid);
                        activityList.add(properties);

                        //标记当前活动记录存在关联数据
                        crmActivity.setIsRelation(1);

                        crmActivityRelations.add(new CrmActivityRelation(nextId, CrmActivityEnum.CONTACTS.getType(), contactsId));
                    }
                }
                if (CollUtil.isNotEmpty(crmActivityBO.getBusinessIds())) {
                    for (Long businessId : crmActivityBO.getBusinessIds()) {
                        CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                        Long nextId = BaseUtil.getNextId();
                        properties.setId(nextId);
                        properties.setActivityType(CrmActivityEnum.CUSTOMER.getType());
                        properties.setActivityTypeId(activityTypeId);
                        properties.setActivityId(businessId);
                        properties.setContent(crmActivityBO.getContent());
                        properties.setIsRelation(1);
                        properties.setPid(pid);
                        activityList.add(properties);

                        //标记当前活动记录存在关联数据
                        crmActivity.setIsRelation(1);
                        crmActivityRelations.add(new CrmActivityRelation(nextId, CrmActivityEnum.BUSINESS.getType(), businessId));
                    }
                }
                //保存关联数据
                activityRelationService.saveBatch(crmActivityRelations, crmActivityRelations.size());
                break;
            }
            //联系人向关联的客户新增一条冗余记录
            case CONTACTS: {
                CrmContacts crmContacts = crmContactsService.lambdaQuery().eq(CrmContacts::getContactsId, crmActivityBO.getActivityTypeId()).one();
                if (crmContacts != null && crmContacts.getCustomerId() != null) {
                    CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                    Long nextId = BaseUtil.getNextId();
                    properties.setId(nextId);
                    properties.setActivityType(CrmActivityEnum.CONTACTS.getType());
                    properties.setActivityTypeId(activityTypeId);
                    properties.setActivityId(crmContacts.getCustomerId());
                    properties.setContent(crmActivityBO.getContent());
                    properties.setIsRelation(1);
                    properties.setPid(pid);
                    activityList.add(properties);

                    CrmActivityRelation crmActivityRelation = new CrmActivityRelation(nextId, CrmActivityEnum.CONTACTS.getType(), crmContacts.getContactsId());
                    //保存关联数据
                    activityRelationService.save(crmActivityRelation);
                }
                break;
            }
            //商机向关联的客户新增一条冗余记录
            case BUSINESS: {
                CrmBusiness crmBusiness = crmBusinessService.lambdaQuery().eq(CrmBusiness::getBusinessId, crmActivityBO.getActivityTypeId()).one();
                if (crmBusiness != null && crmBusiness.getCustomerId() != null) {
                    CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                    Long nextId = BaseUtil.getNextId();
                    properties.setId(nextId);
                    properties.setActivityType(CrmActivityEnum.BUSINESS.getType());
                    properties.setActivityTypeId(activityTypeId);
                    properties.setActivityId(crmBusiness.getCustomerId());
                    properties.setContent(crmActivityBO.getContent());
                    properties.setIsRelation(1);
                    properties.setPid(pid);
                    activityList.add(properties);

                    CrmActivityRelation crmActivityRelation = new CrmActivityRelation(nextId, CrmActivityEnum.BUSINESS.getType(), crmBusiness.getBusinessId());
                    //保存关联数据
                    activityRelationService.save(crmActivityRelation);
                }
                break;
            }
            //合同向关联的客户，商机新增一条冗余记录
            case CONTRACT: {
                CrmContract crmContract = crmContractService.lambdaQuery().eq(CrmContract::getContractId, crmActivityBO.getActivityTypeId()).one();
                //合同肯定会关联客户，所以这个放在一块判断了
                if (crmContract != null && crmContract.getCustomerId() != null) {

                    //关联数据列表
                    List<CrmActivityRelation> crmActivityRelations = new ArrayList<>();
                    CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                    Long nextId = BaseUtil.getNextId();
                    properties.setId(nextId);
                    properties.setActivityType(CrmActivityEnum.CONTRACT.getType());
                    properties.setActivityTypeId(activityTypeId);
                    properties.setActivityId(crmContract.getCustomerId());
                    properties.setContent(crmActivityBO.getContent());
                    properties.setIsRelation(1);
                    properties.setPid(pid);
                    activityList.add(properties);
                    crmActivityRelations.add(new CrmActivityRelation(nextId, CrmActivityEnum.CONTRACT.getType(), crmContract.getContractId()));

                    if (crmContract.getBusinessId() != null) {
                        CrmActivity businessActivity = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                        businessActivity.setActivityType(CrmActivityEnum.CONTRACT.getType());
                        Long businessNextId = BaseUtil.getNextId();
                        businessActivity.setId(businessNextId);
                        businessActivity.setActivityTypeId(activityTypeId);
                        businessActivity.setActivityId(crmContract.getBusinessId());
                        businessActivity.setContent(crmActivityBO.getContent());
                        businessActivity.setIsRelation(1);
                        businessActivity.setPid(pid);
                        activityList.add(businessActivity);

                        crmActivityRelations.add(new CrmActivityRelation(businessNextId, CrmActivityEnum.CONTRACT.getType(), crmContract.getContractId()));
                    }

                    //保存关联数据
                    activityRelationService.saveBatch(crmActivityRelations);
                }
                break;
            }
            //合同向关联的客户，合同，商机新增一条冗余记录，商机ID需要根据合同ID再次查询
            case RECEIVABLES: {
                CrmReceivables crmReceivables = crmReceivablesService.lambdaQuery().eq(CrmReceivables::getReceivablesId, crmActivityBO.getActivityTypeId()).one();
                //回款
                if (crmReceivables != null && crmReceivables.getCustomerId() != null && crmReceivables.getContractId() != null) {

                    //关联数据列表
                    List<CrmActivityRelation> crmActivityRelations = new ArrayList<>();

                    //添加客户冗余记录
                    CrmActivity properties = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                    Long nextId = BaseUtil.getNextId();
                    properties.setId(nextId);
                    properties.setActivityType(CrmActivityEnum.RECEIVABLES.getType());
                    properties.setActivityTypeId(activityTypeId);
                    properties.setActivityId(crmReceivables.getCustomerId());
                    properties.setContent(crmActivityBO.getContent());
                    properties.setIsRelation(1);
                    properties.setPid(pid);
                    activityList.add(properties);

                    crmActivityRelations.add(new CrmActivityRelation(nextId, CrmActivityEnum.RECEIVABLES.getType(), crmReceivables.getReceivablesId()));

                    //添加合同冗余记录
                    CrmActivity contractActivity = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                    contractActivity.setActivityType(CrmActivityEnum.RECEIVABLES.getType());
                    Long contractNextId = BaseUtil.getNextId();
                    contractActivity.setId(contractNextId);
                    contractActivity.setActivityTypeId(activityTypeId);
                    contractActivity.setActivityId(crmReceivables.getContractId());
                    contractActivity.setContent(crmActivityBO.getContent());
                    contractActivity.setIsRelation(1);
                    contractActivity.setPid(pid);
                    activityList.add(contractActivity);

                    crmActivityRelations.add(new CrmActivityRelation(contractNextId, CrmActivityEnum.RECEIVABLES.getType(), crmReceivables.getReceivablesId()));

                    Optional<CrmContract> contract = crmContractService.lambdaQuery().eq(CrmContract::getContractId, crmReceivables.getContractId()).oneOpt();
                    contract.ifPresent(data -> {
                        if (data.getBusinessId() != null) {
                            CrmActivity businessActivity = BeanUtil.copyProperties(crmActivity, CrmActivity.class);
                            Long businessNextId = BaseUtil.getNextId();
                            businessActivity.setId(businessNextId);
                            businessActivity.setActivityType(CrmActivityEnum.RECEIVABLES.getType());
                            businessActivity.setActivityTypeId(activityTypeId);
                            businessActivity.setActivityId(data.getBusinessId());
                            businessActivity.setContent(crmActivityBO.getContent());
                            businessActivity.setIsRelation(1);
                            businessActivity.setPid(pid);
                            activityList.add(businessActivity);

                            crmActivityRelations.add(new CrmActivityRelation(businessNextId, CrmActivityEnum.RECEIVABLES.getType(), crmReceivables.getReceivablesId()));
                        }
                    });
                    //保存关联数据
                    activityRelationService.saveBatch(crmActivityRelations);
                }
                break;
            }
        }
        //保存本体（发送方数据）
        save(crmActivity);
        //保存接收方
        saveBatch(activityList);
        crmActivityBO.setId(crmActivity.getId());
    }

    private final static Long OUT_WORK_MENU_ID = 215L;

    /**
     * 外勤签到
     */
    @Override
    public void outworkSign(CrmActivitySaveBO activityBO) {
        UserInfo user = UserUtil.getUser();
        CrmActivity crmActivity = BeanUtil.copyProperties(activityBO, CrmActivity.class);
        String batchId = StrUtil.isEmpty(crmActivity.getBatchId()) ? IdUtil.simpleUUID() : crmActivity.getBatchId();
        crmActivity.setType(4);
        crmActivity.setCreateUserId(user.getUserId());
        crmActivity.setCreateTime(LocalDateTimeUtil.now());
        crmActivity.setBatchId(batchId);
        crmActivity.setType(4);
        ActivityContent content = new ActivityContent();
        content.setContentType(CrmEnum.CUSTOMER.getType());
        content.setContent(activityBO.getContent());
        JSONObject jsonObject = BeanUtil.copyProperties(content, JSONObject.class);
        jsonObject.fluentPut("address", activityBO.getAddress()).fluentPut("lng", activityBO.getLng()).fluentPut("lat", activityBO.getLat());
        crmActivity.setContent(jsonObject.toJSONString());
        crmActivity.setActivityId(activityBO.getActivityTypeId());
        save(crmActivity);
        updateNextTime(activityBO, true);
    }

    /**
     * app外勤统计
     */
    @Override
    public BasePage<JSONObject> queryOutworkStats(PageEntity entity, String startTime, String endTime) {
        List<Long> userIdByAuth = AuthUtil.getUserIdByAuth(OUT_WORK_MENU_ID);
        return getBaseMapper().queryOutworkStats(entity.parse(), startTime, endTime, userIdByAuth);
    }

    /**
     * app外勤详情
     */
    @Override
    public BasePage<CrmActivity> queryOutworkList(PageEntity entity, String startTime, String endTime, Long userId) {
        /*List<Long> authUserIdList = AuthUtil.getUserIdByAuth(outWorkMenuId);
        if (!authUserIdList.contains(userId)) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(crmActivityQueryBO, CrmAuthEnum.READ.getMenuId(null));
        BasePage<CrmActivityVO> basePage = getBaseMapper().getCrmActivityPageList(entity.parse(), startTime, endTime, userId);
        basePage.getList().forEach(this::buildActivityRelation);
        return basePage;*/
        return null;
    }

    /**
     * app外勤详情
     */
    @Override
    public BasePage<CrmActivityVO> queryOutworkList(CrmActivityQueryBO crmActivityQueryBO) {
        List<Long> authUserIdList = AuthUtil.getUserIdByAuth(OUT_WORK_MENU_ID);
        /*if (!authUserIdList.contains(crmActivityQueryBO.getUserId())) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }*/

        /*BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(crmActivityQueryBO, CrmAuthEnum.READ.getMenuId(null));
        BasePage<CrmActivityVO> basePage = getBaseMapper().getCrmActivityPageList(entity.parse(), startTime, endTime, userId);*/

        BasePage<CrmActivityVO> basePage = getBaseMapper().queryOutworkList(crmActivityQueryBO.parse(), DateUtil.formatDateTime(crmActivityQueryBO.getStartDate()), DateUtil.formatDateTime(crmActivityQueryBO.getEndDate()), crmActivityQueryBO.getUserId());
        basePage.getList().forEach(this::buildActivityRelation);
        return basePage;
    }

    /**
     * app 查询签到照片上传设置
     */
    @Override
    public Integer queryPictureSetting() {
        AdminConfig adminConfig = adminService.queryFirstConfigByName("pictureSetting").getData();
        if (adminConfig == null) {
            adminConfig = new AdminConfig();
            adminConfig.setStatus(1);
            adminConfig.setName("pictureSetting");
            adminService.updateAdminConfig(adminConfig);
        }
        return adminConfig.getStatus();
    }

    /**
     * app 查询签到照片上传设置
     */
    @Override
    public void setPictureSetting(Integer status) {
        AdminConfig adminConfig = adminService.queryFirstConfigByName("pictureSetting").getData();
        if (adminConfig == null) {
            adminConfig = new AdminConfig();
        }
        adminConfig.setStatus(status);
        adminConfig.setName("pictureSetting");
        adminService.updateAdminConfig(adminConfig);
    }

    /**
     * 删除外勤签到
     */
    @Override
    public void deleteOutworkSign(Long activityId) {
        CrmActivity crmActivity = getById(activityId);
        if (crmActivity == null) {
            return;
        }
        //外勤签到删除所需菜单ID
        Long menuId = 216L;
        List<Long> authUserIdList = AuthUtil.getUserIdByAuth(menuId);
        if (!authUserIdList.contains(crmActivity.getCreateUserId())) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
        }
        removeById(activityId);
    }

    @Override
    public List<Map<String, Object>> exportRecordList(CrmActivityQueryBO biParams) {
        biParams.setPageType(0);
        BasePage<CrmActivityVO> crmActivityPageList = getCrmActivityPageList(biParams);
        List<Map<String, Object>> list = new ArrayList<>();
        crmActivityPageList.getList().forEach(crmActivityVO -> {
            Map<String, Object> map = BeanUtil.beanToMap(crmActivityVO);
            map.put("createUserName", crmActivityVO.getCreateUser().getRealname());
            if (CollectionUtil.isNotEmpty(crmActivityVO.getContactsList())) {
                List<String> collect = crmActivityVO.getContactsList().stream().map(CrmActivityVO.Relation::getName).collect(Collectors.toList());
                map.put("contactsNames", StringUtils.join(collect, Const.SEPARATOR));
            } else {
                map.put("contactsNames", "");
            }
            if (CollectionUtil.isNotEmpty(crmActivityVO.getBusinessList())) {
                List<String> collect = crmActivityVO.getBusinessList().stream().map(CrmActivityVO.Relation::getName).collect(Collectors.toList());
                map.put("businessNames", StringUtils.join(collect, Const.SEPARATOR));
            } else {
                map.put("businessNames", "");
            }
            list.add(map);
        });
        return list;
    }

    @Override
    public JSONObject importRecordList(MultipartFile file, Integer crmType) {
        List<List<Object>> errList = new ArrayList<>();
        if (!Arrays.asList(1, TWO, THREE, FIVE, SIX).contains(crmType)) {
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
        CrmEnum crmEnum = CrmEnum.parse(crmType);
        String filePath = getFilePath(file);
        List<UserInfo> userInfos = adminService.queryUserInfoList().getData();
        Map<String, Long> map = userInfos.stream().collect(Collectors.toMap(UserInfo::getRealname, UserInfo::getUserId, (k1, k2) -> k1));
        AtomicReference<Integer> num = new AtomicReference<>(0);
        int size = crmType == 2 ? 7 : 5;
        //客户 ：跟进内容（必填）、创建人（必填）、所属客户（必填）、跟进时间、跟进方式、相关联系人、相关商机
        //非客户：跟进内容（必填）、创建人（必填）、所属线索/联系人/商机/合同（必填）、跟进时间、跟进方式
        ExcelUtil.readBySax(filePath, 0, (int sheetIndex, long rowIndex, List<Object> rowList) -> {
            if (rowList.size() < size) {
                for (int i = rowList.size(); i < size; i++) {
                    rowList.add(null);
                }
            }
            if (rowIndex > 1) {
                num.getAndSet(num.get() + 1);
                if (StrUtil.isEmptyIfStr(rowList.get(0))) {
                    rowList.add(0, "跟进内容不能为空");
                    errList.add(rowList);
                    return;
                }
                if (StrUtil.isEmptyIfStr(rowList.get(1))) {
                    rowList.add(0, "创建人不能为空");
                    errList.add(rowList);
                    return;
                }
                if (StrUtil.isEmptyIfStr(rowList.get(TWO))) {
                    rowList.add(0, "所属" + crmEnum.getRemarks() + "不能为空");
                    errList.add(rowList);
                    return;
                }
                String content = rowList.get(0).toString().trim();
                String createUserName = rowList.get(1).toString().trim();
                Long createUserId = map.get(createUserName);
                if (createUserId == null) {
                    rowList.add(0, "创建人不存在");
                    errList.add(rowList);
                    return;
                }
                String crmTypeName = rowList.get(2).toString().trim();
                String createTime = Optional.ofNullable(rowList.get(3)).orElse("").toString().trim();
                LocalDateTime createDate = null;
                if (StrUtil.isNotEmpty(createTime)) {
                    createDate = DateUtil.parseDate(createTime).toLocalDateTime();
                }
                String category = Optional.ofNullable(rowList.get(4)).orElse("").toString().trim();
                String contactsNames = null;
                String businessNames = null;
                Long crmTypeId = null;

                CrmActivitySaveBO crmActivityBO = new CrmActivitySaveBO();

                switch (crmEnum) {
                    case CUSTOMER:
                        contactsNames = Optional.ofNullable(rowList.get(5)).orElse("").toString().trim();
                        businessNames = Optional.ofNullable(rowList.get(6)).orElse("").toString().trim();
                        CrmCustomer crmCustomer = crmCustomerService.lambdaQuery().select(CrmCustomer::getCustomerId)
                                .eq(CrmCustomer::getCustomerName, crmTypeName).ne(CrmCustomer::getStatus, 3)
                                .orderByDesc(CrmCustomer::getCreateTime).last(" limit 1 ").one();
                        if (crmCustomer == null) {
                            rowList.add(0, "所属客户不存在");
                            errList.add(rowList);
                            return;
                        }
                        crmTypeId = crmCustomer.getCustomerId();
                        if (StrUtil.isNotEmpty(contactsNames)) {
                            List<String> names;
                            String split="/";
                            if (contactsNames.contains(split)) {
                                names = Arrays.asList(contactsNames.split(split));
                            } else {
                                names = ListUtil.toList(contactsNames);
                            }
                            names.removeIf(StrUtil::isEmpty);
                            List<Long> contactsList = new ArrayList<>(names.size());
                            for (String name : names) {
                                Optional<CrmContacts> crmContacts = crmContactsService.lambdaQuery().select(CrmContacts::getContactsId)
                                        .eq(CrmContacts::getName, name).last(" limit 1").oneOpt();
                                crmContacts.ifPresent(crmContacts1 -> contactsList.add(crmContacts1.getContactsId()));
                                crmActivityBO.setContactsIds(contactsList);
                            }
                            contactsNames = StrUtil.join(Const.SEPARATOR, contactsList);
                        }

                        if (StrUtil.isNotEmpty(businessNames)) {
                            List<String> names;
                            String split="/";
                            if (businessNames.contains(split)) {
                                names = Arrays.asList(businessNames.split(split));
                            } else {
                                names = ListUtil.toList(businessNames);
                            }
                            names.removeIf(StrUtil::isEmpty);
                            List<CrmBusiness> list = crmBusinessService.lambdaQuery().select(CrmBusiness::getBusinessId)
                                    .in(CrmBusiness::getBusinessName, names).ne(CrmBusiness::getStatus, 3).list();
                            if (CollUtil.isNotEmpty(list)) {
                                List<Long> businessIdList = list.stream().map(CrmBusiness::getBusinessId).collect(Collectors.toList());
                                businessNames = StrUtil.join(Const.SEPARATOR, businessIdList);
                                crmActivityBO.setBusinessIds(businessIdList);
                            }
                        }

                        break;
                    case LEADS:
                        CrmLeads crmLeads = crmLeadsService.lambdaQuery().select(CrmLeads::getLeadsId)
                                .eq(CrmLeads::getLeadsName, crmTypeName).orderByDesc(CrmLeads::getCreateTime).last(" limit 1 ").one();
                        if (crmLeads == null) {
                            rowList.add(0, "所属线索不存在");
                            errList.add(rowList);
                            return;
                        }
                        crmTypeId = crmLeads.getLeadsId();
                        break;
                    case CONTACTS:
                        CrmContacts crmContacts = crmContactsService.lambdaQuery().select(CrmContacts::getContactsId)
                                .eq(CrmContacts::getName, crmTypeName).orderByDesc(CrmContacts::getCreateTime).last(" limit 1 ").one();
                        if (crmContacts == null) {
                            rowList.add(0, "所属联系人不存在");
                            errList.add(rowList);
                            return;
                        }
                        crmTypeId = crmContacts.getContactsId();
                        break;
                    case BUSINESS:
                        CrmBusiness crmBusiness = crmBusinessService.lambdaQuery().select(CrmBusiness::getBusinessId)
                                .eq(CrmBusiness::getBusinessName, crmTypeName).ne(CrmBusiness::getStatus, 3)
                                .orderByDesc(CrmBusiness::getCreateTime).last(" limit 1 ").one();
                        if (crmBusiness == null) {
                            rowList.add(0, "所属商机不存在");
                            errList.add(rowList);
                            return;
                        }
                        crmTypeId = crmBusiness.getBusinessId();
                        break;
                    case CONTRACT:
                        CrmContract crmContract = crmContractService.lambdaQuery().select(CrmContract::getContractId)
                                .eq(CrmContract::getNum, crmTypeName).ne(CrmContract::getCheckStatus, 7)
                                .orderByDesc(CrmContract::getCreateTime).last(" limit 1 ").one();
                        if (crmContract == null) {
                            rowList.add(0, "所属合同不存在");
                            errList.add(rowList);
                            return;
                        }
                        crmTypeId = crmContract.getContractId();
                        break;
                    default:
                        break;
                }
                UserInfo user = UserCacheUtil.getUserInfo(createUserId);

                crmActivityBO.setActivityType(crmEnum.getType());
                crmActivityBO.setCategory(category);
                crmActivityBO.setContent(content);
                crmActivityBO.setActivityTypeId(crmTypeId);
                crmActivityBO.setCreateTime(createDate != null ? createDate : LocalDateTimeUtil.now());
                saveActivity(crmActivityBO, user);


                List<CrmActivityRelation> activityRelationList = new ArrayList<>();
                if (CollUtil.isNotEmpty(crmActivityBO.getContactsIds())) {
                    for (Long contactsId : crmActivityBO.getContactsIds()) {
                        CrmActivityRelation activityRelation = new CrmActivityRelation();
                        activityRelation.setActivityId(crmActivityBO.getId());
                        activityRelation.setType(CrmEnum.CONTACTS.getType());
                        activityRelation.setTypeId(contactsId);
                        activityRelation.setActivityId(crmActivityBO.getId());
                        activityRelationList.add(activityRelation);
                    }
                }
                if (CollUtil.isNotEmpty(crmActivityBO.getBusinessIds())) {
                    for (Long businessId : crmActivityBO.getBusinessIds()) {
                        CrmActivityRelation activityRelation = new CrmActivityRelation();
                        activityRelation.setActivityId(crmActivityBO.getId());
                        activityRelation.setType(CrmEnum.BUSINESS.getType());
                        activityRelation.setTypeId(businessId);
                        activityRelation.setActivityId(crmActivityBO.getId());
                        activityRelationList.add(activityRelation);
                    }
                }
                activityRelationService.saveBatch(activityRelationList, Const.BATCH_SAVE_SIZE);

                crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.TODAY_CUSTOMER, crmActivityBO.getActivityTypeId());
                crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.FOLLOW_LEADS, crmActivityBO.getActivityTypeId());
                crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.FOLLOW_CUSTOMER, crmActivityBO.getActivityTypeId());
                crmBackLogDealService.deleteByType(user.getUserId(), CrmEnum.parse(crmActivityBO.getActivityType()), CrmBackLogEnum.TO_ENTER_CUSTOMER_POOL, crmActivityBO.getActivityTypeId());

                actionRecordUtil.addFollowupActionRecord(crmActivityBO.getActivityType(), crmActivityBO.getActivityTypeId(), "");
            }
        });
        FileUtil.del(filePath);
        JSONObject result = new JSONObject().fluentPut("totalSize", num.get()).fluentPut("errSize", 0);
        if (errList.size() > 0) {
            BigExcelWriter writer = null;
            try {
                String token = IdUtil.simpleUUID();
                writer = ExcelUtil.getBigWriter(FileUtil.getTmpDirPath() + "/" + token);
                int columnNum = crmType == 2 ? 7 : 5;
                writer.merge(columnNum, "系统用户导入模板(*)为必填项");
                for (int i = 0; i < columnNum + 1; i++) {
                    writer.setColumnWidth(i, 20);
                }
                writer.setDefaultRowHeight(20);
                Cell cell = writer.getCell(0, 0);
                CellStyle cellStyle = cell.getCellStyle();
                cellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font font = writer.createFont();
                font.setBold(true);
                font.setFontHeightInPoints((short) 16);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                if (crmType == TWO) {
                    writer.writeHeadRow(Arrays.asList("错误信息", "跟进内容(*)", "创建人(*)", "所属客户(*)", "跟进时间", "跟进方式", "相关联系人", "相关商机"));
                } else {
                    writer.writeHeadRow(Arrays.asList("错误信息", "跟进内容(*)", "创建人(*)", "所属" + crmEnum.getRemarks() + "(*)", "跟进时间", "跟进方式"));
                }
                writer.write(errList);
                result.fluentPut("errSize", errList.size()).fluentPut("token", token);
            } finally {
                if (writer != null) {
                    writer.close();
                }
            }
        }
        return result;
    }

    private String getFilePath(MultipartFile file) {
        String dirPath = FileUtil.getTmpDirPath();
        try {
            InputStream inputStream = file.getInputStream();
            File fromStream = FileUtil.writeFromStream(inputStream, dirPath + "/" + IdUtil.simpleUUID() + file.getOriginalFilename());
            return fromStream.getAbsolutePath();
        } catch (IOException e) {
            throw new CrmException(SystemCodeEnum.SYSTEM_UPLOAD_FILE_ERROR);
        }
    }
}
