package com.kakarote.crm.controller;


import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.PO.CrmSearchExpert;
import com.kakarote.crm.service.ICrmSearchExpertService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 高级筛选外漏查询条件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@RestController
@RequestMapping("/crmSearchExpert")
public class CrmSearchExpertController {


    @Autowired
    private ICrmSearchExpertService crmSearchExpertService;


    @PostMapping("/save")
    @ApiOperation("保存外漏高级筛选条件")
    public Result save(@RequestBody CrmSearchExpert searchExpert) {
        crmSearchExpertService.saveAndUpdate(searchExpert);
        return R.ok();
    }

    @PostMapping("/queryByLabel")
    @ApiOperation("根据类型，获取外漏高级筛选条件")
    public Result<CrmSearchExpert> queryByLabel(@RequestParam(value = "type",required = false) Integer label) {
        CrmSearchExpert searchExpert = crmSearchExpertService.queryByLabel(label);
        return R.ok(searchExpert);
    }


}

