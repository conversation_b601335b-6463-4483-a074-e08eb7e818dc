package com.kakarote.crm.controller;


import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.PO.CrmSearchDefault;
import com.kakarote.crm.service.ICrmSearchDefaultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 查询条件默认值 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@RestController
@RequestMapping("/crmSearchDefault")
@Api(tags = "仪表盘默认值模块模块")
public class CrmSearchDefaultController {

    @Autowired
    private ICrmSearchDefaultService crmDefaultService;


    @PostMapping("/save")
    @ApiOperation("保存默认值")
    public Result save(@RequestBody CrmSearchDefault crmDefault) {
        crmDefaultService.saveAndUpdate(crmDefault);
        return R.ok();
    }

    @PostMapping("/queryByType")
    @ApiOperation("根据类型，获取当前默认值")
    public Result<CrmSearchDefault> queryByType(@RequestBody CrmSearchDefault crmDefault) {
        CrmSearchDefault crmDefault1 = crmDefaultService.queryByType(crmDefault);
        return R.ok(crmDefault1);
    }

}

