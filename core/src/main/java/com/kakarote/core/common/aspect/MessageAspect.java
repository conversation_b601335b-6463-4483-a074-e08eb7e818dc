package com.kakarote.core.common.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.entity.AdminUserQueryBO;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.feign.admin.entity.AdminMessageBO;
import com.kakarote.core.feign.admin.entity.AdminMessageConfig;
import com.kakarote.core.feign.admin.entity.AdminMessageEnum;
import com.kakarote.core.feign.admin.service.AdminMessageConfigService;
import com.kakarote.core.feign.admin.service.AdminMessageService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.utils.AdminMessageUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 消息切点类
 * @date 2021/9/2510:08
 */
@Aspect
@Component
public class MessageAspect {

    @Autowired
    @Lazy
    private AdminMessageConfigService messageConfigService;

    @Autowired
    @Lazy
    private AdminMessageService messageService;

    @Autowired
    @Lazy
    private AdminService adminService;

    @Pointcut("@annotation(com.kakarote.core.common.annotation.Message)")
    public void messageAspect() {}

    @After("messageAspect()")
    public void doAfter(JoinPoint joinPoint){
        try {
            MsgBodyBO msgBodyBO = AdminMessageUtil.getMsgBody();
            if (ObjectUtil.isNull(msgBodyBO)) {
                return;
            }
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Message message = signature.getMethod().getAnnotation(Message.class);
            CrmMsgLabelEnum label;
            if (StrUtil.isEmpty(msgBodyBO.getMsgTag())) {
                label = message.label();
            } else {
                label = CrmMsgLabelEnum.parse(msgBodyBO.getMsgTag());
            }
            CrmMsgActionEnum action = message.action();
            AdminMessageEnum messageEnum = this.parseMessageType(action);
            if (ObjectUtil.equal(CrmMsgActionEnum.save, action)) {
                messageEnum = AdminMessageEnum.CRM_SAVE;
            }
            AdminMessageConfig messageConfig = messageConfigService.getByLabelAndAction(label.name(), action.name()).getData();
            if (ObjectUtil.isNotNull(messageConfig)) {
                Set<Long> userIds = new HashSet<>();
                // 是否推送负责人
                Integer pushOwnerUser = messageConfig.getPushOwnerUser();
                // 是否推送团队成员
                Integer pushTermMember = messageConfig.getPushTermMember();
                if (pushTermMember == 1) {
                    userIds.addAll(msgBodyBO.getMemberIds());
                }
                // 是否推送上级
                String pushParentUser = messageConfig.getPushParentUser();
                if (StrUtil.isNotEmpty(pushParentUser)) {
                    List<Integer> levels = Arrays.stream(pushParentUser.split(Const.SEPARATOR)).map(l -> Integer.valueOf(l)).collect(Collectors.toList());

                    AdminUserQueryBO queryBO = new AdminUserQueryBO();
                    queryBO.setUserId(msgBodyBO.getCurrentUser().getUserId());
                    queryBO.setLevels(levels);
                    List<UserInfo> parentUsers = adminService.queryParentByLevel(queryBO).getData();
                    List<Long> ids = parentUsers.stream().map(UserInfo::getUserId).collect(Collectors.toList());
                    userIds.addAll(ids);
                }
                // 是否推送自定义用户
                String pushCustomUser = messageConfig.getPushCustomUser();
                if (StrUtil.isNotEmpty(pushCustomUser)) {
                    List<Long> ids = Arrays.stream(pushCustomUser.split(Const.SEPARATOR)).map(Long::valueOf).collect(Collectors.toList());
                    userIds.addAll(ids);
                }
                // 是否推送自定义部门
                String pushCustomDept = messageConfig.getPushCustomDept();
                if (StrUtil.isNotEmpty(pushCustomDept)) {
                    List<Long> deptIds = Arrays.stream(pushCustomDept.split(Const.SEPARATOR)).map(Long::valueOf).collect(Collectors.toList());
                    List<Long> ids = adminService.queryUserByDeptIds(deptIds).getData();
                    userIds.addAll(ids);
                }
                // 操作对象
                List<JSONObject> jsonObjects = msgBodyBO.getOperateObject();
                // 推送消息
                if (messageConfig.getPushMessage() == 1) {
                    if (CollUtil.isNotEmpty(jsonObjects)) {
                        for (JSONObject jsonObject : jsonObjects) {
                            if (pushOwnerUser == 1) {
                                userIds.add(jsonObject.getLong("ownerUserId"));
                            }
                            AdminMessageBO adminMessageBO = new AdminMessageBO();
                            adminMessageBO.setTypeId(jsonObject.getLong("id"));
                            adminMessageBO.setUserId(msgBodyBO.getCurrentUser().getUserId());
                            adminMessageBO.setIds(Arrays.asList(userIds.toArray(userIds.toArray(new Long[0]))));
                            adminMessageBO.setMessageType(messageEnum.getType());
                            messageService.sendMessage(adminMessageBO);
                        }
                    } else {
                        AdminMessageBO adminMessageBO = new AdminMessageBO();
                        adminMessageBO.setTitle(msgBodyBO.getTitle());
                        adminMessageBO.setUserId(msgBodyBO.getCurrentUser().getUserId());
                        userIds.add(msgBodyBO.getCurrentUser().getUserId());
                        adminMessageBO.setIds(Arrays.asList(userIds.toArray(userIds.toArray(new Long[0]))));
                        adminMessageBO.setMessageType(messageEnum.getType());
                        messageService.sendMessage(adminMessageBO);
                    }
                }
                // 浏览器推送
                if (messageConfig.getPushBrowser() == 1) {
                    msgBodyBO.setUserIds(userIds);
                    messageService.sendMQMsg(msgBodyBO);
                }
            }
        } finally {
            AdminMessageUtil.remove();
        }
    }

    private AdminMessageEnum parseMessageType(CrmMsgActionEnum action){
        AdminMessageEnum messageEnum = null;
        if (ObjectUtil.equal(CrmMsgActionEnum.save, action)) {
            messageEnum = AdminMessageEnum.CRM_SAVE;
        } else if (ObjectUtil.equal(CrmMsgActionEnum.transfer, action)) {
            messageEnum = AdminMessageEnum.CRM_TRANSFER;

        } else if (ObjectUtil.equal(CrmMsgActionEnum.transform, action)) {
            messageEnum = AdminMessageEnum.CRM_TRANSFORM;

        } else if (ObjectUtil.equal(CrmMsgActionEnum.addTermMember, action)) {
            messageEnum = AdminMessageEnum.CRM_TERM_MEMBER;

        } else if (ObjectUtil.equal(CrmMsgActionEnum.updateInvoiceStatus, action)) {
            messageEnum = AdminMessageEnum.CRM_UPDATE_INVOICE_STATUS;

        } else if (ObjectUtil.equal(CrmMsgActionEnum.excelImport, action)) {
            messageEnum = AdminMessageEnum.CRM_EXCEL_IMPORT;

        } else if (ObjectUtil.equal(CrmMsgActionEnum.excelExport, action)) {
            messageEnum = AdminMessageEnum.CRM_EXCEL_EXPORT;
        }
        return messageEnum;
    }
}
