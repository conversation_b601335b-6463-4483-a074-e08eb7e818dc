package com.kakarote.core.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.util.TypeUtils;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CreateCache;
import com.kakarote.core.common.Const;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.redis.Redis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 用户缓存相关方法
 */
@Component
public class UserCacheUtil {
    static UserCacheUtil ME;

    @PostConstruct
    public void init() {
        ME = this;
    }

    @CreateCache(name = Const.ADMIN_USER_NAME_CACHE_NAME, expire = 3, timeUnit = TimeUnit.DAYS)
    private Cache<Long, SimpleUser> userCache;

    @CreateCache(name = Const.ADMIN_DEPT_NAME_CACHE_NAME, expire = 3, timeUnit = TimeUnit.DAYS)
    private Cache<Long, String> deptCache;

    @Autowired
    @Lazy
    private AdminService adminService;

    @Autowired
    Redis redis;

    /**
     * 根据用户ID获取用户名列表，使用，号合并
     *
     * @param userIds userIds
     * @return data
     */
    public static <T> List<String> getUserNameList(List<T> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> stringList = new ArrayList<>(userIds.size());
        for (T obj : userIds) {
            String name = getUserName(TypeUtils.castToLong(obj));
            if (!"".equals(name)) {
                stringList.add(name);
            }
        }
        return stringList;
    }

    /**
     * 根据用户ID获取用户名
     *
     * @param userId 用户ID
     * @return data
     */
    public static UserInfo getUserInfo(Long userId) {
        return ME.adminService.getUserInfo(userId).getData();
    }

    /**
     * 根据用户ID获取用户名
     *
     * @param userId 用户ID
     * @return data
     */
    public static String getUserName(Long userId) {
        if (userId == null) {
            return "";
        }
        return getSimpleUser(userId).getRealname();
    }

    public static SimpleUser getSimpleUser(Long userId) {
        if (Objects.isNull(userId)) {
            return new SimpleUser();
        }
        SimpleUser simpleUser = handleSimpleUser(userId);
        if (Objects.isNull(simpleUser)) {
            simpleUser = new SimpleUser();
        }
        return simpleUser;
    }

    /**
     * 获取userList
     *
     * @param ids ids
     * @return java.util.List<com.kakarote.core.feign.admin.entity.SimpleUser>
     * @date 2021/11/19
     */
    public static List<SimpleUser> getSimpleUsers(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<SimpleUser> simpleUserList = new ArrayList<>(ids.size());
        ids.forEach(userId -> {
            SimpleUser simpleUser = handleSimpleUser(userId);
            if (Objects.nonNull(simpleUser)) {
                simpleUserList.add(simpleUser);
            }
        });
        return simpleUserList;
    }

    /**
     * 根据ids查询用户真实名map
     *
     * @param ids ids
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * <AUTHOR> sir
     * @date 2021/11/19
     */
    public static Map<Long, String> getSimpleUserRealNameMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        Map<Long, String> usernameMap = new HashMap<>(ids.size());
        ids.forEach(userId -> {
            SimpleUser simpleUser = handleSimpleUser(userId);
            if (Objects.nonNull(simpleUser)) {
                usernameMap.put(simpleUser.getUserId(), simpleUser.getRealname());
            } else {
                usernameMap.put(userId, "");
            }
        });
        return usernameMap;
    }

    /**
     * 处理SimpleUser
     *
     * @param userId 用户id
     * @return com.kakarote.core.feign.admin.entity.SimpleUser
     * <AUTHOR> sir
     * @date 2021/11/19
     */
    private static SimpleUser handleSimpleUser(Long userId) {
        SimpleUser simpleUser = ME.userCache.get(userId);
        if (Objects.isNull(simpleUser)) {
            simpleUser = ME.adminService.queryUserById(userId).getData();
            if (Objects.nonNull(simpleUser)) {
                ME.userCache.put(userId, simpleUser);
            } else {
                return null;
            }
        }
        return simpleUser;
    }

    /**
     * 根据部门ID获取部门名称，使用，号合并
     *
     * @param deptIds deptIds
     * @return data
     */
    public static <T> List<String> getDeptNameList(List<T> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> stringList = new ArrayList<>(deptIds.size());
        for (T obj : deptIds) {
            String name = getDeptName(TypeUtils.castToLong(obj));
            if (!"".equals(name)) {
                stringList.add(name);
            }
        }
        return stringList;
    }

    /**
     * 根据部门ID获取部门名称
     *
     * @param deptId 部门ID
     * @return data
     */
    public static String getDeptName(Long deptId) {
        if (deptId == null) {
            return "";
        }
        String name = ME.deptCache.get(deptId);
        if (name == null) {
            name = ME.adminService.queryDeptName(deptId).getData();
            ME.deptCache.put(deptId, name);
        }
        return name;
    }

    /**
     * 查询该用户下级的用户
     *
     * @param userId 用户ID 0代表全部
     * @return data
     */
    public static List<Long> queryChildUserId(Long userId) {
        return ME.adminService.queryChildUserId(userId).getData();
    }

    /**
     * 查询部门下属部门
     *
     * @param deptId 上级ID
     * @return data
     */
    public static List<Long> queryChildDeptId(Long deptId) {
        return ME.adminService.queryChildDeptId(deptId).getData();
    }
}
