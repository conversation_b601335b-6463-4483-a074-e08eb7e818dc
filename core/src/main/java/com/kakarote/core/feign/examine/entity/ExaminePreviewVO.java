package com.kakarote.core.feign.examine.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/23
 */
@Data
@ApiModel("审批预览VO")
public class ExaminePreviewVO {


    @ApiModelProperty(value = "撤回之后重新审核操作 1 从第一层开始 2 从拒绝的层级开始")
    private Integer recheckType;

    @ApiModelProperty(value = "审批管理员列表")
    private List<Long> managerList;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty("流程信息")
    private List<ExamineFlowVO>  examineFlowList;

}
