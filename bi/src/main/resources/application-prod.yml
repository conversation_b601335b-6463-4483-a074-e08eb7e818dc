spring:
  redis:
    host: **************
    port: 16379
    password: Unionman666#
    database: 12
    lettuce:
      pool:
        max-active: 300
  datasource:
    url: ******************************************************************************************************************************************************************************************
    username: root
    password: password
    druid:
      testWhileIdle: true
      validationQuery: select 1
mybatis-plus:
  mapper-locations: classpath:/mapper/${bi.xmlPath}/*.xml
bi: #mysql配置详见application-test.yml
  clickhouse: false
  xmlPath: xml
