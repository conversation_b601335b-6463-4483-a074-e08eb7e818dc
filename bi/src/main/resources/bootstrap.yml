server:
  port: 0
spring:
  profiles:
    active: core,dev
  application:
    name: bi
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 113.45.141.212:8848
        file-extension: yaml
        prefix: bi
      discovery:
        enabled: true
        server-addr: 113.45.141.212:8848
    sentinel:
      filter:
        enabled: true
      transport:
        dashboard: 113.45.141.212:8079
      datasource:
        ds1:
          nacos:
            server-addr: 113.45.141.212:8848
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow