package com.kakarote.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.kakarote.bi.entity.VO.BiCustomerAnalyseVO;
import com.kakarote.bi.entity.VO.BiRankCountVO;
import com.kakarote.bi.entity.VO.BiRankMoneyVO;
import com.kakarote.bi.service.BiRankService;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.ExcelParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/biRanking")
@Api(tags = "商业智能排行榜接口")
@Slf4j
public class BiRankController {

    @Autowired
    private BiRankService biRankService;

    @ApiOperation("城市分布分析")
    @PostMapping("/addressAnalyse")
    public Result<List<BiCustomerAnalyseVO>> addressAnalyse(@RequestBody BiEntityParams biParams) {
        List<BiCustomerAnalyseVO> objectList = biRankService.addressAnalyse(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户行业分析")
    @PostMapping("/portrait")
    public Result<List<BiCustomerAnalyseVO>> portrait(@RequestBody BiEntityParams biParams) {
        List<BiCustomerAnalyseVO> objectList = biRankService.portrait(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户级别分析")
    @PostMapping("/portraitLevel")
    public Result<List<BiCustomerAnalyseVO>> portraitLevel(@RequestBody BiEntityParams biParams) {
        List<BiCustomerAnalyseVO> objectList = biRankService.portraitLevel(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户来源分析")
    @PostMapping("/portraitSource")
    public Result<List<BiCustomerAnalyseVO>> portraitSource(@RequestBody BiEntityParams biParams) {
        List<BiCustomerAnalyseVO> objectList = biRankService.portraitSource(biParams);
        return Result.ok(objectList);
    }


    @ApiOperation("合同金额排行榜")
    @PostMapping("/contractRanKing")
    public Result<List<BiRankMoneyVO>> contractRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankMoneyVO> objectList = biRankService.contractRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("合同金额排行榜导出")
    @PostMapping("/contractRanKingExport")
    public void contractRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankMoneyVO> objectList = biRankService.contractRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "签订人"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("money", "合同金额（元）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "合同金额排行榜";
            }
        }, dataList);
    }

    @ApiOperation("回款金额排行榜")
    @PostMapping("/receivablesRanKing")
    public Result<List<BiRankMoneyVO>> receivablesRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankMoneyVO> objectList = biRankService.receivablesRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("回款金额排行榜导出")
    @PostMapping("/receivablesRanKingExport")
    public void receivablesRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankMoneyVO> objectList = biRankService.receivablesRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "签订人"));
        dataList.add(ExcelParseUtil.toEntity("structureName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("money", "回款金额（元）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "回款金额排行榜";
            }
        }, dataList);
    }


    @ApiOperation("签约合同排行榜")
    @PostMapping("/contractCountRanKing")
    public Result<List<BiRankCountVO>> contractCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> rankCountVOS = biRankService.contractCountRanKing(biParams);
        return Result.ok(rankCountVOS);
    }

    @ApiOperation("签约合同排行榜导出")
    @PostMapping("/contractCountRanKingExport")
    public void contractCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> rankCountVOS = biRankService.contractCountRanKing(biParams);
        List<Map<String, Object>> list = rankCountVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "签订人"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "签约合同数（个）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "签约合同排行";
            }
        }, dataList);
    }


    @ApiOperation("产品销量排行榜")
    @PostMapping("/productCountRanKing")
    public Result<List<BiRankCountVO>> productCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.productCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("产品销量排行榜导出")
    @PostMapping("/productCountRanKingExport")
    public void productCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.productCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "签订人"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("productCount", "产品销量"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "产品销量排行";
            }
        }, dataList);
    }

    @ApiOperation("新增客户数排行榜")
    @PostMapping("/customerCountRanKing")
    public Result<List<BiRankCountVO>> customerCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.customerCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("新增客户数排行榜导出")
    @PostMapping("/customerCountRanKingExport")
    public void customerCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.customerCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "创建人"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "新增客户数（个）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "新增客户数排行";
            }
        }, dataList);
    }

    @ApiOperation("新增联系人排行榜")
    @PostMapping("/contactsCountRanKing")
    public Result<List<BiRankCountVO>> contactsCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.contactsCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("新增联系人排行榜导出")
    @PostMapping("/contactsCountRanKingExport")
    public void contactsCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.contactsCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "创建人"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "新增联系人数（个）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "新增联系人排行";
            }
        }, dataList);
    }


    @ApiOperation("跟进客户数排行榜")
    @PostMapping("/customerRecordCountRanKing")
    public Result<List<BiRankCountVO>> customerRecordCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.customerRecordCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("跟进客户数排行榜导出")
    @PostMapping("/customerRecordCountRanKingExport")
    public void customerRecordCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.customerRecordCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "员工"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "跟进客户数（个）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "跟进客户数排行";
            }
        }, dataList);
    }


    @ApiOperation("跟进次数排行榜")
    @PostMapping("/recordCountRanKing")
    public Result<List<BiRankCountVO>> recordCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.recordCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("跟进次数排行榜导出")
    @PostMapping("/recordCountRanKingExport")
    public void recordCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.recordCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "员工"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "跟进客户数（个）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "跟进次数排行";
            }
        }, dataList);
    }


    @ApiOperation("出差次数排行")
    @PostMapping("/travelCountRanKing")
    public Result<List<BiRankCountVO>> travelCountRanKing(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.travelCountRanKing(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("出差次数排行导出")
    @PostMapping("/travelCountRanKingExport")
    public void travelCountRanKingExport(@RequestBody BiEntityParams biParams) {
        List<BiRankCountVO> objectList = biRankService.travelCountRanKing(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("order", "公司总排名"));
        dataList.add(ExcelParseUtil.toEntity("realname", "员工"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("count", "出差次数（次）"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "出差次数排行";
            }
        }, dataList);
    }

}
