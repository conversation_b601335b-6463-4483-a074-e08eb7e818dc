package com.kakarote.bi.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("crm自定义字段BO")
@Getter
@Setter
public class CrmFieldBO {

    @ApiModelProperty("自定义字段ID")
    private Long fieldId;

    @ApiModelProperty("自定义字段名称")
    private String fieldName;

    @ApiModelProperty("选项列表")
    private String options;

    @Override
    public String toString() {
        return "CrmFieldBO{" +
                "fieldId=" + fieldId +
                ", fieldName='" + fieldName + '\'' +
                ", options='" + options + '\'' +
                '}';
    }
}
