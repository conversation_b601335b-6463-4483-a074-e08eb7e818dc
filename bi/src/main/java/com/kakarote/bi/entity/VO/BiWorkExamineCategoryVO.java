package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("办公审批分析 审批类型")
@Data
public class BiWorkExamineCategoryVO implements Serializable {

    @ApiModelProperty("审批名称")
    private String title;

    @ApiModelProperty("审批类型")
    private Integer type;

    @ApiModelProperty("审批ID")
    private String categoryId;
}
