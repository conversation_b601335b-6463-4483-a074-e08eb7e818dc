package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@ApiModel("BI公海客户分析VO")
@Getter
@Setter
public class BiCustomerPoolVO implements Serializable {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户昵称")
    private String realname;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("放入公海数量")
    private Integer putInNum;

    @ApiModelProperty("从公海领取数量")
    private Integer receiveNum;

    @ApiModelProperty("日期类型")
    private String type;

    @Override
    public String toString() {
        return "BiCustomerPoolVO{" +
                "userId=" + userId +
                ", realname='" + realname + '\'' +
                ", deptName='" + deptName + '\'' +
                ", putInNum=" + putInNum +
                ", receiveNum=" + receiveNum +
                ", type='" + type + '\'' +
                '}';
    }
}
