package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("月份金额VO")
public class BiMonthReceivedMoneyVO implements Serializable {

    @ApiModelProperty("业务人员月份内完成金额")
    private BigDecimal receivedMoney;

    @ApiModelProperty("月份")
    private Integer month;
    @ApiModelProperty("月份名称")
    private String monthName;

    @ApiModelProperty("负责人")
    private Long ownerUserId;

    @ApiModelProperty("完成率")
    private BigDecimal rate;

    @ApiModelProperty("实际完成金额")
    private BigDecimal money;
    @ApiModelProperty("业绩目标金额")
    private BigDecimal achievement;
}
