package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("员工业绩分析-合同汇总分析")
@Getter
@Setter
public class BiTotalContractVO implements Serializable {

    @ApiModelProperty("时间类型")
    private String type;

    @ApiModelProperty("合同金额")
    private BigDecimal contractMoney;

    @ApiModelProperty("合同数量")
    private Integer contractNum;

    @ApiModelProperty("回款金额")
    private BigDecimal receivablesMoney;

    @Override
    public String toString() {
        return "BiTotalContractVO{" +
                "type='" + type + '\'' +
                ", contractMoney=" + contractMoney +
                ", contractNum=" + contractNum +
                ", receivablesMoney=" + receivablesMoney +
                '}';
    }
}
