package com.kakarote.bi.service.dwhImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.kakarote.bi.common.BiConst;
import com.kakarote.bi.common.enums.BiCKDateEnum;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.common.enums.MonthEnum;
import com.kakarote.bi.entity.VO.*;
import com.kakarote.bi.mapper.BiInstrumentMapper;
import com.kakarote.bi.service.BiInstrumentService;
import com.kakarote.core.common.enums.DataAuthEnum;
import com.kakarote.core.common.enums.DateFilterEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "true")
@Service
public class BiInstrumentServiceImpl implements BiInstrumentService {


    @Autowired
    private BiInstrumentMapper biInstrumentMapper;

    @Autowired
    @Lazy
    private AdminService adminService;

    /**
     * 查询仪表盘销售简报
     *
     * @param biParams 查询参数
     * @return data
     */
    @Override
    public List<BiBulletinVO> queryBulletin(BiEntityParams biParams) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biParams);

        //因为仪表盘这块查询逻辑与通用逻辑不一样，所以这块方法需要重写
        BiParamsUtil.BiTimeEntity timeEntity = analyzeDate(biParams);
        List<BiBulletinVO> bulletinVOS = biInstrumentMapper.queryBulletin(timeEntity,
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.CUSTOMER_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.CONTACTS_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.BUSINESS_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.CONTRACT_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.RECEIVABLES_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.RECORD_LIST.getMenuId())
        );
        return bulletinVOS;
    }

    @Override
    public List<BiSalesTrendVO> salesTrend(BiEntityParams biParams) {
        if (Objects.equals(biParams.getDataType(), DataAuthEnum.CUSTOM) && CollUtil.isNotEmpty(biParams.getUserList()) && CollUtil.isNotEmpty(biParams.getDeptList())) {
            //当既选择员工也选择部门时，目标及完成情况图标不显示数据
            return Collections.emptyList();
        }
        String generateKey = biParams.generateKey();
        int label = Objects.equals(1, biParams.getType()) ? 1 : 2;
        Long menuId = Objects.equals(1, biParams.getType()) ? BiMenuEnum.CONTRACT_LIST.getMenuId() : BiMenuEnum.RECEIVABLES_LIST.getMenuId();
        //因为仪表盘这块查询逻辑与通用逻辑不一样，所以这块方法需要重写
        BiParamsUtil.BiTimeEntity biTimeEntity = analyzeSalesTrendDate(biParams, menuId);
        List<BiSalesTrendVO> biSalesTrendVOS = biInstrumentMapper.salesTrend(biTimeEntity, label);
        //先查出来金额，再根据时间类型设置业绩目标
        switch (biParams.getDateFilter()) {
            case CUSTOM:
                break;
            case TODAY:
            case YESTERDAY:
            case TOMORROW: {
                int month = DateUtil.month(biTimeEntity.getBeginDate());
                MonthEnum monthEnum = MonthEnum.valueOf(month + 1);
                int year = DateUtil.year(biTimeEntity.getBeginDate());
                //查询本月业绩目标
                Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Collections.singletonList(year), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
                int day = 1;
                BigDecimal bigDecimal;
                if (!mapMap.isEmpty() && mapMap.containsKey(year) && mapMap.get(year).containsKey(monthEnum.getName())) {
                    bigDecimal = mapMap.get(year).get(monthEnum.getName());
                } else {
                    bigDecimal = new BigDecimal("0");
                }
                for (BiSalesTrendVO salesTrendVO : biSalesTrendVOS) {
                    salesTrendVO.setType(monthEnum.getValue() + "-" + String.format("%02d", day++));
                    //业绩目标金额为当前月份目标除以当前月份天数
                    salesTrendVO.setAchievement(bigDecimal.divide(new BigDecimal(biSalesTrendVOS.size()), 2, RoundingMode.HALF_UP));
                }
                break;
            }
            case WEEK:
            case LAST_WEEK:
            case NEXT_WEEK: {
                int month = DateUtil.month(biTimeEntity.getBeginDate());
                MonthEnum monthEnum = MonthEnum.valueOf(month + 1);
                int year = DateUtil.year(biTimeEntity.getBeginDate());
                Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Collections.singletonList(year), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
                BigDecimal bigDecimal;
                if (!mapMap.isEmpty() && mapMap.containsKey(year) && mapMap.get(year).containsKey(monthEnum.getName())) {
                    bigDecimal = mapMap.get(year).get(monthEnum.getName());
                } else {
                    bigDecimal = new BigDecimal("0");
                }
                for (BiSalesTrendVO salesTrendVO : biSalesTrendVOS) {
                    DateTime dateTime = DateUtil.parseDateTime(salesTrendVO.getType());
                    DateTime endOfWeek = DateUtil.endOfWeek(dateTime);
                    if (dateTime.dayOfMonth() < 7) {
                        //当前时间存在跨月的情况,本月与上月
                        salesTrendVO.setType(DateUtil.beginOfMonth(dateTime).toString("MM-dd") + "~" + endOfWeek.toString("MM-dd"));
                        //本月业绩目标除以本月天数再乘以本月开始天数
                        salesTrendVO.setAchievement(bigDecimal.divide(new BigDecimal(DateUtil.endOfMonth(dateTime).dayOfMonth() + ""), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(dateTime.dayOfMonth() + "")));
                    } else if (endOfWeek.month() != month) {
                        //当前时间存在跨月的情况,本月与下月
                        salesTrendVO.setType(DateUtil.beginOfWeek(dateTime).toString("MM-dd") + "~" + DateUtil.endOfMonth(biTimeEntity.getBeginDate()).toString("MM-dd"));
                        //本月业绩目标除以本月天数再乘以7
                        salesTrendVO.setAchievement(bigDecimal.divide(new BigDecimal(DateUtil.endOfMonth(dateTime).dayOfMonth() + ""), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(DateUtil.dayOfWeek(biTimeEntity.getBeginDate()) - 1 + "")));
                    } else {
                        salesTrendVO.setType(DateUtil.beginOfWeek(dateTime).toString("MM-dd") + "~" + endOfWeek.toString("MM-dd"));
                        //本月业绩目标除以本月天数再乘以7
                        salesTrendVO.setAchievement(bigDecimal.divide(new BigDecimal(DateUtil.endOfMonth(dateTime).dayOfMonth() + ""), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("7")));
                    }
                }
                break;
            }
            case MONTH:
            case LAST_MONTH:
            case NEXT_MONTH: {
                int year = DateUtil.year(biTimeEntity.getBeginDate());
                Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Collections.singletonList(year), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
                Map<String, BigDecimal> yearMap;
                if (mapMap.containsKey(year)) {
                    yearMap = mapMap.get(year);
                } else {
                    yearMap = new HashMap<>();
                }
                for (BiSalesTrendVO salesTrendVO : biSalesTrendVOS) {
                    DateTime dateTime = DateUtil.parseDateTime(salesTrendVO.getType());
                    salesTrendVO.setType(dateTime.toString("yyyy-MM"));
                    salesTrendVO.setAchievement(yearMap.isEmpty() ? BigDecimal.ZERO : yearMap.get(MonthEnum.valueOf(dateTime.month() + 1).getName()));
                }
                break;
            }
            case QUARTER:
            case LAST_QUARTER:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_QUARTER) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), -3));
                }
            case NEXT_QUARTER: {
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_QUARTER) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), 3));
                }
                int year = DateUtil.year(biTimeEntity.getBeginDate());
                Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Arrays.asList(year, year + 1,year + 2), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
                for (BiSalesTrendVO salesTrendVO : biSalesTrendVOS) {
                    DateTime endTime = DateUtil.parseDateTime(salesTrendVO.getType());
                    DateTime beginTime = DateUtil.beginOfQuarter(endTime);
                    String detail = beginTime.toString("MM-dd") + "~" + endTime.toString("MM-dd");
                    salesTrendVO.setType(beginTime.year() + "年" + beginTime.quarter() + "季度 " + detail);
                    if (mapMap.containsKey(beginTime.year())) {
                        Map<String, BigDecimal> decimalMap = mapMap.get(beginTime.year());
                        int month = beginTime.month() + 1;
                        //3个月的业绩目标相加
                        BigDecimal bigDecimal = decimalMap.get(MonthEnum.valueOf(month).getName()).add(decimalMap.get(MonthEnum.valueOf(month + 1).getName())).add(decimalMap.get(MonthEnum.valueOf(month + 2).getName()));
                        salesTrendVO.setAchievement(bigDecimal);
                    } else {
                        salesTrendVO.setAchievement(BigDecimal.ZERO);
                    }

                }
                break;
            }
            case YEAR:
            case LAST_YEAR:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_YEAR) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), -12));
                }
            case NEXT_YEAR: {
                int year = DateUtil.year(biTimeEntity.getBeginDate());
                //展示最近五年
                Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Arrays.asList(year, year + 1, year + 2, year + 3, year + 4), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
                for (BiSalesTrendVO salesTrendVO : biSalesTrendVOS) {
                    DateTime dateTime = DateUtil.parseDateTime(salesTrendVO.getType());
                    salesTrendVO.setType(dateTime.toString("yyyy"));
                    if (mapMap.containsKey(dateTime.year())) {
                        salesTrendVO.setAchievement(mapMap.get(dateTime.year()).get(MonthEnum.YEAR.getName()));
                    } else {
                        salesTrendVO.setAchievement(BigDecimal.ZERO);
                    }
                }
                break;
            }
            default:
                break;
        }
        return biSalesTrendVOS;
    }

    @Override
    public BiDataInfoVO queryDataInfo(BiEntityParams biParams) {
        Set<Long> allUserList = BiParamsUtil.getAllUserList(biParams);
        BiParamsUtil.BiTimeEntity timeEntity = new BiParamsUtil.BiTimeEntity();
        //用户要根据每个类型取不同数据，只是date
        BiParamsUtil.analyzeDate(timeEntity, biParams);
        AdminConfig adminConfig = adminService.queryFirstConfigByName("expiringContractDays").getData();
        int expiringDay = (adminConfig != null && Objects.equals(1, adminConfig.getStatus())) ? Integer.parseInt(adminConfig.getValue()) : 7;
        BiDataInfoVO dataInfoVO = biInstrumentMapper.queryDataInfo(timeEntity,
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.CUSTOMER_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.BUSINESS_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.CONTRACT_LIST.getMenuId()),
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.RECEIVABLES_LIST.getMenuId()),
                expiringDay,
                BiParamsUtil.filterUser(allUserList, BiMenuEnum.RECORD_LIST.getMenuId()));
        return dataInfoVO;
    }

    /**
     * 查询仪表盘业绩目标功能
     *
     * @param biParams 查询参数
     * @return data
     */
    @Override
    public BiPerformanceVO queryPerformance(BiEntityParams biParams) {
        if (Objects.equals(biParams.getDataType(), DataAuthEnum.CUSTOM) && CollUtil.isNotEmpty(biParams.getUserList()) && CollUtil.isNotEmpty(biParams.getDeptList())) {
            //当既选择员工也选择部门时，目标及完成情况图标不显示数据
            return new BiPerformanceVO(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        BiPerformanceVO biPerformanceVO = new BiPerformanceVO();
        int label = Objects.equals(1, biParams.getType()) ? 1 : 2;
        Long menuId = Objects.equals(1, biParams.getType()) ? BiMenuEnum.CONTRACT_LIST.getMenuId() : BiMenuEnum.RECEIVABLES_LIST.getMenuId();
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        BiParamsUtil.analyzeDate(biTimeEntity, biParams);
        BiParamsUtil.analyzeUserOrDept(biTimeEntity, biParams, menuId);
        BigDecimal money = biInstrumentMapper.queryPerformance(biTimeEntity, label);
        biPerformanceVO.setMoney(money);
        int year = DateUtil.year(biTimeEntity.getBeginDate());
        //查询本月业绩目标
        Map<Integer, Map<String, BigDecimal>> mapMap = biInstrumentMapper.queryAchievementList(Collections.singletonList(year), label, biTimeEntity.getUserIds(), biTimeEntity.getDeptIds());
        //先查出来金额，再根据时间类型设置业绩目标
        switch (biParams.getDateFilter()) {
            case CUSTOM:
                biPerformanceVO.setAchievementMoneys(BigDecimal.ZERO);
                biPerformanceVO.setProportion(BigDecimal.ZERO);
                break;
            case TODAY:
            case YESTERDAY:
            case TOMORROW:
            case WEEK:
            case LAST_WEEK:
            case NEXT_WEEK: {
                MonthEnum monthEnum = MonthEnum.valueOf(DateUtil.month(biTimeEntity.getBeginDate()) + 1);
                int num = 1;
                if (Arrays.asList(DateFilterEnum.WEEK, DateFilterEnum.LAST_WEEK, DateFilterEnum.NEXT_WEEK).contains(biParams.getDateFilter())) {
                    num = 7;
                }
                if (!mapMap.isEmpty() && mapMap.containsKey(year) && mapMap.get(year).containsKey(monthEnum.getName())) {
                    BigDecimal bigDecimal = mapMap.get(year).get(monthEnum.getName());
                    biPerformanceVO.setAchievementMoneys(bigDecimal.divide(new BigDecimal(DateUtil.endOfMonth(new Date()).dayOfMonth()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(num)));
                } else {
                    biPerformanceVO.setAchievementMoneys(BigDecimal.ZERO);
                }
                break;
            }
            case MONTH:
            case LAST_MONTH:
            case NEXT_MONTH: {
                Map<String, BigDecimal> yearMap;
                if (mapMap.containsKey(year)) {
                    yearMap = mapMap.get(year);
                } else {
                    yearMap = new HashMap<>();
                }
                biPerformanceVO.setAchievementMoneys(yearMap.isEmpty() ? BigDecimal.ZERO : yearMap.get(MonthEnum.valueOf(new DateTime().month() + 1).getName()));
                break;
            }
            case QUARTER:
            case LAST_QUARTER:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_QUARTER) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), -3));
                }
            case NEXT_QUARTER: {
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_QUARTER) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), 3));
                }
                DateTime beginTime = DateUtil.beginOfQuarter(new DateTime());
                if (mapMap.containsKey(beginTime.year())) {
                    Map<String, BigDecimal> decimalMap = mapMap.get(beginTime.year());
                    int month = beginTime.month() + 1;
                    //3个月的业绩目标相加
                    BigDecimal bigDecimal = decimalMap.get(MonthEnum.valueOf(month).getName()).add(decimalMap.get(MonthEnum.valueOf(month + 1).getName())).add(decimalMap.get(MonthEnum.valueOf(month + 2).getName()));
                    biPerformanceVO.setAchievementMoneys(bigDecimal);
                } else {
                    biPerformanceVO.setAchievementMoneys(BigDecimal.ZERO);
                }
                break;
            }
            case YEAR:
            case LAST_YEAR:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_YEAR) {
                    biTimeEntity.setBeginDate(DateUtil.offsetMonth(biTimeEntity.getBeginDate(), -12));
                }
            case NEXT_YEAR: {
                if (mapMap.containsKey(year)) {
                    biPerformanceVO.setAchievementMoneys(mapMap.get(year).get(MonthEnum.YEAR.getName()));
                } else {
                    biPerformanceVO.setAchievementMoneys(BigDecimal.ZERO);
                }
                break;
            }
            default:
                break;
        }
        if (biPerformanceVO.getMoney() != null && biPerformanceVO.getAchievementMoneys() != null) {
            //当业绩目标不等于0并且金额不等于0才计算金额
            if (biPerformanceVO.getMoney().intValue() != 0 && biPerformanceVO.getAchievementMoneys().intValue() != 0) {
                biPerformanceVO.setProportion(biPerformanceVO.getMoney().multiply(BiConst.PERCENTAGE).divide(biPerformanceVO.getAchievementMoneys(), 2, RoundingMode.HALF_UP));
            } else {
                biPerformanceVO.setProportion(BigDecimal.ZERO);
            }

        }
        return biPerformanceVO;
    }

    /**
     * 遗忘客户统计
     *
     * @param biParams params
     * @return data
     */
    @Override
    public BiForgottenCustomerVO forgottenCustomerCount(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.CUSTOMER_LIST.getMenuId());
        return biInstrumentMapper.forgottenCustomerCount(biTimeEntity);
    }

    /**
     * 销售简报的时间计算
     *
     * @param biParams 查询参数
     * @return timeEntity
     */
    private BiParamsUtil.BiTimeEntity analyzeDate(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        Calendar calendar = CalendarUtil.calendar();
        Calendar startCalendar, endCalendar;
        switch (biParams.getDateFilter()) {
            case CUSTOM: {
                BiParamsUtil.analyzeDate(biTimeEntity, biParams);
                return biTimeEntity;
            }
            case YESTERDAY:
                //时间往前推一天
                calendar.add(Calendar.DAY_OF_YEAR, -1);
            case TOMORROW:
                if (biParams.getDateFilter() == DateFilterEnum.TOMORROW) {
                    //时间往前推一天
                    calendar.add(Calendar.DAY_OF_YEAR, 1);
                }
            case TODAY:
                endCalendar = CalendarUtil.endOfDay(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.DAY_OF_YEAR, -6);
                startCalendar = CalendarUtil.beginOfDay(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toDate.getValue());
                break;
            case LAST_WEEK:
                //时间往前推一周
                calendar.add(Calendar.WEEK_OF_YEAR, -1);
            case NEXT_WEEK:
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_WEEK) {
                    //时间往后推一周
                    calendar.add(Calendar.WEEK_OF_YEAR, 1);
                }
            case WEEK:
                endCalendar = CalendarUtil.endOfWeek(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.WEEK_OF_YEAR, -6);
                startCalendar = CalendarUtil.beginOfWeek(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toMonday.getValue());
                break;
            case LAST_MONTH:
                calendar.add(Calendar.MONTH, -1);
            case NEXT_MONTH:
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_MONTH) {
                    calendar.add(Calendar.MONTH, 1);
                }
            case MONTH:
                endCalendar = CalendarUtil.endOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.MONTH, -6);
                startCalendar = CalendarUtil.beginOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toStartOfMonth.getValue());
                break;
            case LAST_QUARTER:
                calendar.add(Calendar.MONTH, -3);
            case NEXT_QUARTER:
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_QUARTER) {
                    calendar.add(Calendar.MONTH, 3);
                }
            case QUARTER:
                endCalendar = CalendarUtil.endOfQuarter(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.MONTH, -18);
                startCalendar = CalendarUtil.beginOfQuarter(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toStartOfQuarter.getValue());
                break;
            case NEXT_YEAR:
                calendar.add(Calendar.YEAR, 1);
            case LAST_YEAR:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_YEAR) {
                    calendar.add(Calendar.YEAR, -1);
                }
            case YEAR:
                endCalendar = CalendarUtil.endOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.YEAR, -6);
                startCalendar = CalendarUtil.beginOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toYear.getValue());
                break;
            default:
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
        biTimeEntity.setBeginDate(startCalendar.getTime());
        biTimeEntity.setEndDate(endCalendar.getTime());
        return biTimeEntity;
    }

    /**
     * 销售趋势分析的时间构建
     *
     * @param biParams 查询参数
     * @param menuId   菜单ID
     * @return timeEntity
     */
    private BiParamsUtil.BiTimeEntity analyzeSalesTrendDate(BiEntityParams biParams, Long menuId) {
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        Calendar calendar = CalendarUtil.calendar();
        //过滤员工以及部门信息
        BiParamsUtil.analyzeUserOrDept(biTimeEntity, biParams, menuId);
        Calendar startCalendar, endCalendar;
        //根据时间类型筛选开始时间和结束时间
        switch (biParams.getDateFilter()) {
            case CUSTOM: {
                BiParamsUtil.analyzeDate(biTimeEntity, biParams);
                return biTimeEntity;
            }
            case YESTERDAY:
            case TOMORROW:
            case TODAY:
                startCalendar = CalendarUtil.beginOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                endCalendar = CalendarUtil.endOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toDate.getValue());
                break;
            case LAST_WEEK:
            case NEXT_WEEK:
            case WEEK:
                startCalendar = CalendarUtil.beginOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                endCalendar = CalendarUtil.endOfMonth(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toMonday.getValue());
                break;
            case LAST_MONTH:
            case NEXT_MONTH:
            case MONTH:
                startCalendar = CalendarUtil.beginOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                endCalendar = CalendarUtil.endOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toStartOfMonth.getValue());
                break;
            case LAST_QUARTER:
                calendar.add(Calendar.MONTH, -3);
            case NEXT_QUARTER:
                if (biParams.getDateFilter() == DateFilterEnum.NEXT_QUARTER) {
                    calendar.add(Calendar.MONTH, 3);
                }
            case QUARTER:
                endCalendar = CalendarUtil.endOfQuarter(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.MONTH, -21);
                startCalendar = CalendarUtil.beginOfQuarter(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toStartOfQuarter.getValue());
                break;
            case NEXT_YEAR:
                calendar.add(Calendar.YEAR, -1);
            case LAST_YEAR:
                if (biParams.getDateFilter() == DateFilterEnum.LAST_YEAR) {
                    calendar.add(Calendar.MONTH, 1);
                }
            case YEAR:
                endCalendar = CalendarUtil.endOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                calendar.add(Calendar.YEAR, -4);
                startCalendar = CalendarUtil.beginOfYear(CalendarUtil.calendar(calendar.getTimeInMillis()));
                biTimeEntity.setDateFormat(BiCKDateEnum.toYear.getValue());
                break;
            default:
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
        biTimeEntity.setBeginDate(startCalendar.getTime());
        biTimeEntity.setEndDate(endCalendar.getTime());
        return biTimeEntity;
    }

}
