package com.kakarote.bi.service;

import com.kakarote.bi.entity.VO.*;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.util.List;

/**
 * crm仪表盘相关的service
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
public interface BiInstrumentService {


    /**
     * 查询仪表盘销售简报
     *
     * @param biParams 查询参数
     * @return data
     */
    List<BiBulletinVO> queryBulletin(BiEntityParams biParams);


    /**
     * 查询仪表盘回款金额目标及完成情况以及合同金额目标及完成情况
     *
     * @param biParams 查询参数
     * @return data
     */
    List<BiSalesTrendVO> salesTrend(BiEntityParams biParams);

    /**
     * 查询仪表盘数据汇总功能
     *
     * @param biParams 查询参数
     * @return data
     */
    BiDataInfoVO queryDataInfo(BiEntityParams biParams);


    /**
     * 查询仪表盘业绩目标功能
     *
     * @param biParams 查询参数
     * @return data
     */
    BiPerformanceVO queryPerformance(BiEntityParams biParams);


    /**
     * 遗忘客户统计
     *
     * @param biParams params
     * @return data
     */
    BiForgottenCustomerVO forgottenCustomerCount(BiEntityParams biParams);
}
