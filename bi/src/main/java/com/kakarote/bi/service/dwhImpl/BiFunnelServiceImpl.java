package com.kakarote.bi.service.dwhImpl;

import com.kakarote.bi.common.BiConst;
import com.kakarote.bi.common.enums.BiCKDateEnum;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.entity.BO.BiSellFunnelBO;
import com.kakarote.bi.entity.VO.BiSellFunnelVO;
import com.kakarote.bi.mapper.BiFunnelMapper;
import com.kakarote.bi.service.BiFunnelService;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "true")
public class BiFunnelServiceImpl implements BiFunnelService {

    @Autowired
    private BiFunnelMapper biFunnelMapper;

    /**
     * 销售漏斗
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiSellFunnelVO> sellFunnel(BiSellFunnelBO biParams) {
        if (biParams.getTypeId() == null) {
            throw new CrmException();
        }
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_SELL_FUNNEL.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiCKDateEnum.getCKDate(biParams.getDateFilter()));
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiSellFunnelVO> biSellFunnelVOS = biFunnelMapper.sellFunnel(timeEntity, biParams.getSortField(), sortType, biParams.getTypeId());

        Map<Integer, BiSellFunnelVO> map = biFunnelMapper.queryEndBusiness(timeEntity, biParams.getTypeId());
        //无论有没有赢单输单 都要返回
        int indexFor = 3;
        for (int i = 1; i < indexFor; i++) {
            if (map.containsKey(i)) {
                BiSellFunnelVO biSellFunnelVO = map.get(i);
                biSellFunnelVO.setSettingId(0L);
                biSellFunnelVOS.add(biSellFunnelVO);
            } else {
                BiSellFunnelVO biSellFunnelVO = new BiSellFunnelVO();
                biSellFunnelVO.setSettingName(i == 1 ? "赢单" : "输单");
                biSellFunnelVO.setIsEnd(i == 1 ? 1 : 2);
                biSellFunnelVO.setSettingId(0L);
                biSellFunnelVO.setBusinessNum(0);
                biSellFunnelVO.setBusinessMoney(BigDecimal.ZERO);
                biSellFunnelVOS.add(biSellFunnelVO);
            }
        }
        return biSellFunnelVOS;
    }

    /**
     * 新增商机分析图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiSellFunnelVO> addBusinessAnalyze(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_SELL_FUNNEL.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiCKDateEnum.getCKDate(biParams.getDateFilter()));
        return biFunnelMapper.addBusinessAnalyze(timeEntity);
    }

    /**
     * 商机转化率分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiSellFunnelVO> win(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_SELL_FUNNEL.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiCKDateEnum.getCKDate(biParams.getDateFilter()));
        List<BiSellFunnelVO> funnelVOList = biFunnelMapper.win(timeEntity);
        for (BiSellFunnelVO sellFunnelVO : funnelVOList) {
            if (sellFunnelVO.getBusinessNum() != 0) {
                //设置客户成交率
                sellFunnelVO.setBusinessConversion(new BigDecimal(sellFunnelVO.getWinBusinessNum()).divide(new BigDecimal(sellFunnelVO.getBusinessNum()), 4, RoundingMode.HALF_UP).multiply(BiConst.PERCENTAGE));
            } else {
                sellFunnelVO.setBusinessConversion(BigDecimal.ZERO);
            }

        }
        return funnelVOList;
    }
}
