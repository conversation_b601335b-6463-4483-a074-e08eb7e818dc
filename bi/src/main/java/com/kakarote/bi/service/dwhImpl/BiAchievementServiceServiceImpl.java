package com.kakarote.bi.service.dwhImpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.kakarote.bi.common.BiConst;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.common.enums.MonthEnum;
import com.kakarote.bi.entity.BO.BiAchievementBO;
import com.kakarote.bi.entity.VO.BiAchievementVO;
import com.kakarote.bi.entity.VO.BiMonthReceivedMoneyVO;
import com.kakarote.bi.mapper.BiAchievementMapper;
import com.kakarote.bi.service.BiAchievementService;
import com.kakarote.core.common.enums.DataAuthEnum;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "true")
public class BiAchievementServiceServiceImpl implements BiAchievementService {

    @Autowired
    private BiAchievementMapper biAchievementMapper;

    @Autowired
    @Lazy
    private AdminService adminService;

    /**
     * 获取商业智能业绩目标完成情况
     *
     * <AUTHOR>
     */
    @Override
    public List<BiAchievementVO> taskCompleteStatistics(BiAchievementBO biAchievementBO) {
        Date year = biAchievementBO.getStartDate();
        Integer isUser = biAchievementBO.getIsUser();
        if (Objects.equals(1, isUser)) {
            Set<Long> userIds;
            if (CollectionUtil.isNotEmpty(biAchievementBO.getUserList())) {
                userIds = new HashSet<>(biAchievementBO.getUserList());
            } else {
                biAchievementBO.setDataType(DataAuthEnum.ALL);
                userIds = BiParamsUtil.getAllUserList(biAchievementBO);
            }
            List<Long> longList = BiParamsUtil.filterUser(userIds, BiMenuEnum.BI_ACHIEVEMENT.getMenuId());
            biAchievementBO.setUserList(longList);
            biAchievementBO.setType(3);
        } else {
            List<Long> deptIds = null;
            if (CollectionUtil.isEmpty(biAchievementBO.getDeptList())) {
                Long deptId = UserUtil.getUser().getDeptId();
                deptIds = UserCacheUtil.queryChildDeptId(deptId);
                deptIds.add(deptId);
            } else {
                deptIds = BiParamsUtil.filterDeptId(biAchievementBO.getDeptList());
            }
            biAchievementBO.setDeptList(deptIds);
            biAchievementBO.setType(2);
        }

        Map<Long, JSONObject> BiAchievementVOs = biAchievementMapper.taskCompleteStatistics(biAchievementBO, DateUtil.year(year));
        if (CollectionUtil.isEmpty(BiAchievementVOs)) {
            return Collections.emptyList();
        }

        if (Objects.equals(0, isUser)) {
            List<Long> userIdList = adminService.queryUserByDeptIds(BiAchievementVOs.keySet()).getData();
            biAchievementBO.setUserList(userIdList);
        }

        BigDecimal oneHundredDecimal = BiConst.PERCENTAGE;
        biAchievementBO.setStartDate(DateUtil.beginOfYear(year));
        biAchievementBO.setEndDate(DateUtil.endOfYear(year));
        List<BiMonthReceivedMoneyVO> userReceivedMoneyList = biAchievementMapper.taskCompleteStatisticsRate(biAchievementBO);
        userReceivedMoneyList.removeIf(item -> item.getOwnerUserId() == null);
        //根据objId进行分组
        Map<Long, List<BiMonthReceivedMoneyVO>> biMonthReceiveMoneyVos = userReceivedMoneyList.stream()
                .collect(Collectors.groupingBy(data -> Objects.equals(0, isUser) ?
                        UserCacheUtil.getSimpleUser(data.getOwnerUserId()).getDeptId() : data.getOwnerUserId()));

        List<BiAchievementVO> list = new ArrayList<>();
        for (Map.Entry<Long, JSONObject> objectEntry : BiAchievementVOs.entrySet()) {
            JSONObject value = objectEntry.getValue();
            String objName = "";
            List<BiMonthReceivedMoneyVO> receivedMoneyVOS = biMonthReceiveMoneyVos.get(value.getLong("objId"));
            Object objId = value.remove("objId");
            if (Objects.equals(0, isUser)) {
                objName = UserCacheUtil.getDeptName(TypeUtils.castToLong(objId));
            } else {
                objName = UserCacheUtil.getUserName(TypeUtils.castToLong(objId));
            }
            BiAchievementVO BiAchievementVO = new BiAchievementVO();
            List<BiMonthReceivedMoneyVO> biMonthReceivedMoneyVO = new ArrayList<>();
            //12个月
            value.forEach((k, v) -> {
                //具体月份
                MonthEnum monthEnum = MonthEnum.parseName(k);
                BigDecimal receiveMoney = new BigDecimal("0");
                Integer month = Integer.valueOf(monthEnum.getValue());

                if (null != receivedMoneyVOS) {
                    List<BigDecimal> bigDecimals = receivedMoneyVOS.stream()
                            .filter(item -> Objects.equals(item.getMonth(), month) && null != item.getReceivedMoney())
                            .map(BiMonthReceivedMoneyVO::getReceivedMoney).collect(Collectors.toList());
                    bigDecimals.removeIf(Objects::isNull);
                    //把月份金额相加
                    for (BigDecimal bigDecimal : bigDecimals) {
                        receiveMoney = receiveMoney.add(bigDecimal);
                    }
                }
                //当前月目标金额
                BigDecimal achievementBigDecimal = TypeUtils.castToBigDecimal(v);

                //计算完成率
                BigDecimal rage = receiveMoney.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : receiveMoney.multiply(oneHundredDecimal).divide(achievementBigDecimal, 2, RoundingMode.HALF_UP);
                BiMonthReceivedMoneyVO biMonthReceivedMoney = new BiMonthReceivedMoneyVO();
                biMonthReceivedMoney.setMonthName(monthEnum.getName());
                biMonthReceivedMoney.setRate(rage);
                biMonthReceivedMoney.setAchievement(achievementBigDecimal);
                biMonthReceivedMoney.setMoney(receiveMoney);
                biMonthReceivedMoney.setMonthName(monthEnum.getRemark());
                biMonthReceivedMoney.setMonth(month);
                biMonthReceivedMoneyVO.add(biMonthReceivedMoney);
            });
            biMonthReceivedMoneyVO.sort(Comparator.comparing(BiMonthReceivedMoneyVO::getMonth));
            BiAchievementVO.setBiMonthReceivedMoneyVOS(biMonthReceivedMoneyVO);
            BiAchievementVO.setName(objName);
            list.add(BiAchievementVO);
        }
        return list;
    }


    @Override
    public List<Map<String, Object>> taskCompleteStatisticsExport(BiAchievementBO biAchievementBO) {
        List<BiAchievementVO> recordList = taskCompleteStatistics(biAchievementBO);
        List<Map<String, Object>> list = new ArrayList<>();
        //bigDecimal类型默认使用string初始化
        recordList.stream().map(BiAchievementVO::getBiMonthReceivedMoneyVOS).forEach(taskList -> {
            List<String> quarterMonth = Arrays.asList(MonthEnum.MARCH.getValue(), MonthEnum.JUNE.getValue(), MonthEnum.SEPTEMBER.getValue(), MonthEnum.DECEMBER.getValue());
            BigDecimal quarterMoney = new BigDecimal("0.00");
            BigDecimal quarterAchievement = new BigDecimal("0.00");
            for (BiMonthReceivedMoneyVO task : taskList) {
                quarterMoney = quarterMoney.add(task.getMoney());
                quarterAchievement = quarterAchievement.add(task.getAchievement());
                Integer month = task.getMonth();
                Map<String, Object> map = BeanUtil.beanToMap(task);
                list.add(map);

                MonthEnum monthEnum = MonthEnum.valueOf(month);
                if (quarterMonth.contains(monthEnum.getValue())) {
                    String quarterName = "";
                    switch (monthEnum) {
                        case MARCH:
                            quarterName = "第一季度";
                            break;
                        case JUNE:
                            quarterName = "第二季度";
                            break;
                        case SEPTEMBER:
                            quarterName = "第三季度";
                            break;
                        case DECEMBER:
                            quarterName = "第四季度";
                            break;
                        default:
                            break;
                    }
                    BigDecimal rate = quarterMoney.divide(quarterAchievement, 4, RoundingMode.HALF_UP).multiply(BiConst.PERCENTAGE);
                    JSONObject quarterTask = new JSONObject().fluentPut("name", "").fluentPut("monthName", quarterName).fluentPut("money", quarterMoney).fluentPut("achievement", quarterAchievement).fluentPut("rate", rate);
                    list.add(quarterTask);

                    //处理年度报表
                    if (monthEnum == MonthEnum.DECEMBER) {
                        BigDecimal totalMoney = new BigDecimal("0.00");
                        BigDecimal totalAchievement = new BigDecimal("0.00");
                        for (BiMonthReceivedMoneyVO biMonthReceivedMoneyVO : taskList) {
                            totalMoney = totalMoney.add(biMonthReceivedMoneyVO.getMoney());
                            totalAchievement = totalAchievement.add(biMonthReceivedMoneyVO.getAchievement());
                        }
                        BigDecimal totalRate = totalMoney.divide(totalAchievement, 4, RoundingMode.HALF_UP).multiply(BiConst.PERCENTAGE);
                        JSONObject yearTask = new JSONObject().fluentPut("name", "").fluentPut("monthName", "全年").fluentPut("money", totalMoney).fluentPut("achievement", totalAchievement).fluentPut("rate", totalRate);
                        list.add(yearTask);
                    }
                    quarterMoney = new BigDecimal("0.00");
                    quarterAchievement = new BigDecimal("0.00");
                }
            }
        });
        return list;
    }
}
