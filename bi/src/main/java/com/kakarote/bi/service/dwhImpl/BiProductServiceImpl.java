package com.kakarote.bi.service.dwhImpl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.entity.VO.BiProductStatisticsVO;
import com.kakarote.bi.mapper.BiProductMapper;
import com.kakarote.bi.service.BiProductService;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "true")
public class BiProductServiceImpl implements BiProductService {

    @Autowired
    private BiProductMapper biProductMapper;

    /**
     * 查询产品销售情况统计
     *
     * @param biParams biParams
     * @return data
     */
    @Override
    public BasePage<BiProductStatisticsVO> queryProductSell(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_PRODUCT_STATS.getMenuId());
        if (CollectionUtil.isEmpty(timeEntity.getUserIds())) {
            BasePage<BiProductStatisticsVO> basePage = new BasePage<>();
            basePage.setExtraData(new JSONObject());
            return basePage;
        }
        BiProductStatisticsVO productStatisticsVO = biProductMapper.queryProductSellCount(timeEntity);
        if (productStatisticsVO == null || Objects.equals(0, productStatisticsVO.getCount())) {
            BasePage<BiProductStatisticsVO> basePage = new BasePage<>();
            basePage.setExtraData(productStatisticsVO);
            return basePage;
        }
        Integer page = biParams.getPage() > 0 ? (biParams.getPage() - 1) * biParams.getLimit() : 0;
        List<BiProductStatisticsVO> statisticsVOS = biProductMapper.queryProductSell(timeEntity, page, biParams.getLimit());
        BasePage<BiProductStatisticsVO> basePage = new BasePage<>(biParams.getPage(), biParams.getLimit(), 1);
        basePage.setList(statisticsVOS);
        basePage.setExtraData(productStatisticsVO);
        return basePage;
    }

    /**
     * 产品分类销量分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiProductStatisticsVO> contractProductRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_PRODUCT_STATS.getMenuId());
        return biProductMapper.contractProductRanKing(timeEntity);
    }
}
