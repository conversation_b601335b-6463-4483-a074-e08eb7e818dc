package com.kakarote.bi.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.VO.BiEmployeeStatsVO;
import com.kakarote.bi.entity.VO.BiTotalContractVO;
import com.kakarote.bi.entity.VO.BiTotalInvoiceVO;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BiEmployeeMapper {

    /**
     * 合同编号状态
     *
     * @param biTimeEntity:BiTimeEntity
     * @param type
     * @return 员工业绩分析VO
     */
    @SqlParser(filter = true)
    List<BiEmployeeStatsVO> contractNumStats(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("type") Integer type);

    /**
     * 合同总表
     *
     * @param biTimeEntity:BiTimeEntity
     * @return 员工业绩分析VO
     */
    @SqlParser(filter = true)
    List<BiTotalContractVO> totalContractTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 发票状态表
     *
     * @param biTimeEntity
     * @return 员工业绩分析VO
     */
    @SqlParser(filter = true)
    List<BiTotalInvoiceVO> invoiceStatsTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);
}
