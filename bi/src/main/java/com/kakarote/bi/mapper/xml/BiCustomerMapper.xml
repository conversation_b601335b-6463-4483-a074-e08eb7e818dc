<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiCustomerMapper">
    <select id="totalCustomerStats" resultType="com.kakarote.bi.entity.VO.BiTotalCustomerVO">
        select
            a.dateType as type,
            IFNULL(customerNum,0) as customerNum,
            IFNULL(dealCustomerNum,0) as dealCustomerNum
        from (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                max(create_time) as lastTime
            from
                wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by
                dateType
            order by
            lastTime asc
        ) as a left join (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as type,
                count(1) as customerNum,
                count(if(deal_status=1,true,null)) as dealCustomerNum
            from wk_crm_customer
            where owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and status !='3'
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by type
        ) as b on a.dateType = b.type
        order by a.dateType asc
    </select>

    <select id="totalCustomerTable" resultType="com.kakarote.bi.entity.VO.BiTotalCustomerVO">
        select d.userId as userId,
               IFNULL(a.customerNum,0) as customerNum,
               IFNULL(a.dealCustomerNum,0) as dealCustomerNum,
               IFNULL(b.money,0) as contractMoney,
               IFNULL(c.money,0) as receivablesMoney
        from (
             <foreach collection="timeEntity.userIds" item="item" open="" separator="UNION" close=""> select ${item} as userId</foreach>
        ) as d left join
        (
            select
                owner_user_id as userId,
                count(1) as customerNum,
                count(if(deal_status=1,true,null)) as dealCustomerNum
            from wk_crm_customer
            where owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and status !='3'
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by owner_user_id
        ) as a on d.userId = a.userId
        left join
        (
            select
                owner_user_id as userId,
                sum(if(check_status=1 or check_status=10,money,0)) as money
            from wk_crm_contract
            where owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and order_date between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by owner_user_id
        ) as b on d.userId = b.userId
        left join
        (
            select
                owner_user_id as userId,
                sum(if(check_status=1 or check_status=10,money,0)) as money
            from wk_crm_receivables as a
            where owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and return_time between #{timeEntity.beginDate,jdbcType=DATE} and #{timeEntity.endDate,jdbcType=DATE}
            group by owner_user_id
        ) as c on d.userId = c.userId
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>


    <select id="customerConversionStats" resultType="com.kakarote.bi.entity.VO.BiTotalCustomerVO">
        select
            a.dateType as type,
            IFNULL(b.customerNum,0) as customerNum,
            IFNULL(c.dealCustomerNum,0) as dealCustomerNum
        from (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                max(create_time) as lastTime
            from
                wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by dateType
            order by lastTime asc
        ) as a left join (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as type,
                count(1) as customerNum
            from wk_crm_customer
            where status !='3' and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by type
        ) as b on a.dateType = b.type
        left join (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'deal_time')} as type,
                count(1) as dealCustomerNum
            from wk_crm_customer
            where status !='3' and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and deal_status = '1'
            and deal_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by type
        ) as c on a.dateType = c.type
        order by a.dateType asc
    </select>

    <select id="poolStats" resultType="com.kakarote.bi.entity.VO.BiCustomerPoolVO">
        select
        a.dateType as type,
        IFNULL(b.putInNum,0) as putInNum,
        IFNULL(c.receiveNum,0) as receiveNum
        from (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                max(create_time) as lastTime
            from
                wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by dateType
            order by lastTime asc
        ) as a left join (
            SELECT
            count(1) as putInNum,
            ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType
            FROM
            wk_crm_owner_record as a
            WHERE a.type='9' and a.create_time BETWEEN #{timeEntity.beginDate} and #{timeEntity.endDate}
            and a.pre_owner_user_id in
            <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by dateType
        ) as b on a.dateType = b.dateType
        left join (
            SELECT
                count(1) as receiveNum,
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType
            FROM
                wk_crm_owner_record as a
            WHERE a.type='9' and a.create_time BETWEEN #{timeEntity.beginDate} and #{timeEntity.endDate}
            and a.post_owner_user_id in
            <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by dateType
        ) as c on a.dateType = c.dateType
        order by a.dateType asc
    </select>

    <select id="poolTable" resultType="com.kakarote.bi.entity.VO.BiCustomerPoolVO">
        select a.userId as userId,
        IFNULL(b.putInNum,0) as putInNum,
        IFNULL(c.receiveNum,0) as receiveNum
        from (
            <foreach collection="timeEntity.userIds" item="item" open="" separator="UNION" close=""> select ${item} as userId</foreach>
        ) as a
        left join (
            SELECT
                count(1) as putInNum,
                pre_owner_user_id as pre_owner_user_id
            FROM
            wk_crm_owner_record as a
            WHERE a.type='9' and a.create_time BETWEEN #{timeEntity.beginDate} and #{timeEntity.endDate}
            and a.pre_owner_user_id in
            <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by pre_owner_user_id
        ) as b on a.userId = b.pre_owner_user_id
        left join (
            SELECT
                count(1) as receiveNum,
                post_owner_user_id as post_owner_user_id
            FROM
                wk_crm_owner_record as a
                join wk_crm_customer as customer on a.type_id = customer.customer_id
            WHERE a.type='9' and a.create_time BETWEEN #{timeEntity.beginDate} and #{timeEntity.endDate}
            and a.post_owner_user_id in
            <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and is_receive = 2
            and customer.owner_user_id in
            <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by post_owner_user_id
        ) as c on a.userId = c.post_owner_user_id
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>

    <select id="employeeCycle" resultType="com.kakarote.bi.entity.VO.BiEmployeeCycleVO">
        select
        a.dateType as type,
        IFNULL(b.customerNum,0) as customerNum,
        IFNULL(b.cycle,0) as cycle
        from (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                max(create_time) as lastTime
            from
                wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by dateType
            order by lastTime asc
        ) as a left join (
            SELECT
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                sum(to_days(deal_time) - to_days(create_time)) as cycle,
                count(1) as customerNum
            FROM
                wk_crm_customer
            WHERE deal_status  = '1' and status !='3'
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            group by
                dateType
        ) as b on a.dateType = b.dateType
        order by a.dateType asc
    </select>

    <select id="employeeCycleInfo" resultType="com.kakarote.bi.entity.VO.BiEmployeeCycleVO">
        select
            a.userId as userId,
            IFNULL(b.customerNum,0) as customerNum,
            IFNULL(b.cycle,0) as cycle
        from (
            <foreach collection="timeEntity.userIds" item="item" open="" separator="UNION" close=""> select ${item} as userId</foreach>
        ) as a join
        (
            select
                owner_user_id as userId,
                sum(to_days(deal_time) - to_days(create_time)) as cycle,
                count(1) as customerNum
            from wk_crm_customer
            where deal_status  = '1' and status !='3' and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by owner_user_id
        ) as b on a.userId = b.userId
        <if test="sortField != null and sortField != ''">
                order by ${sortField} ${sortType}
        </if>
    </select>


    <select id="districtCycle" resultType="com.kakarote.bi.entity.VO.BiEmployeeCycleVO">
        select
        a.city_name as type,
        IFNULL(b.customerNum,0) as customerNum,
        IFNULL(b.cycle,0) as cycle
        from (
            SELECT city_name FROM wk_crm_area WHERE parent_id ='100000'
        ) as a left join
        (
            select
                SUBSTRING_INDEX(address,',',1) as type,
                sum(to_days(deal_time) - to_days(create_time)) as cycle,
                count(1) as customerNum
            from wk_crm_customer
            where status !='3' and address !='' and address is not null and deal_status  ='1' and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by type
        ) as b on a.city_name = b.type
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>

    <select id="productCycle" resultType="com.kakarote.bi.entity.VO.BiEmployeeCycleVO">
        SELECT
            a.name as productName,
            a.product_id,
            ifnull(b.customerNum,0) as customerNum,
            ifnull(b.cycle,0) as cycle
        FROM
            wk_crm_product as a
        join (
            SELECT
                sum(to_days(b.order_date) - to_days(d.create_time)) as cycle,
                a.product_id as product_id,
                count(distinct b.customer_id) as customerNum
            FROM
                wk_crm_contract_product as a
                join wk_crm_contract as b on a.contract_id = b.contract_id
                JOIN wk_crm_customer as d on b.customer_id = d.customer_id
                where b.check_status in (1, 10)
                and b.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
                and d.status !='3'
                and b.owner_user_id in
                <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            GROUP by
            a.product_id
        ) as b on
        a.product_id = b.product_id
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>


    <select id="customerRecordStats" resultType="com.kakarote.bi.entity.VO.BiRecordVO">
        select
            a.dateType as type,
            IFNULL(b.recordNum,0) as recordNum,
            IFNULL(b.outSignRecordNum,0) as outSignRecordNum,
            IFNULL(b.customerNum,0) as customerNum
        from (
            select
            ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
            max(create_time) as lastTime
            from
            wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by dateType
            order by lastTime asc
        ) as a left join (
            SELECT
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                count(if(type=1,true,null)) as recordNum ,
                count(if(type=4,true,null)) as outSignRecordNum,
                count(distinct activity_type_id) as customerNum
            FROM
                wk_crm_activity
            WHERE
                activity_type = '2'
                and create_user_id in
                <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
                and type='1' and activity_id  = activity_type_id
                group by dateType
        ) as b on a.dateType = b.dateType
        order by a.dateType asc
    </select>

    <select id="customerRecordInfo" resultType="com.kakarote.bi.entity.VO.BiRecordVO">
        select
            a.userId as userId,
            IFNULL(b.recordNum,0) as recordNum,
            IFNULL(b.outSignRecordNum,0) as outSignRecordNum,
            IFNULL(b.customerNum,0) as customerNum
        from (
            <foreach collection="timeEntity.userIds" item="item" open="" separator="UNION" close=""> select ${item} as userId</foreach>
        ) as a left join
        (
            SELECT
                create_user_id as userId,
                count(if(type=1,true,null)) as recordNum ,
                count(if(type=4,true,null)) as outSignRecordNum,
                count(distinct activity_type_id) as customerNum
            FROM
                wk_crm_activity
            WHERE
                activity_type = '2'
                and create_user_id in
                <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and type in ('1','4') and activity_id  = activity_type_id
                and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by create_user_id
        ) as b on a.userId = b.userId
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>

    <select id="queryCrmFieldInfo" resultType="com.kakarote.bi.entity.BO.CrmFieldBO">
        select field_id, `options`
        from wk_crm_field
        where label = #{label} and field_name = #{fieldName}
    </select>

    <select id="customerSatisfactionTable" resultType="com.alibaba.fastjson.JSONObject">
        select contract.owner_user_id owner_user_id,
        count(wcrv.contract_id) as visit_contract_num,
        count(if(tem_wcrvd.value != '',true,null)) as allNum,
        <foreach collection="map.options" item="optionValue" separator=",">
            count(if(tem_wcrvd.value='${optionValue}',true,null)) as "${optionValue}"
        </foreach>
        from wk_crm_return_visit wcrv
        join wk_crm_contract as contract on wcrv.contract_id = contract.contract_id
        left join(
        select value ,batch_id from wk_crm_return_visit_data as wcrvd where field_id = #{map.fieldId}
        ) tem_wcrvd on wcrv.batch_id =
        tem_wcrvd.batch_id
        where wcrv.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and contract.owner_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by contract.owner_user_id
    </select>


    <select id="productSatisfactionTable" resultType="com.alibaba.fastjson.JSONObject">
        select a.product_id,a.name as productName, b.visitNum as visitNum,b.allNum as allNum,
        <foreach collection="map.options" separator="," item="optionValue">
            b.`${optionValue}` as `${optionValue}`
        </foreach>
        from wk_crm_product as a join (
        select a.product_id,
        count(b.contract_id) as visitNum,
        count(if(b.value != '',true,null)) as allNum,
        <foreach collection="map.options" separator="," item="optionValue">
            count(if(b.value='${optionValue}',true,null)) as "${optionValue}"
        </foreach>
        from wk_crm_contract_product as a
        left join (
        select a.contract_id contract_id, ifNull(b.value, '') as value
        from wk_crm_return_visit as a
        join (
        select value, batch_id from wk_crm_return_visit_data where field_id = #{map.fieldId}
        ) as b
        on a.batch_id = b.batch_id
        left join wk_crm_contract as contract on a.contract_id = contract.contract_id
        where a.create_time between #{timeEntity.beginDate} and
        #{timeEntity.endDate}
        and contract.owner_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        ) as b on a.contract_id = b.contract_id
        where 1=1
        group by a.product_id
        )as b on a.product_id = b.product_id
    </select>

    <select id="queryContactsByCustomerSatisfaction" resultType="java.lang.String">
        select contract.contract_id from wk_crm_return_visit a
        join wk_crm_contract as contract on a.contract_id = contract.contract_id
        left join(
        select value ,batch_id from wk_crm_return_visit_data as wcrvd where field_id = #{map.fieldId}
        ) tem_wcrvd on a.batch_id = tem_wcrvd.batch_id
        where
        <if test="map.searchType != null and map.searchType != ''">
            tem_wcrvd.value = #{map.searchType} and
        </if>
        a.create_time between #{timeEntity.beginDate} and
        #{timeEntity.endDate}
        and contract.owner_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="queryContractsByProductSatisfaction" resultType="java.lang.String">
        SELECT b.contract_id FROM wk_crm_contract_product as product join (
        select visit.contract_id contract_id from wk_crm_return_visit visit
        join wk_crm_contract as contract on visit.contract_id = contract.contract_id
        join(
        select value ,batch_id from wk_crm_return_visit_data as wcrvd where field_id = #{map.fieldId}
        <if test="map.searchType != null">
            and `wcrvd`.value = #{map.searchType}
        </if>
        ) tem_wcrvd on visit.batch_id = tem_wcrvd.batch_id
        where visit.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and contract.owner_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        ) as b on
        product.contract_id = b.contract_id
        WHERE product.product_id = #{map.id}
    </select>

    <select id="queryProductTypeList" resultType="com.alibaba.fastjson.JSONObject">
        select * from wk_crm_product where product_id in (
        SELECT
        product_id
        FROM
        wk_crm_contract_product as a
        WHERE contract_id in (
        SELECT
        contract_id
        FROM
        wk_crm_contract
        where
        order_date between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        )
        and category_id = #{biParams.categoryId}
        limit ${page},${size}
    </select>

    <select id="customerRecordIds" resultType="java.lang.Long">
        SELECT
            activity_type_id
        FROM
        wk_crm_activity
        WHERE
        activity_type = #{type}
        and create_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and type = 1
        and activity_id  = activity_type_id
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        group by activity_type_id
    </select>

    <select id="queryProductCustomerList" resultType="java.lang.Long">
        SELECT
            b.customer_id
        FROM
        wk_crm_contract_product as a
        join wk_crm_contract as b on a.contract_id = b.contract_id
        JOIN wk_crm_customer as d on b.customer_id = d.customer_id
        where b.check_status in (1, 10)
        and a.product_id = #{id}
        and b.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and d.status !='3'
        and b.owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP by
        b.customer_id
    </select>

    <select id="queryContendBusinessList" resultType="java.lang.Long">
        select business_id from wk_crm_business where is_end = #{biParams.type}
        and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
    </select>

    <select id="queryCallAnalysis" resultType="com.kakarote.bi.entity.VO.BiEmployeeCallVO">
        SELECT
        owner_user_id,
        count(1) AS `total_count_calls`,
        count(if(state = 2,true,null)) AS `total_count_answer`,
        sum(if(state=2,talk_time,0)) AS `total_time_calls`,
        count(if(type=0,true,null)) AS `total_count_calls_out`,
        count(if(type=0 and state=2,true,null)) AS `total_count_answer_out`,
        sum(if(state=2 and type=0,talk_time,0)) AS `total_time_calls_out`,
        count(if(type=1,true,null)) AS `total_count_calls_in`,
        count(if(type=1 and state=2,true,null)) AS `total_count_answer_in`,
        sum(if(state=2 and type=1,talk_time,0)) AS `total_time_calls_in`
        FROM
        wk_call_record
        WHERE owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        GROUP BY owner_user_id
    </select>

    <select id="queryCallList" resultType="com.kakarote.bi.entity.VO.BiCallListVO">
        SELECT
            `call_record_id`,
            `number`,
            `talk_time`,
            `create_time`,
            `dial_time`,
            `state`,
            `type`,
            `model`,
            `model_id`,
            `size`,
            `file_name`,
            `owner_user_id`
        FROM
         wk_call_record as a
        WHERE a.create_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        <if test = "talkTime != null and talkTime != ''">
            <if test="talkTimeCondition != null and talkTimeCondition == '&lt;'.toString() ">
                and talk_time <![CDATA[<]]> #{talkTime}
            </if>
            <if test="talkTimeCondition != null and talkTimeCondition == '&lt;='.toString() ">
                and talk_time <![CDATA[<=]]> #{talkTime}
            </if>
            <if test="talkTimeCondition != null and talkTimeCondition == '='.toString() ">
                and talk_time <![CDATA[=]]> #{talkTime}
            </if>
            <if test="talkTimeCondition != null and talkTimeCondition == '&gt;'.toString() ">
                and talk_time <![CDATA[>]]> #{talkTime}
            </if>
            <if test="talkTimeCondition != null and talkTimeCondition == '&gt;='.toString() ">
                and talk_time <![CDATA[>=]]> #{talkTime}
            </if>
        </if>
        order by a.call_record_id DESC
    </select>

    <select id="queryModelName" resultType="java.util.HashMap">
        SELECT leads_id as modelId,leads_name as modelName from wk_crm_leads as a
        where
        <choose>
            <when test="_parameter.containsKey('leads')">
                leads_id in
                <foreach collection="leads" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise> 1=2 </otherwise>
        </choose>
        union all
        select customer_id  as modelId,customer_name as modelName from wk_crm_customer as b
        where
        <choose>
            <when test="_parameter.containsKey('customer')">
                customer_id in
                <foreach collection="customer" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise> 1=2 </otherwise>
        </choose>
        union all
        select contacts_id  as modelId,name as modelName from wk_crm_contacts as c
        where
        <choose>
            <when test="_parameter.containsKey('contacts')">
                contacts_id in
                <foreach collection="contacts" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise> 1=2 </otherwise>
        </choose>
    </select>
</mapper>
