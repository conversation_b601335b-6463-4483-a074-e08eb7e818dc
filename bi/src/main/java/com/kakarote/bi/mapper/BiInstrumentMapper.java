package com.kakarote.bi.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.VO.BiBulletinVO;
import com.kakarote.bi.entity.VO.BiDataInfoVO;
import com.kakarote.bi.entity.VO.BiForgottenCustomerVO;
import com.kakarote.bi.entity.VO.BiSalesTrendVO;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface BiInstrumentMapper {

    /**
     * 查询仪表盘销售简报功能
     *
     * @param timeEntity         时间对象
     * @param customerUserIds    客户模块可查询用户列表
     * @param contactsUserIds    联系人模块可查询用户列表
     * @param businessUserIds    商机模块可查询用户列表
     * @param contractUserIds    合同模块可查询用户列表
     * @param receivablesUserIds 回款模块可查询用户列表
     * @param recordUserIds      跟进记录模块可查询用户列表
     * @return data
     */
    @SqlParser(filter = true)
    List<BiBulletinVO> queryBulletin(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,
                                     @Param("customerUserIds") List<Long> customerUserIds,
                                     @Param("contactsUserIds") List<Long> contactsUserIds,
                                     @Param("businessUserIds") List<Long> businessUserIds,
                                     @Param("contractUserIds") List<Long> contractUserIds,
                                     @Param("receivablesUserIds") List<Long> receivablesUserIds,
                                     @Param("recordUserIds") List<Long> recordUserIds
    );


    /**
     * 查询仪表盘销售简报功能
     *
     * @param timeEntity 时间对象
     * @param type       类型 1 合同 2 回款
     * @return data
     */

    @SqlParser(filter = true)
    List<BiSalesTrendVO> salesTrend(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,
                                    @Param("type") Integer type
    );


    /**
     * 查询成绩List
     * @param year
     * @param label
     * @param userIds
     * @param deptIds
     * @return data
     */
    @MapKey(value = "year")
    Map<Integer, Map<String, BigDecimal>> queryAchievementList(@Param("year") List<Integer> year,
                                                               @Param("label") Integer label,
                                                               @Param("userIds") List<Long> userIds,
                                                               @Param("deptIds") List<Long> deptIds
    );

    /**
     * 查询仪表盘销售简报功能
     *
     * @param timeEntity         时间对象
     * @param customerUserIds    客户模块可查询用户列表
     * @param businessUserIds    商机模块可查询用户列表
     * @param contractUserIds    合同模块可查询用户列表
     * @param receivablesUserIds 回款模块可查询用户列表
     * @param recordUserIds      跟进记录模块可查询用户列表
     * @param expiringDay        到期天数
     * @return data
     */
    @SqlParser(filter = true)
    BiDataInfoVO queryDataInfo(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,
                               @Param("customerUserIds") List<Long> customerUserIds,
                               @Param("businessUserIds") List<Long> businessUserIds,
                               @Param("contractUserIds") List<Long> contractUserIds,
                               @Param("receivablesUserIds") List<Long> receivablesUserIds,
                               @Param("expiringDay") Integer expiringDay,
                               @Param("recordUserIds") List<Long> recordUserIds
    );

    /**
     * 查询仪表盘业绩指标完成率
     *
     * @param timeEntity 时间对象
     * @param type       类型 1 合同 2 回款
     * @return data
     */
    @SqlParser(filter = true)
    BigDecimal queryPerformance(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,
                                @Param("type") Integer type
    );


    /**
     * 遗忘客户统计功能
     *
     * @param timeEntity 时间对象
     * @return data
     */
    @SqlParser(filter = true)
    BiForgottenCustomerVO forgottenCustomerCount(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);
}
