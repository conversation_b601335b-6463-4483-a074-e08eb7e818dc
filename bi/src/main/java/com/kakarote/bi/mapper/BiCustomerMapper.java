package com.kakarote.bi.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.BO.BICrmInfoBO;
import com.kakarote.bi.entity.BO.CrmFieldBO;
import com.kakarote.bi.entity.VO.*;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface BiCustomerMapper {

    /**
     * 客户总量分析表
     *
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiTotalCustomerVO> totalCustomerStats(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 客户总量分析图
     *
     * @param biTimeEntity timeEntity
     * @param sortField    排序字段
     * @param sortType     排序方式，正序还是倒序
     * @return data
     */
    @SqlParser(filter = true)
    List<BiTotalCustomerVO> totalCustomerTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);


    /**
     * 客户转化率分析
     *
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiTotalCustomerVO> customerConversionStats(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 公海客户分析图
     *
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiCustomerPoolVO> poolStats(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 公海客户分析表格
     *
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序类型
     * @return data
     */
    @SqlParser(filter = true)
    List<BiCustomerPoolVO> poolTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);

    /**
     * 员工成交周期分析
     *
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiEmployeeCycleVO> employeeCycle(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 员工成交周期分析表
     *
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序类型
     * @return data
     */
    @SqlParser(filter = true)
    List<BiEmployeeCycleVO> employeeCycleInfo(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);

    /**
     * 地区成交周期分析表
     *
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序类型
     * @return data
     */
    @SqlParser(filter = true)
    List<BiEmployeeCycleVO> districtCycle(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);

    /**
     * 产品周期分析
     *
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序类型
     * @return data
     */
    @SqlParser(filter = true)
    List<BiEmployeeCycleVO> productCycle(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);

    /**
     * 查询跟进方式列表
     * @param label 字段等级
     * @param fieldName 字段名
     * @return data
     */
    CrmFieldBO queryCrmFieldInfo(@Param("label") Integer label, @Param("fieldName") String fieldName);


    /**
     * 客户跟进次数分析图
     *
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiRecordVO> customerRecordStats(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 客户跟进次数分析表
     *
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序类型
     * @return data
     */
    @SqlParser(filter = true)
    List<BiRecordVO> customerRecordInfo(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType);

    /**
     * 客户满意表
     * @param   biTimeEntity
     * @param   map
     * @return  data
    */
    @SqlParser(filter = true)
    List<JSONObject> customerSatisfactionTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("map") Map<String, Object> map);
    /**
     * 产品满意表
     * @param biTimeEntity
     * @param map
     * @return data
    */
    @SqlParser(filter = true)
    List<JSONObject> productSatisfactionTable(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("map") Map<String, Object> map);
    /**
     * 查看员工满意分析合同数据
     * @return data
     */
    @SqlParser(filter = true)
    List<String> queryContactsByCustomerSatisfaction(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity,@Param("map")Map<String, Object> map);

    /**
     * 查看产品满意分析合同数据
     * @param biTimeEntity
     * @param map
     * @return
     */
    @SqlParser(filter = true)
    List<String> queryContractsByProductSatisfaction(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("map") Map<String, Object> map);

    /**
     * 查看产品类别销售列表
     * @param biTimeEntity
     * @param biParams
     */
    List<JSONObject> queryProductTypeList(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity,@Param("biParams") BICrmInfoBO biParams,
                                          @Param("page") Integer page, @Param("size") Integer size);

    /**
     * 查询跟进crm模块数量ID
     * @param timeEntity
     * @return
     */
    List<Long> customerRecordIds(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,@Param("type") Integer type,@Param("category")Object category);

    /**
     * 查看产品成交客户列表
     * @param biTimeEntity
     * @param
     * @return
     */
    @SqlParser(filter = true)
    List<Long> queryProductCustomerList(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity,@Param("id") Long id);

    /**
     * 查看商机输赢数据
     * @param biTimeEntity
     * @param biParams
     * @return
     */
    List<Long> queryContendBusinessList(@Param("timeEntity")BiParamsUtil.BiTimeEntity biTimeEntity,@Param("biParams") BICrmInfoBO biParams);

    /**
     * 查看员工通话统计分析
     * @param biTimeEntity
     * @param biParams
     */
    @SqlParser(filter = true)
    BasePage<BiEmployeeCallVO> queryCallAnalysis(BasePage basePage, @Param("timeEntity")BiParamsUtil.BiTimeEntity biTimeEntity, @Param("biParams")BiEntityParams biParams);

    /**
     * 查看员工通话记录
     * @param parse 分页信息
     * @param biTimeEntity timeEntity
     * @param talkTime 通话时间
     * @param talkTimeCondition 通话筛选条件
     * @return
     */
    BasePage<BiCallListVO> queryCallList(BasePage<Object> parse, @Param("timeEntity")BiParamsUtil.BiTimeEntity biTimeEntity, @Param("talkTime")Long talkTime,  @Param("talkTimeCondition")String talkTimeCondition);


    @MapKey("modelId")
    Map<Long,Map<String,Object>> queryModelName(Map<String,List<Long>> modelMap);
}
