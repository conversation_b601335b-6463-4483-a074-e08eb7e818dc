package com.kakarote.bi.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.BO.BiAchievementBO;
import com.kakarote.bi.entity.VO.BiMonthReceivedMoneyVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface BiAchievementMapper {

    /**
     * 彻底完成工作统计
     *
     * @param biAchievementBO:业绩目标完成
     * @param year:年份
     * @return a
     */
    @MapKey("objId")
    public Map<Long, JSONObject> taskCompleteStatistics(@Param("data") BiAchievementBO biAchievementBO, @Param("year") Integer year);

    @SqlParser(filter = true)
    /**
     * 彻底完成工作统计速度
     * @param biAchievementBO:业绩目标完成
     * @return java.util.List<com.kakarote.bi.entity.VO.BiMonthReceivedMoneyVO>
     */
    List<BiMonthReceivedMoneyVO> taskCompleteStatisticsRate(@Param("data") BiAchievementBO biAchievementBO);

}
