<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiProductMapper">
    <select id="queryProductSell" resultType="com.kakarote.bi.entity.VO.BiProductStatisticsVO">
        select
            a.contractNum as contractNum,
            ifnull(a.num,0) as num,
            ifnull(a.total,0) as total,
            b.name  as productName,
            c.name as categoryName,
            b.product_id productId
        FROM
            (
                SELECT
                    product_id,
                    count(distinct contract_id) as contractNum,
                    SUM(num) as num,
                    SUM(subtotal) as total
                FROM
                    wk_crm_contract_product as a
                WHERE contract_id in (
                        SELECT
                            contract_id
                        FROM
                            wk_crm_contract
                        where
                            order_date  between #{timeEntity.beginDate} and #{timeEntity.endDate}
                            and check_status in ('1','10')
                            and owner_user_id in
                            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                )
                group by
                    product_id
                limit ${page},${size}
            ) as a
            JOIN wk_crm_product as b on a.product_id = b.product_id
            JOIN wk_crm_product_category as c on b.category_id = c.category_id
    </select>

    <select id="queryProductSellCount" resultType="com.kakarote.bi.entity.VO.BiProductStatisticsVO">
        select
        a.contractNum as contractNum,
        ifnull(a.num,0) as num,
        ifnull(a.total,0) as total,
        a.count as count
        FROM
        (
            SELECT
                count(distinct contract_id) as contractNum,
                SUM(num) as num,
                SUM(subtotal) as total,
                count(1) as count
            FROM
            wk_crm_contract_product as a
            WHERE contract_id in (
                SELECT
                    contract_id
                FROM
                    wk_crm_contract
                where
                order_date  between #{timeEntity.beginDate} and #{timeEntity.endDate}
                and owner_user_id in
                <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        ) as a
    </select>

    <select id="contractProductRanKing" resultType="com.kakarote.bi.entity.VO.BiProductStatisticsVO">
        select
            ifnull(a.number,0) as num,
            ifnull(a.total,0) as total,
            a.count as count,
            c.name as categoryName,
            a.category_id
        FROM
        (
            select
            sum(number) as number,
            sum(total) as total,
            count(1) as count,
            category_id
            from
            (
                SELECT
                    num as number,
                    subtotal as total,
                    product_id
                FROM
                    wk_crm_contract_product as a
                WHERE contract_id in (
                    SELECT
                    contract_id
                    FROM
                    wk_crm_contract
                    where
                    order_date  between #{timeEntity.beginDate} and #{timeEntity.endDate}
                    and owner_user_id in
                    <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            ) as a
            join wk_crm_product as b on a.product_id = b.product_id
            group by
            b.category_id
        ) as a
        join wk_crm_product_category as c on a.category_id = c.category_id
        order by a.number desc
    </select>
</mapper>
