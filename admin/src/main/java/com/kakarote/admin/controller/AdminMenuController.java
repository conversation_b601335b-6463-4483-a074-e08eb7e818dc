package com.kakarote.admin.controller;


import com.alibaba.fastjson.JSONObject;
import com.kakarote.admin.common.AdminRoleTypeEnum;
import com.kakarote.admin.entity.PO.AdminMenu;
import com.kakarote.admin.service.IAdminMenuService;
import com.kakarote.core.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 后台菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@RestController
@RequestMapping("/adminMenu")
@Api(tags = "菜单模块")
public class AdminMenuController {

    @Autowired
    private IAdminMenuService adminMenuService;

    @RequestMapping("/getMenuListByType/{type}")
    @ApiOperation("根据类型查询菜单")
    public Result<JSONObject> getMenuListByType(@PathVariable("type") Integer type) {
        AdminRoleTypeEnum typeEnum = AdminRoleTypeEnum.parse(type);
        JSONObject byType = adminMenuService.getMenuListByType(typeEnum);
        return Result.ok(byType);
    }

    @RequestMapping("/queryMenuId")
    public Result<Long> queryMenuId(@RequestParam("realm1") String realm1,@RequestParam("realm2") String realm2,
                                       @RequestParam("realm3") String realm3){
        Long menuId = adminMenuService.queryMenuId(realm1,realm2,realm3);
        return Result.ok(menuId);

    }

    /**
     * 通过角色列表查询菜单列表
     * @param roleIds 角色ids
     * @return 菜单
     */
    @RequestMapping("/queryMenuListByRoleIds")
    @ApiOperation("根据角色id查询菜单")
    public Result<List<AdminMenu>> queryMenuListByRoleIds(@RequestBody List<Long> roleIds){
        return Result.ok(adminMenuService.queryMenuListByRoleIds(roleIds));
    }

    /**
     * 通过角色列表查询菜单列表
     * @return 菜单
     */
    @RequestMapping("/queryMenuList")
    @ApiOperation("根据用户id公司id查询菜单")
    public Result<List<AdminMenu>> queryMenuList(@RequestParam("userId")Long userId){
        return Result.ok(adminMenuService.queryMenuList(userId));
    }

    /**
     * 查询所有菜单
     * @return
     */
    @RequestMapping("/queryAllMenuList")
    @ApiOperation("通过realm查询菜单")
    public Result<List<AdminMenu>> queryAllMenuList(){
        return Result.ok(adminMenuService.queryAllMenuList());

    }
}

