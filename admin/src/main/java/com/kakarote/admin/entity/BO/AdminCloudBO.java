package com.kakarote.admin.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 云平台注册BO
 */
@Data
@ApiModel("云平台注册BO")
public class AdminCloudBO {

    @ApiModelProperty("注册手机号")
    @NotNull(message = "手机号或密码不可为空！")
    private String phone;

    @ApiModelProperty("密码")
    @NotNull(message = "手机号或密码不可为空！")
    private String password;

    @ApiModelProperty("短信验证码")
    private String smsCode;

    @ApiModelProperty("企业名称")
    @NotNull(message = "企业名称不能为空！")
    private String companyName;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("优惠码")
    private String discount;

    @ApiModelProperty("姓名")
    private String realname;

    @ApiModelProperty("开通的应用列表")
    private List<String> authList = Collections.emptyList();


}
