package com.kakarote.admin.entity.BO;

import com.kakarote.core.entity.PageEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: AdminPageApplyBO
 * @Author: Blue
 * @Description: AdminPageApplyBO
 * @Date: 2021/11/8 13:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查询员工申请列表查询对象")
public class AdminPageApplyBO extends PageEntity {

    @ApiModelProperty(value = "1.待审核邀请 2.已审核邀请")
    private Integer type;
}
