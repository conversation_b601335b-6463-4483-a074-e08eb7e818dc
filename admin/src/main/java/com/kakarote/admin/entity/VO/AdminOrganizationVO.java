package com.kakarote.admin.entity.VO;

import com.kakarote.core.feign.admin.entity.SimpleUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel("组织架构信息对象")
public class AdminOrganizationVO {

    @ApiModelProperty("用户对象")
    private Map<Character,List<SimpleUser>> userMap;

    @ApiModelProperty("部门对象")
    private List<AdminDeptVO> deptList;

    @ApiModelProperty("禁用员工列表")
    private List<SimpleUser> disableUserList;

    @Override
    public String toString() {
        return "AdminOrganizationVO{" +
                "userMap=" + userMap +
                ", deptList=" + deptList +
                ", disableUserList=" + disableUserList +
                '}';
    }
}
