package com.kakarote.admin.entity.BO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AdminPayCreateOrderBO {

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("购买年份数，工商查询则是数量")
    private Integer year;

    @ApiModelProperty(value = "购买的系统列表")
    List<String> key;

}
