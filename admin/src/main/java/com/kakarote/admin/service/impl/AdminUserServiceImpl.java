package com.kakarote.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kakarote.admin.common.AdminCodeEnum;
import com.kakarote.admin.common.AdminConst;
import com.kakarote.admin.common.AdminRoleTypeEnum;
import com.kakarote.admin.entity.BO.*;
import com.kakarote.admin.entity.PO.*;
import com.kakarote.admin.entity.VO.*;
import com.kakarote.admin.mapper.AdminUserMapper;
import com.kakarote.admin.service.*;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.common.enums.BaseStatusEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.AdminUserQueryBO;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.SimpleDept;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.entity.mall.UserDTO;
import com.kakarote.core.feign.crm.service.CrmService;
import com.kakarote.core.feign.hrm.entity.HrmEmployee;
import com.kakarote.core.feign.hrm.service.HrmService;
//import com.kakarote.core.feign.jxc.service.JxcService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.*;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
public class AdminUserServiceImpl extends BaseServiceImpl<AdminUserMapper, AdminUser> implements IAdminUserService {

    @Autowired
    private IAdminRoleService adminRoleService;

    @Autowired
    private IAdminDeptService adminDeptService;

    @Autowired
    private IAdminUserConfigService adminUserConfigService;

    @Autowired
    private IAdminUserRoleService adminUserRoleService;

    @Autowired
    private IAdminAttentionService adminAttentionService;

//    @Autowired
//    @Lazy
//    private CrmService crmService;

//    @Autowired
//    private JxcService jxcService;

    @Autowired
    private Redis redis;

    @CreateCache(name = Const.ADMIN_USER_NAME_CACHE_NAME, expire = 3, timeUnit = TimeUnit.DAYS)
    private Cache<Long, SimpleUser> userCache;

    @Override
    public List<Map<String, Object>> findByUsername(String username, HttpServletRequest request) {
        String serverName = StrUtil.isNotEmpty(request.getHeader("proxyHost")) ? request.getHeader("proxyHost") : request.getServerName();
        log.info("当前请求域名：{}", serverName);
        List<Map<String, Object>> userInfoList = getBaseMapper().findByUsername(username);
        userInfoList.forEach(userInfo -> {
            userInfo.put("superUserId", UserUtil.getSuperUser());
            userInfo.put("superRoleId", UserUtil.getSuperRole());
        });
        return userInfoList;
    }


    /**
     * 查询企业下所有用户
     *
     * @param adminUserBO 业务对象
     * @return ids
     */
    @Override
    public BasePage<AdminUserVO> queryUserList(AdminUserBO adminUserBO) {
        if (adminUserBO == null) {
            BasePage<AdminUserVO> userBasePage = new BasePage<>();
            List<AdminUserVO> adminUserVOList = this.lambdaQuery().ne(AdminUser::getUserId, UserUtil.getSuperUser())
                    .list().stream().map(adminUser -> this.convertAdminUserToVo(adminUser, 2)).collect(Collectors.toList());
            //置顶超级管理员
            AdminUser adminSuperUser = this.getById(UserUtil.getSuperUser());
            adminUserVOList.add(0, this.convertAdminUserToVo(adminSuperUser, 0));
            userBasePage.setRecords(adminUserVOList);
            return userBasePage;
        }
        Long deptOwnerUserId = null;
        adminUserBO.setUserId(UserUtil.getSuperUser());
        if (adminUserBO.getDeptId() != null) {
            List<Long> list;
            if (Objects.equals(adminUserBO.getIsNeedChild(), 1)) {
                list = adminDeptService.queryChildDept(adminUserBO.getDeptId());
            } else {
                list = new ArrayList<>();
            }
            list.add(adminUserBO.getDeptId());
            adminUserBO.setDeptIdList(list);
            deptOwnerUserId = adminDeptService.getById(adminUserBO.getDeptId()).getOwnerUserId();
            adminUserBO.setDeptOwnerUserId(deptOwnerUserId);
        }
        BasePage<AdminUserVO> basePage = getBaseMapper().queryUserList(adminUserBO.parse(), adminUserBO);
        LambdaQueryWrapper<AdminUserHisTable> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUserHisTable::getUserId);
        queryWrapper.eq(AdminUserHisTable::getHisTable, 1);
        List<Long> longs = ApplicationContextHolder.getBean(IAdminUserHisTableService.class).listObjs(queryWrapper, user -> Long.valueOf(user.toString()));
        for (AdminUserVO adminUserVO : basePage.getRecords()) {
            if (longs.contains(adminUserVO.getUserId())) {
                adminUserVO.setHisTable(1);
            }
            List<AdminRole> adminRoleList = adminRoleService.queryRoleListByUserId(adminUserVO.getUserId());
            adminUserVO.setRoleId(adminRoleList.stream().map(adminRole -> adminRole.getRoleId().toString()).collect(Collectors.joining(",")));
            adminUserVO.setRoleName(adminRoleList.stream().map(AdminRole::getRoleName).collect(Collectors.joining(",")));
            //标识超级管理员和部门负责人
            if (Objects.equals(adminUserVO.getUserId(), UserUtil.getSuperUser())) {
                adminUserVO.setUserIdentity(0);
            }
            //既是主账号又是负责人以主账号为准
            if (Objects.equals(adminUserVO.getUserId(), deptOwnerUserId) && Objects.equals(2, adminUserVO.getUserIdentity())) {
                adminUserVO.setUserIdentity(1);
            }
        }
        //按照是否负责人进行数据排序
        basePage.getList().sort(Comparator.comparing(AdminUserVO::getUserIdentity));
        return basePage;
    }

    private AdminUserVO convertAdminUserToVo(AdminUser adminUser, Integer userIdentity) {
        AdminUserVO userVO = BeanUtil.copyProperties(adminUser, AdminUserVO.class);
        userVO.setUserIdentity(userIdentity);
        userVO.setDeptName(UserCacheUtil.getDeptName(userVO.getDeptId()));
        return userVO;
    }


    @Override
    public JSONObject countUserByLabel() {
        JSONObject jsonObject = new JSONObject();
        jsonObject
                .fluentPut("allUserCount", getBaseMapper().countUserByLabel(0, null))
                .fluentPut("addNewlyCount", getBaseMapper().countUserByLabel(1, null))
                .fluentPut("activateCount", getBaseMapper().countUserByLabel(null, 1))
                .fluentPut("inactiveCount", getBaseMapper().countUserByLabel(2, null))
                .fluentPut("disableCount", getBaseMapper().countUserByLabel(3, null))
                .fluentPut("applyUserCount", getBaseMapper().countUserApply(2));
        return jsonObject;
    }

    /**
     * 查询该用户下级的用户
     *
     * @param userId 用户ID 0代表全部
     * @return data
     */
    @Override
    public List<Long> queryChildUserId(Long userId) {
        return RecursionUtil.getChildList(list(), "parentId", userId, "userId", "userId");
    }


    /**
     * 新增或修改用户
     *
     * @param adminUserVO data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setUser(AdminUserVO adminUserVO) {
        if (adminUserVO.getParentId() == null) {
            adminUserVO.setParentId(0L);
        }
        List<Long> userList = queryChildUserId(adminUserVO.getUserId());
        if (userList.contains(adminUserVO.getParentId())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PARENT_USER_ERROR);
        }
        if (adminUserVO.getUserId().equals(adminUserVO.getParentId())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PARENT_USER_ERROR1);
        }
        //用户姓名处理
        Integer nameCount = query().eq("realname", adminUserVO.getRealname()).ne("user_id", adminUserVO.getUserId()).count();
        if (nameCount > 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_USER_REAL_NAME_EXIST_ERROR);
        }
        //不修改用户名
        adminUserVO.setUsername(null);
        //不修改密码
        adminUserVO.setPassword(null);
        AdminUser adminUser = BeanUtil.copyProperties(adminUserVO, AdminUser.class);
        adminUserRoleService.saveByUserId(adminUserVO.getUserId(), true, StrUtil.splitTrim(adminUserVO.getRoleId(), Const.SEPARATOR));
        updateById(adminUser);
//        crmService.batchUpdateEsData(adminUser.getUserId().toString(), adminUser.getRealname(), "user");
//        crmService.batchUpdateEsData(adminUser.getUserId().toString(), adminUser.getDeptId().toString(), "ownerDept");
//        crmService.batchUpdateEsData(adminUser.getUserId().toString(), UserCacheUtil.getDeptName(adminUser.getDeptId()), "ownerDeptName");
        Long key = adminUser.getUserId();
        redis.del(AdminCacheKey.USER_AUTH_CACHE_KET + key);
        userCache.put(key, new SimpleUser(adminUser.getUserId(), adminUser.getImg(), adminUser.getRealname(), adminUser.getDeptId(), adminDeptService.getNameByDeptId(adminUser.getDeptId())));
    }

    @Override
    public void setUserDept(AdminUserBO adminUserBO) {
        Long deptId = adminUserBO.getDeptId();
        List<Long> userIdList = adminUserBO.getUserIdList();
        if (CollUtil.isEmpty(userIdList) || deptId == null) {
            return;
        }
        AdminDept dept = adminDeptService.getById(deptId);
        if (dept == null) {
            throw new CrmException(AdminCodeEnum.ADMIN_DEPT_NOT_EXIST_ERROR);
        }
        this.lambdaUpdate().set(AdminUser::getDeptId, deptId).in(AdminUser::getUserId, userIdList).update();
        for (Long userId : userIdList) {
            SimpleUser simpleUser = userCache.get(userId);
            if (simpleUser != null) {
                simpleUser.setDeptId(deptId);
                simpleUser.setDeptName(dept.getName());
                userCache.put(userId, simpleUser);
//                crmService.batchUpdateEsData(simpleUser.getUserId().toString(), deptId.toString(), "ownerDept");
//                crmService.batchUpdateEsData(simpleUser.getUserId().toString(), dept.getName(), "ownerDeptName");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUser(AdminUserVO adminUser) {
        if (adminUser.getParentId() == null) {
            adminUser.setParentId(0L);
        }
        if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, adminUser.getPassword())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INTENSITY_ERROR);
        }
        Integer count = query().eq("username", adminUser.getUsername()).count();
        if (count > 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_USER_EXIST_ERROR);
        }
        Integer nameCount = query().eq("realname", adminUser.getRealname()).count();
        if (nameCount > 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_USER_REAL_NAME_EXIST_ERROR);
        }
        String salt = IdUtil.fastSimpleUUID();
        AdminUser adminUserPO = BeanUtil.copyProperties(adminUser, AdminUser.class);
        adminUserPO.setCreateTime(LocalDateTimeUtil.now());
        adminUserPO.setNum(RandomUtil.randomNumbers(15));
        adminUserPO.setMobile(adminUserPO.getUsername());
        adminUserPO.setSalt(salt);
        adminUserPO.setPassword(UserUtil.sign((adminUser.getUsername().trim() + adminUser.getPassword().trim()), salt));
        save(adminUserPO);
        adminUserConfigService.initUserConfig(adminUserPO.getUserId());
        if (StrUtil.isEmpty(adminUser.getRoleId())) {
            adminUserRoleService.saveByUserId(adminUserPO.getUserId());
        } else {
            adminUserRoleService.saveByUserId(adminUserPO.getUserId(), false, StrUtil.splitTrim(adminUser.getRoleId(), Const.SEPARATOR));
        }
        Long key = adminUserPO.getUserId();
        userCache.put(key, new SimpleUser(adminUserPO.getUserId(), adminUserPO.getImg(), adminUserPO.getRealname(), adminUserPO.getDeptId(), adminDeptService.getNameByDeptId(adminUserPO.getDeptId())));
    }

    /**
     * 修改用户信息
     *
     * @param adminUser
     */
    @Override
    public void updateUser(AdminUser adminUser) {
        if (!UserUtil.getUser().getUsername().equals(adminUser.getUsername())) {
            throw new CrmException(AdminCodeEnum.ADMIN_USERNAME_EDIT_ERROR);
        }
        adminUser.setUserId(UserUtil.getUserId());
        boolean b = false;
        if (StrUtil.isNotEmpty(adminUser.getPassword())) {
            if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, adminUser.getPassword())) {
                throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INTENSITY_ERROR);
            }
            b = true;
            adminUser.setSalt(IdUtil.simpleUUID());
            adminUser.setPassword(UserUtil.sign((adminUser.getUsername().trim() + adminUser.getPassword().trim()), adminUser.getSalt()));
        }
        adminUser.setStatus(null);
        boolean update = updateById(adminUser);
        AdminUser user = getById(adminUser.getUserId());
        SimpleUser simpleUser = new SimpleUser(user.getUserId(), user.getImg(), user.getRealname(), user.getDeptId(), adminDeptService.getNameByDeptId(user.getDeptId()));
//        jxcService.updateWarehouseCreateUserName(simpleUser);
        Long key = adminUser.getUserId();
        userCache.put(key, simpleUser);
//        jxcService.batchUpdateEsData(adminUser.getUserId().toString(), adminUser.getRealname(), "user");
        // 20220209 wwl ↑
//        crmService.batchUpdateEsData(adminUser.getUserId().toString(), adminUser.getRealname(), "user");
        if (b && update) {
            UserUtil.userExit(adminUser.getUserId(), null);
        }
    }

    /**
     * 修改用户账号功能
     *
     * @param id       用户ID
     * @param username 新的用户名
     * @param password 新的密码
     * @return 操作状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer usernameEdit(Long id, String username, String password) {
        if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, password)) {
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INTENSITY_ERROR);
        }
        AdminUser adminUser = getById(id);
        if (adminUser == null) {
            throw new CrmException(AdminCodeEnum.ADMIN_USER_NOT_EXIST_ERROR);
        }
        if (adminUser.getUsername().equals(username)) {
            throw new CrmException(AdminCodeEnum.ADMIN_ACCOUNT_ERROR);
        }
        Integer count = lambdaQuery().eq(AdminUser::getUsername, username).count();
        if (count > 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_PHONE_REGISTER_ERROR);
        }
        adminUser.setUsername(username);
        adminUser.setMobile(username);
        adminUser.setPassword(UserUtil.sign(username + password, adminUser.getSalt()));
        UserUtil.userExit(adminUser.getUserId(), null);
        updateById(adminUser);
        return null;
    }

    /**
     * excel导入员工
     *
     * @param file file
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject excelImport(MultipartFile file) {
        List<List<Object>> errList = new ArrayList<>();
        String filePath = getFilePath(file);
        AtomicReference<Integer> num = new AtomicReference<>(0);
        ExcelUtil.readBySax(filePath, 0, (int sheetIndex, long rowIndex, List<Object> rowList) -> {
            if (rowIndex > 1) {
                num.getAndSet(num.get() + 1);
                if (StrUtil.isEmptyIfStr(rowList.get(0))) {
                    rowList.add(0, "用户名不能为空");
                    errList.add(rowList);
                    return;
                }
                if (StrUtil.isEmptyIfStr(rowList.get(1))) {
                    rowList.add(0, "密码不能为空");
                    errList.add(rowList);
                    return;
                }
                if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, rowList.get(1).toString())) {
                    rowList.add(0, "密码必须由 6-20位字母、数字组成");
                    errList.add(rowList);
                    return;
                }

                int listIndexTwo = 2;
                if (StrUtil.isEmptyIfStr(rowList.get(listIndexTwo))) {
                    rowList.add(0, "姓名不能为空");
                    errList.add(rowList);
                    return;
                } else {
                    Integer count = query().eq("realname", rowList.get(2).toString().trim()).count();
                    if (count > 0) {
                        rowList.add(0, "姓名重复");
                        errList.add(rowList);
                        return;
                    }
                }
                String username = rowList.get(0).toString().trim();
                Integer count = lambdaQuery().eq(AdminUser::getUsername, username).count();
                if (count > 0) {
                    rowList.add(0, "手机号已存在");
                    errList.add(rowList);
                    return;
                }

                String phoneRegex = "^[1][3,4,5,6,7,8,9][0-9]{9}$";
                if (!ReUtil.isMatch(phoneRegex, username)) {
                    rowList.add(0, "手机号格式不正确");
                    errList.add(rowList);
                    return;
                }
                AdminUser adminUser = new AdminUser();
                int listSize = 6;
                if (rowList.size() < listSize) {
                    rowList.add(0, "部门不能为空");
                    errList.add(rowList);
                    return;
                }
                String deptNames = Optional.ofNullable(rowList.get(5)).orElse("").toString().trim();
                if (!StrUtil.isEmpty(deptNames)) {
                    String[] strArr = deptNames.split("/");
                    Long deptId = null;
                    for (int i = 0; i < strArr.length; i++) {
                        AdminDept dept;
                        if (i == 0) {
                            dept = adminDeptService.lambdaQuery().select(AdminDept::getDeptId)
                                    .eq(AdminDept::getName, strArr[0])
                                    .last("limit 1").one();

                        } else {
                            dept = adminDeptService.lambdaQuery().select(AdminDept::getDeptId)
                                    .eq(AdminDept::getName, strArr[i]).eq(AdminDept::getParentId, deptId)
                                    .last("limit 1").one();
                        }
                        if (dept == null) {
                            Integer a = i + 1;
                            rowList.add(0, a + "级部门不存在");
                            errList.add(rowList);
                            return;
                        }
                        deptId = dept.getDeptId();
                    }

                    if (deptId == null) {
                        rowList.add(0, "部门不存在");
                        errList.add(rowList);
                        return;
                    }
                    adminUser.setDeptId(deptId);

                } else {
                    rowList.add(0, "部门不能为空");
                    errList.add(rowList);
                    return;
                }

                String password = rowList.get(1).toString().trim();
                String realname = rowList.get(2).toString().trim();
                String sex = null;
                int listIndexThree = 3;
                if (rowList.get(listIndexThree) != null) {
                    sex = Optional.ofNullable(rowList.get(3)).orElse("").toString().trim();
                }

                String email = null;
                int listIndexFour = 4;
                if (rowList.get(listIndexFour) != null) {
                    email = Optional.ofNullable(rowList.get(4)).orElse("").toString().trim();
                }
                String post = null;
                int listIndexSix = 6;
                if (rowList.size() > listIndexSix) {
                    post = Optional.ofNullable(rowList.get(6)).orElse("").toString().trim();
                }
                String salt = IdUtil.fastSimpleUUID();
                adminUser.setUsername(username);
                adminUser.setPassword(UserUtil.sign((adminUser.getUsername().trim() + password.trim()), salt));
                adminUser.setSalt(salt);
                adminUser.setNum(RandomUtil.randomNumbers(15));
                adminUser.setCreateTime(LocalDateTimeUtil.now());
                adminUser.setRealname(realname);
                adminUser.setMobile(username);
                adminUser.setEmail(email);
                adminUser.setPost(post);
                adminUser.setStatus(0);
                if (StrUtil.isNotEmpty(sex)) {
                    String woman = "女";
                    if (woman.equals(sex)) {
                        adminUser.setSex(2);
                    } else {
                        adminUser.setSex(1);
                    }
                }
                save(adminUser);
                adminUserConfigService.initUserConfig(UserUtil.getUserId());
            } else {
                if (rowIndex == 1) {
                    rowList.add(0, "错误信息");
                }
                errList.add(Convert.toInt(rowIndex), rowList);
            }
        });
        FileUtil.del(filePath);
        JSONObject result = new JSONObject().fluentPut("totalSize", num.get()).fluentPut("errSize", 0);
        int listSize = 2;
        if (errList.size() > listSize) {
            BigExcelWriter writer = null;
            try {
                String token = IdUtil.simpleUUID();
                writer = ExcelUtil.getBigWriter(FileUtil.getTmpDirPath() + "/" + token);
                // 取消数据的黑色边框以及数据左对齐
                CellStyle cellStyle = writer.getCellStyle();
                cellStyle.setFillBackgroundColor(IndexedColors.BLACK.getIndex());
                cellStyle.setBorderTop(BorderStyle.NONE);
                cellStyle.setBorderBottom(BorderStyle.NONE);
                cellStyle.setBorderLeft(BorderStyle.NONE);
                cellStyle.setBorderRight(BorderStyle.NONE);
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                Font defaultFont = writer.createFont();
                defaultFont.setFontHeightInPoints((short) 11);
                cellStyle.setFont(defaultFont);
                // 取消数字格式的数据的黑色边框以及数据左对齐
                CellStyle cellStyleForNumber = writer.getStyleSet().getCellStyleForNumber();
                cellStyleForNumber.setBorderTop(BorderStyle.NONE);
                cellStyleForNumber.setBorderBottom(BorderStyle.NONE);
                cellStyleForNumber.setBorderLeft(BorderStyle.NONE);
                cellStyleForNumber.setBorderRight(BorderStyle.NONE);
                cellStyleForNumber.setAlignment(HorizontalAlignment.LEFT);
                cellStyleForNumber.setFillBackgroundColor(IndexedColors.BLACK.getIndex());
                cellStyleForNumber.setFont(defaultFont);

                CellStyle textStyle = writer.getWorkbook().createCellStyle();
                DataFormat format = writer.getWorkbook().createDataFormat();
                textStyle.setDataFormat(format.getFormat("@"));

                writer.merge(errList.get(1).size() + 1, errList.get(0).get(0).toString().trim(), true);
                writer.getHeadCellStyle().setAlignment(HorizontalAlignment.LEFT);
                writer.getHeadCellStyle().setWrapText(true);
                Font headFont = writer.createFont();
                headFont.setFontHeightInPoints((short) 11);
                writer.getHeadCellStyle().setFont(headFont);
                writer.getHeadCellStyle().setFillPattern(FillPatternType.NO_FILL);
                writer.getOrCreateRow(0).setHeightInPoints(120);
                writer.setRowHeight(-1, 20);

                //writer.merge(6, "系统用户导入模板(*)为必填项");
                for (int i = 0; i < errList.get(1).size(); i++) {
                    writer.getSheet().setDefaultColumnStyle(i, textStyle);
                }
                errList.remove(0);
                writer.write(errList);
                result.fluentPut("errSize", errList.size() - 1).fluentPut("token", token);
            } finally {
                if (writer != null) {
                    writer.close();
                }
            }
        }
        return result;
    }

    private String getFilePath(MultipartFile file) {
        String dirPath = FileUtil.getTmpDirPath();
        try {
            InputStream inputStream = file.getInputStream();
            File fromStream = FileUtil.writeFromStream(inputStream, dirPath + "/" + IdUtil.simpleUUID() + file.getOriginalFilename());
            return fromStream.getAbsolutePath();
        } catch (IOException e) {
            throw new CrmException(SystemCodeEnum.SYSTEM_UPLOAD_FILE_ERROR);
        }
    }

    /**
     * 设置状态
     *
     * @param adminUserStatusBO status
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setUserStatus(AdminUserStatusBO adminUserStatusBO) {
        for (Long id : adminUserStatusBO.getIds()) {
            if (BaseStatusEnum.CLOSE.getStatus().equals(adminUserStatusBO.getStatus())) {
                if (id.equals(UserUtil.getSuperUser())) {
                    throw new CrmException(AdminCodeEnum.ADMIN_SUPER_USER_DISABLED_ERROR);
                }
                UserUtil.userExit(id, null);
            } else if (BaseStatusEnum.OPEN.getStatus().equals(adminUserStatusBO.getStatus())) {
                Integer roleCount = adminUserRoleService.lambdaQuery().eq(AdminUserRole::getUserId, id).count();
                if (roleCount == 0) {
                    throw new CrmException(AdminCodeEnum.ADMIN_USER_NOT_ROLE_ERROR);
                }
                AdminUser adminUser = getById(id);
                if (adminUser.getDeptId() == null) {
                    throw new CrmException(AdminCodeEnum.ADMIN_USER_NOT_DEPT_ERROR);
                }
            }
            lambdaUpdate().set(AdminUser::getStatus, adminUserStatusBO.getStatus()).eq(AdminUser::getUserId, id).update();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetPassword(AdminUserStatusBO adminUserStatusBO) {
        if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, adminUserStatusBO.getPassword())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INTENSITY_ERROR);
        }
        for (Long id : adminUserStatusBO.getIds()) {
            AdminUser adminUser = getById(id);
            String password = UserUtil.sign(adminUser.getUsername() + adminUserStatusBO.getPassword(), adminUser.getSalt());
            lambdaUpdate().set(AdminUser::getPassword, password).eq(AdminUser::getUserId, id).update();
            UserUtil.userExit(adminUser.getUserId(), null);
        }
    }

    /**
     * 根据用户ID查询角色ID
     *
     * @param userId userId
     * @return ids
     */
    @Override
    public List<Long> queryUserRoleIds(Long userId) {
        QueryWrapper<AdminUserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("role_id").eq("user_id", userId);
        return adminUserRoleService.listObjs(queryWrapper, obj -> Long.valueOf(obj.toString()));
    }

    /**
     * 通讯录查询
     *
     * @param userBookBO data
     * @return
     */
    @Override
    public BasePage<UserBookVO> queryListName(UserBookBO userBookBO) {
        userBookBO.setUserId(UserUtil.getUserId());
        return getBaseMapper().queryListName(userBookBO.parse(), userBookBO);
    }

    private static final List<Character> BASE_CHAR = Arrays.asList('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');

    /**
     * 查询组织架构下员工信息
     *
     * @return 组织架构信息
     */
    @Override
    public AdminOrganizationVO queryOrganizationInfo() {
        AdminOrganizationVO adminOrganizationVO = new AdminOrganizationVO();
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUser::getUserId, AdminUser::getImg, AdminUser::getRealname, AdminUser::getDeptId, AdminUser::getStatus);
        //将用户分组，禁用的员工只在禁用员工列表显示
        Map<Integer, List<SimpleUser>> collect = list(queryWrapper).stream().map(data -> BeanUtil.copyProperties(data, SimpleUser.class)).collect(Collectors.groupingBy(SimpleUser::getStatus));
        adminOrganizationVO.setDisableUserList(Optional.ofNullable(collect.get(0)).orElse(Collections.emptyList()));
        //未激活员工和正常员工相同的处理逻辑
        List<SimpleUser> userList = collect.get(1);
        userList.addAll(Optional.ofNullable(collect.get(2)).orElse(Collections.emptyList()));
        //处理字母排序逻辑
        Map<Character, List<SimpleUser>> userMap = userList.stream().collect(Collectors.groupingBy(data -> {
            String realName = data.getRealname();
            char character = realName.charAt(0);
            if (PinyinUtil.isChinese(character)) {
                //转为大写字母
                return (char) (PinyinUtil.getFirstLetter(character) - 32);
            } else if (BASE_CHAR.contains(character)) {
                char start = 'a';
                char ending = 'z';
                if (character >= start && character <= ending) {
                    character -= 32;
                }
                return character;
            } else {
                // ^的ASCII码为94，大于A-Z，先使用这个，再替换为 #
                return '^';
            }
        }));
        // ^的ASCII码为94，大于A-Z，先使用这个，再替换为 #
        LinkedHashMap<Character, List<SimpleUser>> linkedHashMap = new LinkedHashMap<>(userMap);
        linkedHashMap.put('#', Optional.ofNullable(linkedHashMap.remove('^')).orElse(Collections.emptyList()));
        adminOrganizationVO.setUserMap(linkedHashMap);

        Map<Long, List<SimpleUser>> deptMap = userList.stream().collect(Collectors.groupingBy(SimpleUser::getDeptId));
        //处理部门信息
        LambdaQueryWrapper<AdminDept> deptQueryWrapper = new LambdaQueryWrapper<>();
        deptQueryWrapper.select(AdminDept::getDeptId, AdminDept::getParentId, AdminDept::getName);
        List<AdminDeptVO> deptVOList = adminDeptService.list(deptQueryWrapper).stream().map(data -> BeanUtil.copyProperties(data, AdminDeptVO.class)).collect(Collectors.toList());
        for (AdminDeptVO deptVO : deptVOList) {
            Long deptId = deptVO.getDeptId();
            if (deptMap.containsKey(deptId)) {
                deptVO.setUserList(deptMap.get(deptId).stream().peek(data -> data.setDeptName(deptVO.getName())).collect(Collectors.toList()));
            }
        }
        List<AdminDeptVO> childList = RecursionUtil.getChildListTree(deptVOList, "parentId", 0L, "deptId", "children", AdminDeptVO.class);
        adminOrganizationVO.setDeptList(childList);
        return adminOrganizationVO;

    }

    /**
     * 切换关注状态
     *
     * @param userId 用户ID 0代表全部
     * @return
     */
    @Override
    public void attention(Long userId) {
        QueryWrapper<AdminAttention> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("be_user_id", userId);
        int count = adminAttentionService.count(queryWrapper);
        if (count > 0) {
            adminAttentionService.remove(queryWrapper);
        } else {
            AdminAttention attention = new AdminAttention();
            attention.setBeUserId(userId);
            attention.setAttentionUserId(UserUtil.getUserId());
            adminAttentionService.save(attention);
        }
    }

    /**
     * 根据ids查询用户信息
     *
     * @param ids id列表
     * @return data
     */
    @Override
    public List<SimpleUser> queryUserByIds(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUser::getUserId, AdminUser::getImg, AdminUser::getRealname).in(AdminUser::getUserId, ids);
        List<AdminUser> list = list(queryWrapper);
        return list.stream().map(obj -> BeanUtil.copyProperties(obj, SimpleUser.class)).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryNormalUserByIds(List<Long> ids) {
        List<Long> userIdList = new ArrayList<>();
        if (ids.size() == 0) {
            return userIdList;
        }
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUser::getUserId, AdminUser::getImg, AdminUser::getRealname)
                .in(AdminUser::getUserId, ids)
                .eq(AdminUser::getStatus, 1);
        List<AdminUser> list = list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            userIdList = list.stream().map(AdminUser::getUserId).collect(Collectors.toList());
        }
        return userIdList;
    }

    /**
     * 根据部门ids查询用户列表
     *
     * @param ids id列表
     * @return data
     */
    @Override
    public List<Long> queryUserByDeptIds(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUser::getUserId);
        if (ids.size() > 1) {
            queryWrapper.in(AdminUser::getDeptId, ids);
        } else {
            queryWrapper.eq(AdminUser::getDeptId, ids.get(0));
        }
        return listObjs(queryWrapper, obj -> Long.valueOf(obj.toString()));
    }

    @Override
    public List<SimpleUser> queryListByDeptIds(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AdminUser::getUserId, AdminUser::getUsername, AdminUser::getDeptId, AdminUser::getRealname);
        queryWrapper.in(AdminUser::getDeptId, deptIds);
        List<AdminUser> users = list(queryWrapper);
        return users.stream().map(u -> {
            SimpleUser user = new SimpleUser();
            user.setUserId(u.getUserId());
            user.setRealname(u.getRealname());
            user.setDeptId(u.getDeptId());
            user.setDeptName(u.getDeptName());
            return user;
        }).collect(Collectors.toList());
    }

//    @Autowired
//    @Lazy
//    private HrmService hrmService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hrmAddUser(HrmAddUserBO hrmAddUserBO) {
//        Result<Set<HrmEmployee>> listResult = hrmService.queryEmployeeListByIds(hrmAddUserBO.getEmployeeIds());
//        Set<HrmEmployee> employeeList = listResult.getData();
//        for (HrmEmployee hrmEmployee : employeeList) {
//            AdminUserVO adminUserVO = new AdminUserVO();
//            adminUserVO.setRealname(hrmEmployee.getEmployeeName());
//            adminUserVO.setUsername(hrmEmployee.getMobile());
//            adminUserVO.setSex(hrmEmployee.getSex());
//            adminUserVO.setMobile(hrmEmployee.getMobile());
//            adminUserVO.setPassword(hrmAddUserBO.getPassword());
//            adminUserVO.setEmail(hrmEmployee.getEmail());
//            adminUserVO.setDeptId(hrmAddUserBO.getDeptId());
//            adminUserVO.setParentId(hrmAddUserBO.getParentId());
//            adminUserVO.setRoleId(hrmAddUserBO.getRoleId());
//            adminUserVO.setPost(hrmEmployee.getPost());
//            addUser(adminUserVO);
//        }
        return;
    }

    @Override
    public DeptUserListVO queryDeptUserList(Long deptId, boolean isAllUser) {
        DeptUserListVO deptUserListVO = new DeptUserListVO();
        List<DeptVO> deptList = adminDeptService.queryDeptUserList();
        createTree(deptId, deptList);
        List<HrmSimpleUserVO> userList;
        if (isAllUser) {
            userList = getBaseMapper().querySimpleUserByDeptId(deptId);
        } else {
            userList = getBaseMapper().querySimpleUserByDeptIdAndExamine(deptId);
        }
        List<DeptVO> collect = deptList.stream().filter(dept -> dept.getParentId().equals(deptId)).collect(Collectors.toList());
        deptUserListVO.setDeptList(collect);
        deptUserListVO.setUserList(userList);
        return deptUserListVO;
    }

    private List<DeptVO> createTree(Long pid, List<DeptVO> deptList) {
        List<DeptVO> treeDept = new ArrayList<>();
        for (DeptVO dept : deptList) {
            if (ObjectUtil.equal(pid, dept.getParentId())) {
                treeDept.add(dept);
                List<DeptVO> children = createTree(dept.getDeptId(), deptList);
                if (CollUtil.isNotEmpty(children)) {
                    for (DeptVO child : children) {
                        dept.setAllNum(dept.getAllNum() + child.getAllNum());
                    }
                    dept.setHasChildren(1);
                } else {
                    dept.setHasChildren(0);
                }
            }
        }
        return treeDept;
    }

    @Override
    public Set<HrmSimpleUserVO> queryDeptUserListByHrm(DeptUserListByHrmBO deptUserListByHrmBO) {
        Set<HrmSimpleUserVO> userVOSet = new HashSet<>();
        if (CollUtil.isNotEmpty(deptUserListByHrmBO.getDeptIdList())) {
            List<AdminUser> userList = findChildUserList(deptUserListByHrmBO.getDeptIdList());
            List<HrmSimpleUserVO> hrmSimpleUserVOS = TransferUtil.transferList(userList, HrmSimpleUserVO.class);
            userVOSet.addAll(hrmSimpleUserVOS);
        }
        if (CollUtil.isNotEmpty(deptUserListByHrmBO.getUserIdList())) {
            List<AdminUser> userList = query().select("user_id", "realname", "img", "sex", "username as mobile", "post").in("user_id", deptUserListByHrmBO.getUserIdList())
                    .ne("status", 0).list();
            List<HrmSimpleUserVO> hrmSimpleUserVOS = TransferUtil.transferList(userList, HrmSimpleUserVO.class);
            userVOSet.addAll(hrmSimpleUserVOS);
        }
        return userVOSet;
    }

    private List<AdminUser> findChildUserList(List<Long> deptIds) {
        List<AdminUser> empList = new ArrayList<>();
        for (Long deptId : deptIds) {
            List<AdminUser> list = query().select("user_id", "realname", "img", "sex", "username as mobile", "post").eq("dept_id", deptId).ne("status", 0).list();
            empList.addAll(list);
            List<AdminDept> childList = adminDeptService.lambdaQuery().select(AdminDept::getDeptId).eq(AdminDept::getParentId, deptId).list();
            if (CollUtil.isNotEmpty(childList)) {
                List<Long> childDeptIds = childList.stream().map(AdminDept::getDeptId).collect(Collectors.toList());
                empList.addAll(findChildUserList(childDeptIds));
            }
        }
        return empList;
    }

    @Override
    public List<Long> queryUserIdByRealName(List<String> realNames) {
        if (CollUtil.isEmpty(realNames)) {
            return new ArrayList<>();
        }
        return lambdaQuery().select(AdminUser::getUserId)
                .in(AdminUser::getRealname, realNames)
                .list().stream().map(AdminUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public UserInfo queryLoginUserInfo(Long userId) {
        UserInfo userInfo = getBaseMapper().queryLoginUserInfo(userId);
        userInfo.setSuperUserId(UserUtil.getSuperUser());
        userInfo.setSuperRoleId(UserUtil.getSuperRole());
        userInfo.setRoles(queryUserRoleIds(userInfo.getUserId()));
        return userInfo;
    }

    /**
     * 查询所有员工
     *
     * @return
     */
    @Override
    public List<UserInfo> queryAllUserInfoList() {
        List<AdminUser> adminUserList = lambdaQuery().list();
        for (AdminUser user : adminUserList) {
            String deptName = UserCacheUtil.getDeptName(user.getDeptId());
            user.setDeptName(deptName);
        }
        List<AdminUserRole> userRoles = adminUserRoleService.query().list();
        Map<Long, List<AdminUserRole>> longListMap = userRoles.stream().collect(Collectors.groupingBy(AdminUserRole::getUserId));
        List<UserInfo> userInfoList = adminUserList.stream().map(user -> BeanUtil.copyProperties(user, UserInfo.class)).collect(Collectors.toList());
        for (UserInfo userInfo : userInfoList) {
            List<AdminUserRole> roleList = longListMap.get(userInfo.getUserId());
            if (roleList == null) {
                roleList = new ArrayList<>();
            }
            userInfo.setRoles(roleList.stream().map(AdminUserRole::getRoleId).collect(Collectors.toList()));
        }
        return userInfoList;
    }

    @Override
    public void downloadExcel(HttpServletResponse response) throws IOException {
        List<JSONObject> list = queryField();
        ExcelParseUtil.importExcel(new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "员工";
            }
        }, list, response, "user");
    }

    @Override
    public UserInfoVO queryUserDeptOrRoleInfo(UserInfoRequestBO requestBO) {
        UserInfoVO infoVO = new UserInfoVO();
        if (CollUtil.isNotEmpty(requestBO.getUserIds())) {
            List<AdminUser> adminUsers = lambdaQuery()
                    .select(AdminUser::getUserId, AdminUser::getRealname, AdminUser::getImg)
                    .in(AdminUser::getUserId, requestBO.getUserIds()).list();
            List<SimpleUser> simpleUsers = adminUsers.stream().map(a -> {
                SimpleUser user = new SimpleUser();
                user.setUserId(a.getUserId());
                user.setRealname(a.getRealname());
                user.setImg(a.getImg());
                return user;
            }).collect(Collectors.toList());
            infoVO.setUsers(simpleUsers);
        }
        if (CollUtil.isNotEmpty(requestBO.getDeptIds())) {
            List<AdminDept> adminDepts = adminDeptService.lambdaQuery()
                    .select(AdminDept::getDeptId, AdminDept::getName)
                    .in(AdminDept::getDeptId, requestBO.getDeptIds()).list();
            List<SimpleDept> simpleDepts = adminDepts.stream().map(a -> {
                SimpleDept simpleDept = new SimpleDept();
                simpleDept.setId(a.getDeptId());
                simpleDept.setName(a.getName());
                return simpleDept;
            }).collect(Collectors.toList());
            infoVO.setDepts(simpleDepts);
        }
        if (CollUtil.isNotEmpty(requestBO.getRoleIds())) {
            List<AdminRole> adminRoles = adminRoleService
                    .lambdaQuery().select(AdminRole::getRoleId, AdminRole::getRoleName)
                    .in(AdminRole::getRoleId, requestBO.getRoleIds()).list();
            List<Map<String, Object>> roles = adminRoles.stream().map(a -> {
                Map<String, Object> role = new HashMap<>();
                role.put("roleId", a.getRoleId());
                role.put("roleName", a.getRoleName());
                return role;
            }).collect(Collectors.toList());
            infoVO.setRoles(roles);
        }
        return infoVO;
    }

    /**
     * 财务管理查询用户及角色根据用户id
     *
     * @param userIds
     * @return
     */
    @Override
    public List<Map<String, Object>> getUserByIds(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> dataMap = baseMapper.getUserByIds(userIds);
        for (Map map : dataMap) {
            //根据用户id查询角色为财务管理的角色
            List<Long> roleList = baseMapper.getRoleByUserId(Convert.toLong(map.get("userId")), AdminRoleTypeEnum.FINANCE.getType());
            roleList.forEach(ro -> {
                map.put(ro, true);
            });
        }
        return dataMap;
    }

    private List<JSONObject> queryField() {
        List<JSONObject> list = new ArrayList<>();
        list.add(queryField("username", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "手机号", 1));
        list.add(queryField("password", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "登录密码", 1));
        list.add(queryField("realname", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "姓名", 1));
        list.add(queryField("sex", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "性别", 0));
        list.add(queryField("email", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "邮箱", 0));
        list.add(queryField("deptName", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "部门", 1));
        list.add(queryField("post", FieldEnum.TEXT.getFormType(), FieldEnum.TEXT.getType(), "岗位", 0));
        return list;
    }

    private JSONObject queryField(String fieldName, String formType, Integer type, String name, Integer isNull) {
        JSONObject json = new JSONObject();
        json.fluentPut("fieldName", fieldName)
                .fluentPut("formType", formType)
                .fluentPut("type", type)
                .fluentPut("name", name).fluentPut("isNull", isNull);
        return json;
    }

    @Override
    public List<UserInfo> queryParentByLevel(AdminUserQueryBO queryBO) {
        List<UserInfo> result = new ArrayList<>();
        List<UserInfo> users = queryAllUserInfoList();
        List<Integer> levels = queryBO.getLevels();
        CollUtil.sort(levels, (Comparator.comparingInt(o -> o)));
        for (Integer level : levels) {
            UserInfo userInfo = this.queryParentByLevel(users, queryBO.getUserId(), level);
            if (ObjectUtil.isNotNull(userInfo)) {
                result.add(userInfo);
            }
        }
        return result;
    }

    /**
     * 查询当前系统有没有初始化
     *
     * @return data
     */
    @Override
    public Integer querySystemStatus() {
        Integer count = lambdaQuery().count();
        return count > 0 ? 1 : 0;
    }

    /**
     * 系统用户初始化
     */
    @Override
    public void initUser(SystemUserBO systemUserBO) {
        Integer integer = querySystemStatus();
        if (integer > 0) {
            return;
        }
        JSONObject jsonObject = null;
        try {
            RSA rsa = SecureUtil.rsa((String) null, AdminConst.userPublicKey);
            String fromBcd = rsa.decryptStrFromBcd(systemUserBO.getCode(), KeyType.PublicKey);
            jsonObject = JSON.parseObject(fromBcd);
        } catch (Exception e) {
            throw new CrmException(AdminCodeEnum.ADMIN_PHONE_VERIFY_ERROR);
        }
        if (jsonObject == null) {
            throw new CrmException(AdminCodeEnum.ADMIN_PHONE_VERIFY_ERROR);
        }

        AdminUser adminUser = new AdminUser();
        adminUser.setUsername(systemUserBO.getUsername());
        adminUser.setSalt(IdUtil.simpleUUID());
        adminUser.setPassword(UserUtil.sign(systemUserBO.getUsername() + systemUserBO.getPassword(), adminUser.getSalt()));
        adminUser.setCreateTime(LocalDateTimeUtil.now());
        adminUser.setRealname("admin");
        adminUser.setMobile(systemUserBO.getUsername());
        adminUser.setDeptId(14852L);
        adminUser.setPost("标准岗位");
        adminUser.setStatus(1);
        adminUser.setParentId(0L);
        save(adminUser);
        lambdaUpdate().set(AdminUser::getUserId, UserUtil.getSuperUser())
                .eq(AdminUser::getUserId, adminUser.getUserId()).update();
        adminUserConfigService.save(new AdminUserConfig(
                null,
                UserUtil.getSuperUser(),
                1,
                "InitUserConfig",
                jsonObject.toJSONString(),
                "用户信息",
                null,
                null,
                LocalDateTimeUtil.now(),
                LocalDateTimeUtil.now())
        );
        registerSeataToNacos();
    }

    private UserInfo queryParentByLevel(List<UserInfo> users, Long userId, Integer level) {
        if (level <= 0) {
            return null;
        }
        level--;
        UserInfo currentUser = users.stream().filter(userInfo -> ObjectUtil.equal(userId, userInfo.getUserId())).findFirst().orElse(null);
        if (ObjectUtil.isNotNull(currentUser)) {
            Long parentId = currentUser.getParentId();
            if (ObjectUtil.isNotNull(parentId) && ObjectUtil.notEqual(0, parentId)) {
                if (level == 0) {
                    return users.stream().filter(userInfo -> ObjectUtil.equal(parentId, userInfo.getUserId())).findFirst().orElse(null);
                } else {
                    return queryParentByLevel(users, parentId, level);
                }
            }
        }
        return null;
    }

    @Override
    public void clearUserCache(Long... userIds) {
        if (userIds == null) {
            return;
        }
        for (Long userId : userIds) {
            userCache.remove(userId);
        }
    }


    @Value("${seata.config.nacos.serverAddr}")
    private String serverAddr;

    @Autowired
    private DataSourceProperties dataSourceProperties;
    private void registerSeataToNacos() {
        Map<String, String> nacosMap = new HashMap<>();
        nacosMap.put("service.vgroupMapping.crm_tx_group", "default");
        nacosMap.put("service.vgroupMapping.admin_tx_group", "default");
        nacosMap.put("service.vgroupMapping.examine_tx_group", "default");
        nacosMap.put("service.vgroupMapping.oa_tx_group", "default");
        nacosMap.put("service.vgroupMapping.hrm_tx_group", "default");
        nacosMap.put("service.vgroupMapping.jxc_tx_group", "default");
        nacosMap.put("store.mode", "db");
        nacosMap.put("store.db.datasource", "druid");
        nacosMap.put("store.db.dbType", "mysql");
        nacosMap.put("store.db.driverClassName", "com.mysql.jdbc.Driver");
        String host = dataSourceProperties.getUrl().replace("jdbc:mysql://", "").split(":")[0];
        nacosMap.put("store.db.url", "jdbc:mysql://" + host + ":3306/seata?useUnicode=true");
        nacosMap.put("store.db.user", dataSourceProperties.getUsername());
        nacosMap.put("store.db.password", dataSourceProperties.getPassword());
        nacosMap.put("store.db.minConn", "5");
        nacosMap.put("store.db.maxConn", "30");
        nacosMap.put("store.db.globalTable", "global_table");
        nacosMap.put("store.db.branchTable", "branch_table");
        nacosMap.put("store.db.queryLimit", "100");
        nacosMap.put("store.db.lockTable", "lock_table");
        nacosMap.put("store.db.maxWait", "5000");
        String group = "SEATA_GROUP";
        nacosMap.forEach((k, v) -> {
            try {
                Properties properties = new Properties();
                properties.put("serverAddr", serverAddr);
                ConfigService configService = NacosFactory.createConfigService(properties);
                boolean isPublishOk = configService.publishConfig(k, group, v);
                log.warn("seata初始化：{}", isPublishOk);
            } catch (NacosException e) {
                log.error("同步seata失败", e);
            }
        });

    }


    //===========================================提供商城用户接口开始===========================================

    @Override
    public List<UserInfo> seleListForMall(UserDTO userDTO) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        if(CollectionUtil.isNotEmpty(userDTO.getUserIds())){
            queryWrapper.in(AdminUser::getUserId,userDTO.getUserIds());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getDeptId())){
            queryWrapper.eq(AdminUser::getDeptId,userDTO.getDeptId());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getRealname())){
            queryWrapper.like(AdminUser::getRealname,userDTO.getRealname());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getPhone())){
            queryWrapper.like(AdminUser::getMobile,userDTO.getPhone());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getStatus())){
            queryWrapper.eq(AdminUser::getStatus,userDTO.getStatus());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getCreateStartTime())){
            queryWrapper.ge(AdminUser::getCreateTime,userDTO.getCreateStartTime());
        }
        if(ObjectUtil.isNotEmpty(userDTO.getCreateEndTime())){
            queryWrapper.le(AdminUser::getCreateTime,userDTO.getCreateEndTime());
        }
        List<AdminUser> adminUserList = baseMapper.selectList(queryWrapper);
        for (AdminUser user : adminUserList) {
            String deptName = UserCacheUtil.getDeptName(user.getDeptId());
            user.setDeptName(deptName);
        }

        List<AdminUserRole> userRoles = adminUserRoleService.query().list();
        Map<Long, List<AdminUserRole>> longListMap = userRoles.stream().collect(Collectors.groupingBy(AdminUserRole::getUserId));
        List<UserInfo> userInfoList = adminUserList.stream().map(user -> BeanUtil.copyProperties(user, UserInfo.class)).collect(Collectors.toList());
        for (UserInfo userInfo : userInfoList) {
            List<AdminUserRole> roleList = longListMap.get(userInfo.getUserId());
            if (roleList == null) {
                roleList = new ArrayList<>();
            }
            userInfo.setRoles(roleList.stream().map(AdminUserRole::getRoleId).collect(Collectors.toList()));
        }
        return userInfoList;
    }
    //===========================================提供商城用户接口结束===========================================
}
