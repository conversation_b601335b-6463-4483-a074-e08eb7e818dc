package com.kakarote.admin.service;

import com.kakarote.admin.entity.PO.AdminUserHisTable;
import com.kakarote.core.servlet.BaseService;

import java.util.List;

/**
 * <p>
 * 授权坐席 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface IAdminUserHisTableService extends BaseService<AdminUserHisTable> {

    /**
     * 员工坐席授权
     *
     * @param userIds:用户idList
     * @param status:状态
     * @param hisUse:hisUse
     * @return boolean
     */
    boolean authorize(List<Long> userIds, Integer status, Integer hisUse);

    /**
     * 判断用户是否为坐席
     *
     * @return
     */
    public Integer checkAuth();
}
