package com.kakarote.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.kakarote.admin.common.AdminCodeEnum;
import com.kakarote.admin.common.AdminModuleEnum;
import com.kakarote.admin.common.AdminRoleTypeEnum;
import com.kakarote.admin.entity.BO.AdminUserRoleBO;
import com.kakarote.admin.entity.PO.*;
import com.kakarote.admin.entity.VO.AdminRoleVO;
import com.kakarote.admin.mapper.AdminRoleMapper;
import com.kakarote.admin.service.*;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.common.enums.DataAuthEnum;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.service.CrmService;
import com.kakarote.core.feign.hrm.service.HrmService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
public class AdminRoleServiceImpl extends BaseServiceImpl<AdminRoleMapper, AdminRole> implements IAdminRoleService {

    @Autowired
    private IAdminMenuService adminMenuService;

    @Autowired
    private IAdminConfigService adminConfigService;

    @Autowired
    private IAdminUserRoleService adminUserRoleService;

    @Autowired
    private IAdminRoleMenuService adminRoleMenuService;

    @Autowired
    private IAdminUserService adminUserService;

    @Autowired
    private IAdminDeptService adminDeptService;

    @Autowired
    private IAdminRoleAuthService adminRoleAuthService;

//    @Autowired
//    @Lazy
//    private CrmService crmService;

    @Autowired
    private Redis redis;

//    @Autowired
//    @Lazy
//    private HrmService hrmService;

    @Autowired
    private IAdminConfigService configService;

    /**
     * 管理员枚举
     */
    private static final String ADMIN_NUM = "adminNum";

    /**
     * 用户枚举
     */
    private static final String USER_NUM = "userNum";

    /**
     * 进销存模块
     */
    private static final String JXC = "jxc";

    /**
     * bi模块
     */
    private static final String BI = "bi";

    /**
     * hrm模块
     */
    private static final String HRM = "hrm";

    private static final String CP = "cp";

    /**
     * 正则判断
     */
    private static final String CANONICAL = "^[(]\\d+[)]$";

    private static final String LEFT_BRACKET = "(";

    private static final String RIGHT_BRACKET = ")";

    private static final int NUMBER = 935;

    /**
     * 查询用户所属权限
     *
     * @return obj
     */
    @Override
    public JSONObject auth(Long userId) {
        String cacheKey = AdminCacheKey.USER_AUTH_CACHE_KET + UserUtil.getUserId();
        if (redis.exists(cacheKey)) {
            return redis.get(cacheKey);
        }
        List<AdminMenu> adminMenus = adminMenuService.queryMenuList(userId);
        List<AdminMenu> menus = adminMenuService.list();
        for (int i = 0; i < menus.size(); i++) {
            if (Objects.equals(0L, menus.get(i).getParentId())) {
                adminMenus.add(menus.get(i));
                for (AdminMenu menu : menus) {
                    if (Objects.equals(menu.getParentId(), menus.get(i).getMenuId())) {
                        adminMenus.add(menu);
                    }
                }
            }
        }
        JSONObject jsonObject = createMenu(new HashSet<>(adminMenus), 0L);
        List<AdminConfig> adminConfigList = adminConfigService.queryConfigListByName((Object[]) AdminModuleEnum.getValues());

        //为crm模块根据用户权限添加公海权限
        if (jsonObject.containsKey(AdminModuleEnum.CRM.getValue())) {
            JSONObject authObject = new JSONObject();
            UserInfo userInfo = UserUtil.getUser();
            Map<String, Long> read = adminMenuService.queryPoolReadAuth(userInfo.getUserId(), userInfo.getDeptId());
            if (UserUtil.isAdmin() || read.get(ADMIN_NUM) > 0 || read.get(USER_NUM) > 0) {
                authObject.fluentPut("index", true).fluentPut("receive", true);
                if (UserUtil.isAdmin() || read.get(ADMIN_NUM) > 0) {
                    authObject.fluentPut("distribute", true).fluentPut("excelexport", true).fluentPut("delete", true);
                }
            }
            jsonObject.getJSONObject(AdminModuleEnum.CRM.getValue()).put("pool", authObject);
//            List data = crmService.queryPoolNameListByAuth().getData();
//            if (CollUtil.isEmpty(data)) {
//                JSONObject crm = jsonObject.getJSONObject("crm");
//                if (crm != null) {
//                    crm.remove("pool");
//                    jsonObject.put("crm", crm);
//                }
//            }
        }

        if (jsonObject.containsKey(JXC)) {
            JSONObject jxc = jsonObject.getJSONObject(JXC);
            if (jxc.containsKey(BI) && !jxc.getJSONObject(BI).isEmpty()) {
                JSONObject jxcBi = jxc.getJSONObject(BI);
                jxc.remove(BI);
                if (jsonObject.containsKey(BI)) {
                    JSONObject bi = jsonObject.getJSONObject(BI);
                    bi.putAll(jxcBi);
                    jsonObject.put("bi", bi);
                } else {
                    jsonObject.put("bi", jxcBi);
                }
            } else if (jxc.isEmpty()) {
                jsonObject.remove(JXC);
            }
        }
        //判断当前用户是否有角色
        List<AdminUserRole> userRoles = adminUserRoleService.lambdaQuery()
                .eq(AdminUserRole::getUserId, userId).list();
        if (userRoles.size() == 0) {
            jsonObject.put(AdminModuleEnum.OA.getValue(), new JSONObject());
        }
        /*
          循环模块配置，把禁用的模块菜单隐藏掉
         */
        adminConfigList.forEach(adminConfig -> {
            //是否开启该模块
            Integer status = adminConfig.getStatus();
            //需要特殊处理的模块
            /*if (AdminModuleEnum.CALL.getValue().equals(adminConfig.getName())) {
                JSONObject object = jsonObject.getJSONObject(AdminModuleEnum.BI.getValue());
                if (object != null && status != 1) {
                    object.remove(AdminModuleEnum.CALL.getValue());
                }
                return;
            }*/
            //需要特殊处理的模块
            List<String> oaArray = Arrays.asList(AdminModuleEnum.TASK_EXAMINE.getValue(), AdminModuleEnum.LOG.getValue(), AdminModuleEnum.BOOK.getValue(), AdminModuleEnum.CALENDAR.getValue());
            if (oaArray.contains(adminConfig.getName())) {
                if (!jsonObject.containsKey(AdminModuleEnum.OA.getValue())) {
                    jsonObject.put(AdminModuleEnum.OA.getValue(), new JSONObject());
                }
                JSONObject object = jsonObject.getJSONObject(AdminModuleEnum.OA.getValue());
                if (status == 0) {
                    object.remove(adminConfig.getName());
                } else {
                    if (!AdminModuleEnum.BOOK.getValue().equals(adminConfig.getName())) {
                        object.put(adminConfig.getName(), new JSONObject());
                    }
                }
                return;
            }
            if (Objects.equals(0, adminConfig.getStatus())) {
                JSONObject object = jsonObject.getJSONObject(AdminModuleEnum.MANAGE.getValue());
                //禁用模块时，删除管理模块下对应
                if (object != null) {
                    object.remove(adminConfig.getName());
                }
                jsonObject.remove(adminConfig.getName());

            } else {
                if (!jsonObject.containsKey(adminConfig.getName())) {
                    jsonObject.put(adminConfig.getName(), new JSONObject());
                }
            }
        });
        if (jsonObject.containsKey(HRM) && jsonObject.getJSONObject(HRM).isEmpty() && !UserUtil.isAdmin()) {
            List<AdminRole> roles = queryRoleByRoleTypeAndUserId(9);
//            Boolean isInHrm = hrmService.queryIsInHrm().getData();
//            //不在人资员工并且人资没有角色不展示人资导航
//            if (!isInHrm && CollUtil.isEmpty(roles)) {
//                jsonObject.remove("hrm");
//            }
        }
        //如果存在人资，暂时先移除绩效考核模块(升级）
        if (jsonObject.containsKey(HRM)) {
            JSONObject hrm = jsonObject.getJSONObject("hrm");
            if (hrm != null) {
                hrm.remove("appraisal");
                jsonObject.put("hrm", hrm);
            }
        }
        List<AdminConfig> adminConfigs = configService.queryConfigListByName(AdminRoleTypeEnum.HRM.getName(), AdminRoleTypeEnum.FINANCE.getName());
        Map<String, List<AdminConfig>> adminMap = adminConfigs.stream().collect(Collectors.groupingBy(AdminConfig::getName));
        if (!adminMap.containsKey(AdminRoleTypeEnum.HRM.getName()) || adminMap.get(AdminRoleTypeEnum.HRM.getName()).get(0).getStatus() == 0) {
            jsonObject.remove(AdminRoleTypeEnum.HRM.getName());
        }
        if (!adminMap.containsKey(AdminRoleTypeEnum.FINANCE.getName()) || adminMap.get(AdminRoleTypeEnum.FINANCE.getName()).get(0).getStatus() == 0) {
            jsonObject.remove(AdminRoleTypeEnum.FINANCE.getName());
        }
        redis.setex(cacheKey, 300, jsonObject);
        return jsonObject;
    }

    /**
     * 查询用户所属权限
     *
     * @param userIds ids
     */
    @Override
    public void authInvalidate(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return;
        }
        Object[] strings = userIds.stream().map(data -> AdminCacheKey.USER_AUTH_CACHE_KET + data).toArray(String[]::new);
        redis.del(strings);
    }

    /**
     * 通过用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return data
     */
    @Override
    public List<AdminRole> queryRoleListByUserId(Long userId) {
        QueryWrapper<AdminUserRole> wrapper = new QueryWrapper<>();
        wrapper.select("role_id");
        wrapper.eq("user_id", userId);
        List<Long> roleIdList = adminUserRoleService.list(wrapper).stream().map(AdminUserRole::getRoleId).collect(Collectors.toList());
        if (roleIdList.size() > 0) {
            return listByIds(roleIdList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<AdminRole> queryRoleListByUserId(List<Long> userIds) {
        QueryWrapper<AdminUserRole> wrapper = new QueryWrapper<>();
        wrapper.select("role_id");
        wrapper.in("user_id", userIds);
        List<Long> roleIdList = adminUserRoleService.list(wrapper).stream().map(AdminUserRole::getRoleId).collect(Collectors.toList());
        if (roleIdList.size() > 0) {
            roleIdList = roleIdList.stream().distinct().collect(Collectors.toList());
            return listByIds(roleIdList);
        }
        return new ArrayList<>();
    }

    /**
     * 根据类型查询角色
     *
     * @param roleTypeEnum type
     * @return data
     */
    @Override
    public List<AdminRole> getRoleByType(AdminRoleTypeEnum roleTypeEnum) {
        List<AdminRole> recordList = lambdaQuery().eq(AdminRole::getRoleType, roleTypeEnum.getType()).list();
        String realm = roleTypeEnum.getName();
        LambdaQueryWrapper<AdminMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AdminMenu::getMenuId);
        wrapper.eq(AdminMenu::getParentId, 0L);
        wrapper.eq(AdminMenu::getRealm, realm);
        AdminMenu adminMenu = adminMenuService.getOne(wrapper);
        if (adminMenu != null) {
            Long pid = adminMenuService.getOne(wrapper).getMenuId();
            if (recordList.size() != 0) {
                recordList.forEach(record -> {
                    Map<String, List<Long>> map = new HashMap<>();
                    List<Long> data = getBaseMapper().getRoleMenu(pid, record.getRoleId());
                    //判断是否财务模块
                    if (ObjectUtil.notEqual(realm, AdminRoleTypeEnum.FINANCE.getName())) {
                        List<Long> bi = getBaseMapper().getRoleMenu(2L, record.getRoleId());
                        map.put("bi", bi);
                    }
                    map.put("data", data);
                    record.setRules(map);
                });
            }
        }
        return recordList;
    }

    /**
     * 查询全部角色
     *
     * @return data
     */
    @Override
    public List<AdminRoleVO> getAllRoleList() {
        List<AdminRoleVO> records = new ArrayList<>();
        List<AdminConfig> adminConfigs = configService.queryConfigListByName(AdminRoleTypeEnum.HRM.getName(), AdminRoleTypeEnum.FINANCE.getName());
        Map<String, List<AdminConfig>> adminMap = adminConfigs.stream().collect(Collectors.groupingBy(AdminConfig::getName));

        for (AdminRoleTypeEnum typeEnum : AdminRoleTypeEnum.values()) {
            // 20220221 wwl 修改 0, 3, 5, 8, 10 => 0, 3, 5, 8， 10为进销存
            if (Arrays.asList(0, 3, 5, 8).contains(typeEnum.getType())) {
                continue;
            }
            if (!adminMap.containsKey(AdminRoleTypeEnum.HRM.getName()) || adminMap.get(AdminRoleTypeEnum.HRM.getName()).get(0).getStatus() == 0) {
                if (typeEnum.getType() == 9) {
                    continue;
                }
            }
            if (!adminMap.containsKey(AdminRoleTypeEnum.FINANCE.getName()) || adminMap.get(AdminRoleTypeEnum.FINANCE.getName()).get(0).getStatus() == 0) {
                if (typeEnum.getType() == 4) {
                    continue;
                }
            }
            AdminRoleVO record = new AdminRoleVO();
            record.setName(typeEnum.getDesc());
            record.setParentId(typeEnum.getType());
            List<AdminRole> recordList = getRoleByType(typeEnum);
            record.setList(recordList);
            records.add(record);
        }
        return records;
    }

    /**
     * 查询新增员工时的可查询角色
     *
     * @return 角色列表
     */
    @Override
    public List<AdminRoleVO> getRoleList() {
        List<AdminRoleVO> records = new ArrayList<>();
        boolean queryAllRole = adminRoleAuthService.isQueryAllRole();
        if (queryAllRole) {
            /* 可以查询全部直接走查询全部方法 */
            return getAllRoleList();
        }
        Set<Long> roleIds = adminRoleAuthService.queryAuthByUser();
        for (AdminRoleTypeEnum typeEnum : AdminRoleTypeEnum.values()) {
            AdminRoleVO record = new AdminRoleVO();
            record.setName(typeEnum.getDesc());
            record.setParentId(typeEnum.getType());
            List<AdminRole> recordList = getRoleByType(typeEnum);
            recordList.removeIf(adminRole -> !roleIds.contains(adminRole.getRoleId()));
            if (recordList.size() == 0) {
                continue;
            }
            record.setList(recordList);
            records.add(record);
        }
        return records;
    }

    @Override
    public Integer queryDataType(Long userId, Long menuId) {
        return getBaseMapper().queryDataType(userId, menuId);
    }

    /**
     * 查询下属用户
     *
     * @param userId 用户ID
     * @param menuId 菜单ID
     * @return 权限
     */
    @Override
    public Collection<Long> queryUserByAuth(Long userId, Long menuId) {
        if (UserUtil.isAdmin()) {
            List<AdminUser> adminUsers = adminUserService.lambdaQuery().select(AdminUser::getUserId).list();
            return adminUsers.stream().map(AdminUser::getUserId).collect(Collectors.toList());
        }
        Integer dataType = queryDataType(userId, menuId);
        if (dataType == null) {
            return Collections.singletonList(0L);
        }
        Set<Long> userSet = new HashSet<>();
        userSet.add(userId);
        switch (DataAuthEnum.parse(dataType)) {
            case MYSELF: {
                return userSet;
            }
            case MYSELF_AND_SUBORDINATE: {
                userSet.addAll(adminUserService.queryChildUserId(userId));
                break;
            }
            case THIS_DEPARTMENT: {
                AdminUser adminUser = adminUserService.getById(userId);
                userSet.addAll(adminUserService.queryUserByDeptIds(Collections.singletonList(adminUser.getDeptId())));
                break;
            }
            case THIS_DEPARTMENT_AND_SUBORDINATE: {
                AdminUser adminUser = adminUserService.getById(userId);
                List<Long> deptIds = adminDeptService.queryChildDept(adminUser.getDeptId());
                deptIds.add(adminUser.getDeptId());
                userSet.addAll(adminUserService.queryUserByDeptIds(deptIds));
                break;
            }
            case ALL: {
                List<AdminUser> adminUsers = adminUserService.lambdaQuery().select(AdminUser::getUserId).list();
                userSet.addAll(adminUsers.stream().map(AdminUser::getUserId).collect(Collectors.toSet()));
            }
            default:
                break;
        }
        return userSet;
    }

    /**
     * 保存角色
     *
     * @param adminRole role
     */
    @Override
    public void add(AdminRole adminRole) {
        Integer count = lambdaQuery().eq(AdminRole::getRoleName, adminRole.getRoleName()).eq(AdminRole::getRoleType, adminRole.getRoleType()).count();
        if (count > 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_ROLE_NAME_EXIST_ERROR);
        }
        if (adminRole.getRoleId() != null) {
            updateById(adminRole);
        } else {
            adminRole.setRoleId(null);
            adminRole.setStatus(1);
            adminRole.setRemark(null);
            save(adminRole);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long roleId) {
        AdminRole adminRole = getById(roleId);
        if (CP.equals(adminRole.getRemark())) {
            throw new CrmException(AdminCodeEnum.ADMIN_DEFAULT_ROLE_CANNOT_BE_DELETED);
        }
        removeById(roleId);
        JSONObject object = new JSONObject().fluentPut("role_id", roleId);
        adminUserRoleService.removeByMap(object);
        adminRoleMenuService.removeByMap(object);
    }

    /**
     * <AUTHOR>
     * 复制
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(Long roleId) {
        AdminRole adminRole = getById(roleId);
        LambdaQueryWrapper<AdminRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AdminRoleMenu::getMenuId);
        wrapper.eq(AdminRoleMenu::getRoleId, roleId);
        List<Long> menuIdsList = adminRoleMenuService.listObjs(wrapper, obj -> Convert.toLong(obj.toString()));
        String roleName = adminRole.getRoleName().trim();
        String pre = ReUtil.delFirst("[(]\\d+[)]$", roleName);
        List<AdminRole> adminRoleList;
        if (!ReUtil.contains(CANONICAL, roleName)) {
            adminRoleList = lambdaQuery().likeRight(AdminRole::getRoleName, pre).list();
        } else {
            adminRoleList = lambdaQuery().last(" role_name regexp '^[(]\\d+[)]$'").list();
        }
        StringBuilder numberSb = new StringBuilder();
        for (AdminRole dbAdminRole : adminRoleList) {
            String endCode = ReUtil.get("[(]\\d+[)]$", dbAdminRole.getRoleName(), 0);
            if (endCode != null) {
                numberSb.append(endCode);
            }
        }
        int i = 1;
        if (numberSb.length() == 0) {
            while (numberSb.toString().contains(LEFT_BRACKET + i + RIGHT_BRACKET)) {
                i++;
            }
        }
        adminRole.setRoleName(pre + "(" + i + ")");
        adminRole.setRoleId(null);
        adminRole.setRemark(null);
        save(adminRole);
        adminRoleMenuService.saveRoleMenu(adminRole.getRoleId(), menuIdsList);
        if (adminRole.getRoleType().equals(AdminRoleTypeEnum.MANAGER.getType()) && menuIdsList.contains(NUMBER)) {
            List<Long> authRoleIds = adminRoleAuthService.queryByRoleId(adminRole.getRoleId());
            adminRoleAuthService.saveRoleAuth(adminRole.getRoleId(), authRoleIds);
        }
    }

    /**
     * 用户关联角色
     *
     * @param userIds 用户列表
     * @param roleIds 角色列表
     */
    @Override
    public void relatedUser(List<Long> userIds, List<Long> roleIds) {
        if (CollUtil.isNotEmpty(roleIds)) {
            roleIds = roleIds.stream().distinct().collect(Collectors.toList());
            if (!adminRoleAuthService.isQueryAllRole()) {
                Set<Long> authByUser = adminRoleAuthService.queryAuthByUser();
                roleIds.retainAll(authByUser);
                if (roleIds.size() == 0) {
                    return;
                }
            }
        } else {
            throw new CrmException(AdminCodeEnum.ADMIN_ROLE_NOT_EXIST_ERROR);
        }
        List<AdminUserRole> adminUserRoleList = new ArrayList<>();
        for (Long userId : userIds) {
            for (Long roleId : roleIds) {
                Integer count = adminUserRoleService.lambdaQuery().eq(AdminUserRole::getRoleId, roleId).eq(AdminUserRole::getUserId, userId).count();
                if (count == 0) {
                    AdminUserRole userRole = new AdminUserRole();
                    userRole.setUserId(userId);
                    userRole.setRoleId(roleId);
                    adminUserRoleList.add(userRole);
                }
            }
        }
        adminUserRoleService.saveBatch(adminUserRoleList, Const.BATCH_SAVE_SIZE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accountUserRole(AdminUserRoleBO userRoleBO) {
        List<AdminUserRole> adminUserRoleList = new ArrayList<>();
        userRoleBO.getAuthSaveBOS().forEach(au -> {
            au.getRoleIdList().forEach(ro -> {
                AdminUserRole role = new AdminUserRole();
                role.setRoleId(ro);
                role.setUserId(au.getUserId());
                adminUserRoleList.add(role);
            });
            //先删除员工角色是财务类型根据员工id
            List<Long> ids = getBaseMapper().getUserRoleIds(AdminRoleTypeEnum.FINANCE.getType(), au.getUserId());
            adminUserRoleService.removeByIds(ids);
        });
        adminUserRoleService.saveBatch(adminUserRoleList, Const.BATCH_SAVE_SIZE);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void relatedDeptUser(List<Long> userIds, List<Long> deptIds, List<Long> roleIds) {
        Set<Long> userIdList = new HashSet<>();
        if (CollUtil.isNotEmpty(userIds)) {
            userIdList.addAll(userIds);
        }
        if (CollUtil.isNotEmpty(deptIds)) {
            List<Long> list = adminUserService.queryUserByDeptIds(deptIds);
            userIdList.addAll(list);
        }
        if (CollUtil.isEmpty(roleIds)) {
            throw new CrmException(AdminCodeEnum.ADMIN_ROLE_NOT_EXIST_ERROR);
        }
        roleIds = roleIds.stream().distinct().collect(Collectors.toList());
        List<AdminUserRole> adminUserRoleList = new ArrayList<>();
        for (Long userId : userIdList) {
            if (Objects.equals(userId, UserUtil.getSuperUser())) {
                continue;
            }
            adminUserRoleService.lambdaUpdate().eq(AdminUserRole::getUserId, userId).remove();
            for (Long roleId : roleIds) {
                AdminUserRole userRole = new AdminUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                adminUserRoleList.add(userRole);
            }
        }
        boolean queryAllRole = adminRoleAuthService.isQueryAllRole();
        if (!queryAllRole) {
            Set<Long> authByUser = adminRoleAuthService.queryAuthByUser();
            adminUserRoleList.removeIf(userRole -> !authByUser.contains(userRole.getRoleId()));
        }
        adminUserRoleService.saveBatch(adminUserRoleList, Const.BATCH_SAVE_SIZE);
    }

    private static final String ADMIN = "admin";

    /**
     * 取消用户关联角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    @Override
    public void unbindingUser(Long userId, Long roleId) {
        Integer count = adminUserRoleService.lambdaQuery().eq(AdminUserRole::getUserId, userId).ne(AdminUserRole::getRoleId, roleId).count();
        if (count == 0) {
            throw new CrmException(AdminCodeEnum.ADMIN_USER_NEEDS_AT_LEAST_ONE_ROLE);
        }
        AdminRole adminRole = getById(roleId);
        AdminUser adminUser = adminUserService.getById(userId);
        if (StrUtil.isNotEmpty(adminRole.getRemark()) && ADMIN.equals(adminRole.getRemark())) {
            if (adminUser.getUserId().equals(UserUtil.getSuperUser())) {
                throw new CrmException(AdminCodeEnum.ADMIN_USER_NEEDS_AT_ADMIN_ONE_ROLE);
            }
        }
        adminUserRoleService.lambdaUpdate().eq(AdminUserRole::getRoleId, roleId).eq(AdminUserRole::getUserId, userId).remove();
    }

    /**
     * 修改角色菜单关系
     *
     * @param adminRole adminrole
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRoleMenu(AdminRole adminRole) {
        updateById(adminRole);
        adminRoleMenuService.removeByMap(Collections.singletonMap("role_id", adminRole.getRoleId()));
        adminRoleMenuService.saveRoleMenu(adminRole.getRoleId(), adminRole.getMenuIds());
    }

    /**
     * 查询项目管理的角色
     *
     * @param label label
     * @return roleId
     */
    @Override
    public Long queryWorkRole(Integer label) {
        LambdaQueryWrapper<AdminRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AdminRole::getRoleId);
        if (label == null) {
            wrapper.eq(AdminRole::getRemark, "project");
        } else {
            wrapper.eq(AdminRole::getLabel, label);
        }
        AdminRole adminRole = getOne(wrapper);
        return adminRole != null ? adminRole.getRoleId() : 1;
    }

    /**
     * 保存项目管理角色
     *
     * @param object obj
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setWorkRole(JSONObject object) {
        Long roleId = object.getLong("roleId");
        String roleName = object.getString("roleName");
        String remark = object.getString("remark");
        JSONArray menuIds = object.getJSONArray("menuIds");
        AdminRole adminRole;
        if (roleId == null) {
            adminRole = new AdminRole();
            adminRole.setRoleName(roleName);
            adminRole.setRemark(remark);
            adminRole.setRoleType(6);
            save(adminRole);
        } else {
            adminRole = getById(roleId);
            adminRole.setRoleName(roleName);
            adminRole.setRemark(remark);
            adminRole.setRoleId(roleId);
            updateById(adminRole);
            adminRoleMenuService.removeByMap(new JSONObject().fluentPut("role_id", roleId));
        }
        adminRoleMenuService.saveRoleMenu(adminRole.getRoleId(), menuIds.toJavaList(Long.class));
    }

    /**
     * 删除项目管理角色
     *
     * @param roleId roleId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkRole(Long roleId) {
        removeById(roleId);
        adminRoleMenuService.removeByMap(new JSONObject().fluentPut("role_id", roleId));
        getBaseMapper().deleteWorkRole(queryWorkRole(3), roleId);
    }

    /**
     * 查询项目管理角色
     *
     * @return
     */
    @Override
    public List<AdminRole> queryProjectRoleList() {
        List<AdminRole> roleList = lambdaQuery().in(AdminRole::getRoleType, Arrays.asList(5, 6)).eq(AdminRole::getIsHidden, 1).list();
        roleList.forEach(record -> {
            LambdaQueryChainWrapper<AdminRoleMenu> chainWrapper = adminRoleMenuService.lambdaQuery().select(AdminRoleMenu::getMenuId).eq(AdminRoleMenu::getRoleId, record.getRoleId());
            List<Long> rules = chainWrapper.list().stream().map(AdminRoleMenu::getMenuId).collect(Collectors.toList());
            record.setMenuIds(rules);
        });
        return roleList;
    }


    private JSONObject createMenu(Set<AdminMenu> adminMenuList, Long parentId) {
        JSONObject jsonObject = new JSONObject();
        adminMenuList.forEach(adminMenu -> {
            if (Objects.equals(parentId, adminMenu.getParentId())) {
                if (Objects.equals(1, adminMenu.getMenuType())) {
                    JSONObject object = createMenu(adminMenuList, adminMenu.getMenuId());
                    if (!object.isEmpty()) {
                        jsonObject.put(adminMenu.getRealm(), object);
                    }
                } else {
                    jsonObject.put(adminMenu.getRealm(), Boolean.TRUE);
                }
            }
        });
        return jsonObject;
    }

    @Override
    public List<AdminRole> queryRoleList() {
        return lambdaQuery().eq(AdminRole::getStatus, 1).in(AdminRole::getRoleType, Arrays.asList(5, 6)).list();

    }

    @Override
    public AdminRole queryDefaultRole() {
        List<AdminRole> list = lambdaQuery().eq(AdminRole::getRoleName, "默认角色").eq(AdminRole::getRemark, "cp").list();
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<String> queryNoAuthMenu(Long userId) {
        if (UserUtil.isAdmin()) {
            return Collections.emptyList();
        }
        List<String> noAuthMenuUrls = new ArrayList<>();
        List<AdminMenu> adminMenus = adminMenuService.queryMenuList(userId);
        if (adminMenus.isEmpty()) {
            noAuthMenuUrls.add("/*/**");
            return noAuthMenuUrls;
        }
        List<Long> menuIdList = adminMenus.stream().map(AdminMenu::getMenuId).collect(Collectors.toList());
        LambdaQueryWrapper<AdminMenu> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //非空
        lambdaQueryWrapper.notIn(AdminMenu::getMenuId, menuIdList);
        List<AdminMenu> noAuthMenus = adminMenuService.list(lambdaQueryWrapper);
        noAuthMenus.removeIf(node -> StrUtil.isEmpty(node.getRealmUrl()));
        if (!noAuthMenus.isEmpty()) {
            noAuthMenuUrls.addAll(noAuthMenus.stream().map(AdminMenu::getRealmUrl).collect(Collectors.toList()));
            return noAuthMenuUrls;
        }
        return noAuthMenuUrls;
    }

    @Override
    public List<AdminRole> queryRoleByRoleTypeAndUserId(Integer type) {
        return getBaseMapper().queryRoleByRoleTypeAndUserId(type, UserUtil.getUserId());
    }

    /**
     * 跟进角色ID查询下属员工
     *
     * @param roleId 角色ID
     * @return userIds
     */
    @Override
    public List<Long> queryUserIdByRoleId(Long roleId) {
        LambdaQueryChainWrapper<AdminUserRole> queryChainWrapper = adminUserRoleService.lambdaQuery().select(AdminUserRole::getUserId).eq(AdminUserRole::getRoleId, roleId);
        return queryChainWrapper.list().stream().map(AdminUserRole::getUserId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delUserRole(Long userId) {
        //先删除员工角色是财务类型根据员工id
        List<Long> ids = getBaseMapper().getUserRoleIds(AdminRoleTypeEnum.FINANCE.getType(), userId);
        adminUserRoleService.removeByIds(ids);
    }
}
