package com.kakarote.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.admin.entity.BO.AdminMessageQueryBO;
import com.kakarote.admin.entity.PO.AdminMessage;
import com.kakarote.admin.entity.VO.AdminMessageVO;
import com.kakarote.admin.mapper.AdminMessageMapper;
import com.kakarote.admin.service.IAdminMessageService;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.admin.entity.AdminMessageBO;
import com.kakarote.core.feign.admin.entity.AdminMessageContentBO;
import com.kakarote.core.feign.admin.entity.AdminMessageEnum;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.feign.crm.service.CrmService;
import com.kakarote.core.feign.oa.OaService;
import com.kakarote.core.feign.oa.entity.ExamineVO;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 系统消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
public class AdminMessageServiceImpl extends BaseServiceImpl<AdminMessageMapper, AdminMessage> implements IAdminMessageService {


    @Autowired
    private Redis redis;

    @Autowired
    @Lazy
    private CrmService crmService;

    /**
     * 新增或修改消息
     *
     * @param message message
     */
    @Override
    public Long saveOrUpdateMessage(com.kakarote.core.feign.admin.entity.AdminMessage message) {
        AdminMessage adminMessage = BeanUtil.copyProperties(message, AdminMessage.class);
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        saveOrUpdate(adminMessage);
        return adminMessage.getMessageId();
    }

    /**
     * 查询消息列表
     *
     * @param adminMessageBO 搜索对象
     * @return data
     */
    @Override
    public BasePage<AdminMessage> queryList(AdminMessageQueryBO adminMessageBO) {
        adminMessageBO.setUserId(UserUtil.getUserId());
        BasePage<AdminMessage> adminMessageBasePage = getBaseMapper().queryList(adminMessageBO.parse(), adminMessageBO);
        if (Arrays.asList(AdminMessageEnum.CRM_CUSTOMER_IMPORT.getType(), AdminMessageEnum.CRM_CONTACTS_IMPORT.getType(), AdminMessageEnum.CRM_LEADS_IMPORT.getType(), AdminMessageEnum.CRM_PRODUCT_IMPORT.getType()).contains(adminMessageBO.getType())) {
            adminMessageBasePage.getList().forEach(data -> {
//                List<String> splitTrim = StrUtil.splitTrim(data.getContent(), Const.SEPARATOR);
//                data.setContent(splitTrim.size() > 0 ? splitTrim.get(0) : "0");
//                data.setContent(splitTrim.get(0)+","+splitTrim.get(1));
                if (StrUtil.isEmpty(data.getTitle())) {
                    data.setTitle("0");
                }
            });
        }
        return adminMessageBasePage;
    }

    /**
     * 查询未读消息数量
     *
     * @return data
     */
    @Override
    public AdminMessageVO queryUnreadCount() {
        return getBaseMapper().queryUnreadCount(UserUtil.getUserId());
    }

    /**
     * 新增消息
     *
     * @param adminMessageBO message
     */
    @Override
    public void addMessage(AdminMessageBO adminMessageBO) {
        if (adminMessageBO.getIds().size() == 0) {
            return;
        }
        AdminMessageEnum messageEnum = AdminMessageEnum.parse(adminMessageBO.getMessageType());
        switch (messageEnum) {
            case OA_TASK_ALLOCATION:
            case OA_TASK_JOIN:
            case OA_TASK_OVER:
                addOaTaskMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds(), adminMessageBO.getContent());
                break;
            case OA_LOG_SEND:
                addOaLogSendMessage(messageEnum, adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds(), adminMessageBO.getContent());
                break;
            case OA_LOG_FAVOUR:
            case OA_LOG_REPLY:
                addOaLogReplyMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getContent(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case OA_COMMENT_REPLY:
                addOaLogReplyMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getContent(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case OA_EXAMINE_REJECT:
            case OA_EXAMINE_PASS:
                addOaLogExamineMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getContent(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case OA_NOTICE_MESSAGE:
                addOaNoticeMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case OA_EVENT_MESSAGE:
                addOaEventMessage(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case CRM_CONTRACT_PASS:
            case CRM_CONTRACT_REJECT:
            case CRM_RECEIVABLES_PASS:
            case CRM_RECEIVABLES_REJECT:
            case CRM_INVOICE_PASS:
            case CRM_INVOICE_REJECT:
                addCrmExamineMessage(messageEnum, adminMessageBO.getContent(), adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case CRM_BUSINESS_USER:
            case CRM_CONTRACT_USER:
            case CRM_CUSTOMER_USER:
            case CRM_BUSINESS_TEAM_EXIT:
            case CRM_CUSTOMER_TEAM_EXIT:
            case CRM_CONTRACT_TEAM_EXIT:
            case CRM_BUSINESS_REMOVE_TEAM:
            case CRM_CUSTOMER_REMOVE_TEAM:
            case CRM_CONTRACT_REMOVE_TEAM:
                addCrmTeamMessage(messageEnum, adminMessageBO.getTypeId(), adminMessageBO.getTitle(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case OA_EXAMINE_NOTICE:
                addOaExamineNotice(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case CRM_CONTRACT_EXAMINE:
            case CRM_RECEIVABLES_EXAMINE:
            case CRM_INVOICE_EXAMINE:
                addCrmExamineNotice(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case KM_DOCUMENT_SHARE_OPEN:
            case KM_DOCUMENT_SHARE_CLOSE:
                addKmDocumentShareNotice(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case JXC_PURCHASE_EXAMINE:
            case JXC_RETREAT_EXAMINE:
            case JXC_SALE_EXAMINE:
            case JXC_SALE_RETURN_EXAMINE:
            case JXC_PAYMENT_EXAMINE:
            case JXC_COLLECTION_EXAMINE:
            case JXC_INVENTORY_EXAMINE:
            case JXC_ALLOCATION_EXAMINE:
            case HRM_EMPLOYEE_SALARY_EXAMINE:
                addJXCExamineNotice(messageEnum, adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            case JXC_PURCHASE_PASS:
            case JXC_RETREAT_REJECT:
            case JXC_RETREAT_PASS:
            case JXC_SALE_REJECT:
            case JXC_SALE_PASS:
            case JXC_SALE_RETURN_REJECT:
            case JXC_SALE_RETURN_PASS:
            case JXC_PAYMENT_REJECT:
            case JXC_PAYMENT_PASS:
            case JXC_COLLECTION_REJECT:
            case JXC_COLLECTION_PASS:
            case JXC_INVENTORY_REJECT:
            case JXC_INVENTORY_PASS:
            case JXC_ALLOCATION_REJECT:
            case JXC_ALLOCATION_PASS:
            case HRM_EMPLOYEE_SALARY_PASS:
            case HRM_EMPLOYEE_SALARY_REJECT:
                addJXCExamineMessage(messageEnum, adminMessageBO.getContent(), adminMessageBO.getTitle(), adminMessageBO.getTypeId(), adminMessageBO.getUserId(), adminMessageBO.getIds());
                break;
            default:
                break;
        }
    }

    /**
     * 添加任务消息
     *
     * <AUTHOR>
     */
    private void addOaTaskMessage(AdminMessageEnum messageEnum, String name, Long typeId, Long userId, List<Long> ids, String content) {
        List<AdminMessage> messageList = new ArrayList<>();
        ids.forEach(id -> {
            if (userId.equals(id)) {
                return;
            }
            AdminMessage adminMessage = new AdminMessage();
            adminMessage.setCreateTime(LocalDateTimeUtil.now());
            adminMessage.setCreateUser(userId);
            adminMessage.setType(messageEnum.getType());
            adminMessage.setLabel(messageEnum.getLabel());
            adminMessage.setTitle(name);
            adminMessage.setContent(content);
            adminMessage.setRecipientUser(id);
            adminMessage.setTypeId(typeId);
            messageList.add(adminMessage);
        });
        saveBatch(messageList);
    }

    /**
     * 添加日志发送消息
     *
     * <AUTHOR>
     */
    private void addOaLogSendMessage(AdminMessageEnum messageEnum, Long typeId, Long userId, List<Long> ids, String content) {
        List<AdminMessage> messageList = new ArrayList<>();
        ids.forEach(id -> {
            AdminMessage adminMessage = new AdminMessage();
            adminMessage.setCreateTime(LocalDateTimeUtil.now());
            adminMessage.setCreateUser(userId);
            adminMessage.setType(messageEnum.getType());
            adminMessage.setLabel(messageEnum.getLabel());
            adminMessage.setTitle(DateUtil.today());
            adminMessage.setContent(content);
            adminMessage.setRecipientUser(id);
            adminMessage.setTypeId(typeId);
            messageList.add(adminMessage);
        });
        saveBatch(messageList);
    }

    /**
     * 添加日志回复消息
     *
     * <AUTHOR>
     */
    private void addOaLogReplyMessage(AdminMessageEnum messageEnum, String title, String content, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setContent(content);
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    /**
     * 添加日志审批
     *
     * <AUTHOR>
     */
    private void addOaLogExamineMessage(AdminMessageEnum messageEnum, String title, String content, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setContent(setContent(messageEnum, typeId, content));
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }


    private void addKmDocumentShareNotice(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        List<AdminMessage> adminMessageList = new ArrayList<>();
        for (Long shareUserId : ids) {
            AdminMessage adminMessage = new AdminMessage();
            adminMessage.setCreateTime(LocalDateTimeUtil.now());
            adminMessage.setCreateUser(userId);
            adminMessage.setType(messageEnum.getType());
            adminMessage.setLabel(messageEnum.getLabel());
            adminMessage.setTitle(title);
            adminMessage.setRecipientUser(shareUserId);
            adminMessage.setTypeId(typeId);
            adminMessageList.add(adminMessage);
        }
        saveBatch(adminMessageList);
    }

    /**
     * 添加oa待审批提醒
     *
     * <AUTHOR>
     */
    private void addOaExamineNotice(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setTitle(title);
        adminMessage.setContent(setContent(messageEnum, typeId, null));
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    /**
     * 添加OA公告消息
     *
     * <AUTHOR>
     */
    private void addOaNoticeMessage(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        List<AdminMessage> messageList = new ArrayList<>();
        ids.forEach(id -> {
            AdminMessage adminMessage = new AdminMessage();
            adminMessage.setCreateTime(LocalDateTimeUtil.now());
            adminMessage.setCreateUser(userId);
            adminMessage.setType(messageEnum.getType());
            adminMessage.setLabel(messageEnum.getLabel());
            adminMessage.setTitle(title);
            adminMessage.setRecipientUser(id);
            adminMessage.setTypeId(typeId);
            messageList.add(adminMessage);
        });
        saveBatch(messageList);
    }

    /**
     * 添加OA日程消息
     *
     * <AUTHOR>
     */
    private void addOaEventMessage(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        List<AdminMessage> messageList = new ArrayList<>();
        ids.forEach(id -> {
            AdminMessage adminMessage = new AdminMessage();
            adminMessage.setCreateTime(LocalDateTimeUtil.now());
            adminMessage.setCreateUser(userId);
            adminMessage.setType(messageEnum.getType());
            adminMessage.setLabel(messageEnum.getLabel());
            adminMessage.setTitle(title);
            adminMessage.setRecipientUser(id);
            adminMessage.setTypeId(typeId);
            messageList.add(adminMessage);
        });
        saveBatch(messageList);
    }

    /**
     * 添加CRM审批消息
     *
     * <AUTHOR>
     */
    private void addCrmExamineMessage(AdminMessageEnum messageEnum, String content, String title, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setContent(setContent(messageEnum, typeId, content));
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    private void addCrmExamineNotice(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setContent(setContent(messageEnum, typeId, null));
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    /**
     * 团队成员提醒
     */
    private void addCrmTeamMessage(AdminMessageEnum messageEnum, Long typeId, String title, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        adminMessage.setTitle(title);
        save(adminMessage);
    }


    /**
     * 添加JXC审批消息
     */
    private void addJXCExamineMessage(AdminMessageEnum messageEnum, String content, String title, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setContent(content);
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    private void addJXCExamineNotice(AdminMessageEnum messageEnum, String title, Long typeId, Long userId, List<Long> ids) {
        AdminMessage adminMessage = new AdminMessage();
        adminMessage.setCreateTime(LocalDateTimeUtil.now());
        adminMessage.setCreateUser(userId);
        adminMessage.setType(messageEnum.getType());
        adminMessage.setLabel(messageEnum.getLabel());
        adminMessage.setTitle(title);
        adminMessage.setRecipientUser(ids.get(0));
        adminMessage.setTypeId(typeId);
        save(adminMessage);
    }

    /**
     * 加工消息列表预览详情数据
     *
     * @param messageEnum 消息类型
     * @param typeId      消息来源数据ID
     * @param refuse      审批拒绝使用：拒绝原因
     * @return
     */
    private String setContent(AdminMessageEnum messageEnum, Long typeId, String refuse) {
        String content = null;
        AdminMessageContentBO adminMessageContentBO = new AdminMessageContentBO();
        adminMessageContentBO.setRemarks(refuse);
        switch (messageEnum) {
            case OA_EXAMINE_NOTICE:
            case OA_EXAMINE_REJECT:
            case OA_EXAMINE_PASS:
                ExamineVO data = ApplicationContextHolder.getBean(OaService.class).getOaExamineInfo(typeId.toString()).getData();
                adminMessageContentBO.setMoney(data.getMoney());
                adminMessageContentBO.setStartTime(LocalDateTimeUtil.formatNormal(data.getStartTime()));
                adminMessageContentBO.setEndTime(LocalDateTimeUtil.formatNormal(data.getEndTime()));
                adminMessageContentBO.setContent(data.getContent());
                adminMessageContentBO.setRemarks(refuse);
                adminMessageContentBO.setDuration(data.getDuration());
                adminMessageContentBO.setType(data.getType());
                adminMessageContentBO.setExamineName(data.getCategoryTitle());
                break;
            case CRM_CONTRACT_EXAMINE:
            case CRM_CONTRACT_REJECT:
            case CRM_CONTRACT_PASS:
                Map<String, Object> contract;
                List<JSONObject> mapList = null;
                if (redis.exists(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId)) {
                    contract = redis.get(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId);
                    String products = "products";
                    if (!ObjectUtil.isEmpty(contract.get(products))) {
                        mapList = (List<JSONObject>) contract.get("products");
                    }
                } else {
                    contract = getBaseMapper().queryContract(typeId);
                    mapList = getBaseMapper().queryContractProduct(typeId);
                }
                adminMessageContentBO.setMoney((BigDecimal) contract.get("money"));

                List<SimpleCrmEntity> contractCustomer = crmService.queryCustomerInfo(Collections.singletonList(contract.get("customerId").toString())).getData();
                if (!CollectionUtil.isEmpty(contractCustomer)) {
                    adminMessageContentBO.setCustomer(new JSONObject().fluentPut("id", contractCustomer.get(0).getId().toString()).fluentPut("name", contractCustomer.get(0).getName()));
                }

                if (!CollectionUtil.isEmpty(mapList)) {
                    adminMessageContentBO.setProducts(mapList);
                }

                break;
            case CRM_INVOICE_EXAMINE:
            case CRM_INVOICE_PASS:
            case CRM_INVOICE_REJECT:
                Map<String, Object> invoice;
                if (redis.exists(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId)) {
                    invoice = redis.get(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId);
                } else {
                    invoice = getBaseMapper().queryInvoice(typeId);
                }
                adminMessageContentBO.setMoney((BigDecimal) invoice.get("invoiceMoney"));
                adminMessageContentBO.setCreateTime(invoice.get("invoiceDate") instanceof Date ? DateUtil.formatDateTime((Date) invoice.get("invoiceDate")) : LocalDateTimeUtil.formatNormal((LocalDateTime) invoice.get("invoiceDate")));

                List<SimpleCrmEntity> invoiceCustomer = crmService.queryCustomerInfo(Collections.singletonList(invoice.get("customerId").toString())).getData();
                if (!CollectionUtil.isEmpty(invoiceCustomer)) {
                    adminMessageContentBO.setCustomer(new JSONObject().fluentPut("id", invoiceCustomer.get(0).getId().toString()).fluentPut("name", invoiceCustomer.get(0).getName()));
                }

                List<SimpleCrmEntity> invoiceContract = crmService.queryContractInfo(Collections.singletonList(invoice.get("contractId").toString())).getData();
                if (!CollectionUtil.isEmpty(invoiceCustomer)) {
                    adminMessageContentBO.setContract(new JSONObject().fluentPut("id", invoiceContract.get(0).getId().toString()).fluentPut("name", invoiceContract.get(0).getName()));
                }

                break;
            case CRM_RECEIVABLES_EXAMINE:
            case CRM_RECEIVABLES_PASS:
            case CRM_RECEIVABLES_REJECT:
                Map<String, Object> receivables;
                if (redis.exists(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId)) {
                    receivables = redis.get(CrmCacheKey.CRM_ADMIN_MESSAGE + typeId);
                } else {
                    receivables = getBaseMapper().queryReceivables(typeId);
                }
                if (receivables == null || receivables.isEmpty()) {
                    return "";
                }
                adminMessageContentBO.setMoney((BigDecimal) receivables.get("money"));
                List<SimpleCrmEntity> receivablesCustomer = crmService.queryCustomerInfo(Collections.singletonList(receivables.get("customerId").toString())).getData();
                if (!CollectionUtil.isEmpty(receivablesCustomer)) {
                    adminMessageContentBO.setCustomer(new JSONObject().fluentPut("id", receivablesCustomer.get(0).getId().toString()).fluentPut("name", receivablesCustomer.get(0).getName()));
                }

                List<SimpleCrmEntity> receivablesContract = crmService.queryContractInfo(Collections.singletonList(receivables.get("contractId").toString())).getData();
                if (!CollectionUtil.isEmpty(receivablesContract)) {
                    adminMessageContentBO.setContract(new JSONObject().fluentPut("id", receivablesContract.get(0).getId().toString()).fluentPut("name", receivablesContract.get(0).getName()));
                }

                break;
            default:
                break;
        }
        content = JSONObject.toJSONString(adminMessageContentBO);
        return content;
    }

    @Override
    public void deleteEventMessage(Integer eventId) {
        lambdaUpdate().eq(AdminMessage::getLabel, AdminMessageEnum.OA_EVENT_MESSAGE.getLabel())
                .eq(AdminMessage::getType, AdminMessageEnum.OA_EVENT_MESSAGE.getType())
                .apply("create_time > now()").eq(AdminMessage::getTypeId, eventId).remove();
    }

    @Override
    public void deleteById(Long messageId) {
        removeById(messageId);
    }

    @Override
    public void deleteByLabel(Integer label) {
        lambdaUpdate().eq(AdminMessage::getLabel, label).remove();
    }
}
