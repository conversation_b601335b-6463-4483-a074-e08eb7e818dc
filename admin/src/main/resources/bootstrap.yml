server:
  port: 0
spring:
  profiles:
    active: core,dev
  application:
    name: admin
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 113.45.141.212:8848
        file-extension: yaml
        prefix: admin
      discovery:
        enabled: true
        server-addr: 113.45.141.212:8848
    sentinel:
      filter:
        enabled: true
      transport:
        dashboard: 113.45.141.212:8079
      datasource:
        ds1:
          nacos:
            server-addr: 113.45.141.212:8848
            dataId: admin-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
seata:
  enabled: true
  application-id: admin
  tx-service-group: admin_tx_group
  config:
    type: nacos
    nacos:
      namespace:
      serverAddr: 113.45.141.212:8848
      group: SEATA_GROUP
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 113.45.141.212:8848
      namespace:

rocketmq:
  name-server: 192.168.1.126:9876
  producer:
    group: springboot-mq-consumer
